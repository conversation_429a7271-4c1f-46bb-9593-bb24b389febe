// ============================================================================
// 1.27版本自适应UI样式定义 - 解决全屏拉伸问题
// ============================================================================

// 自适应容器样式
Frame "BACKDROP" "AdaptiveContainer" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\blank-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.008,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
    BackdropEdgeFile  "",
}

// 顶部资源面板样式
Frame "BACKDROP" "TopPanel" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.012,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.006 0.006 0.006 0.006,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-panel-border.blp",
    
    // 添加渐变效果
    BackdropBlendAll,
}

// 底部命令面板样式
Frame "BACKDROP" "BottomPanel" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.016,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.008 0.008 0.008 0.008,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-panel-border.blp",
}

// 命令按钮样式
Frame "BACKDROP" "CommandButton" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-button-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.008,
    BackdropBackgroundSize  0.064,
    BackdropBackgroundInsets 0.002 0.002 0.002 0.002,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-button-border.blp",
    
    // 悬停高光效果
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
    ControlMouseOverHighlight "CommandButtonHighlight",
}

// 命令按钮高光样式
Frame "BACKDROP" "CommandButtonHighlight" {
    BackdropBackground "UI\\Widgets\\EscMenu\\Human\\quest-button-highlight.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.008,
    BackdropBlendAll,
}

// 小地图容器样式
Frame "BACKDROP" "MiniMapFrame" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.012,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.006 0.006 0.006 0.006,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-panel-border.blp",
}

// 资源文本样式
Frame "TEXT" "ResourceText" {
    FontJustificationH JUSTIFYLEFT,
    FontJustificationV JUSTIFYMIDDLE,
    FontFlags "FIXEDSIZE",
    FontColor 1.0 1.0 1.0 1.0,
    FontShadowColor 0.0 0.0 0.0 1.0,
    FontShadowOffset 0.001 -0.001,
}

// 小地图标题样式
Frame "TEXT" "MapTitle" {
    FontJustificationH JUSTIFYCENTER,
    FontJustificationV JUSTIFYMIDDLE,
    FontFlags "FIXEDSIZE",
    FontColor 1.0 0.843 0.0 1.0,  // 金色
    FontShadowColor 0.0 0.0 0.0 1.0,
    FontShadowOffset 0.001 -0.001,
}

// ============================================================================
// 宽屏适配专用样式
// ============================================================================

// 左右黑边样式（用于超宽屏）
Frame "BACKDROP" "LeftBlackBar" {
    BackdropBackground "UI\\Widgets\\EscMenu\\Human\\blank-background.blp",
    BackdropCornerFlags "",
    BackdropBackgroundSize 0.256,
}

Frame "BACKDROP" "RightBlackBar" {
    BackdropBackground "UI\\Widgets\\EscMenu\\Human\\blank-background.blp",
    BackdropCornerFlags "",
    BackdropBackgroundSize 0.256,
}

// ============================================================================
// 响应式按钮样式（不同尺寸）
// ============================================================================

// 大按钮样式
Frame "BACKDROP" "LargeButton" INHERITS "CommandButton" {
    BackdropCornerSize  0.012,
    BackdropBackgroundSize  0.096,
    BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
}

// 小按钮样式
Frame "BACKDROP" "SmallButton" INHERITS "CommandButton" {
    BackdropCornerSize  0.006,
    BackdropBackgroundSize  0.048,
    BackdropBackgroundInsets 0.001 0.001 0.001 0.001,
}

// ============================================================================
// 不同种族风格适配
// ============================================================================

// 兽族风格面板
Frame "BACKDROP" "OrcPanel" INHERITS "TopPanel" {
    BackdropBackground  "UI\\Widgets\\EscMenu\\Orc\\orc-panel-background.blp",
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Orc\\orc-panel-border.blp",
}

// 不死族风格面板
Frame "BACKDROP" "UndeadPanel" INHERITS "TopPanel" {
    BackdropBackground  "UI\\Widgets\\EscMenu\\Undead\\undead-panel-background.blp",
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Undead\\undead-panel-border.blp",
}

// 暗夜精灵风格面板
Frame "BACKDROP" "NightElfPanel" INHERITS "TopPanel" {
    BackdropBackground  "UI\\Widgets\\EscMenu\\NightElf\\nightelf-panel-background.blp",
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\NightElf\\nightelf-panel-border.blp",
}

// ============================================================================
// 动画和过渡效果样式
// ============================================================================

// 淡入淡出动画样式
Frame "BACKDROP" "FadePanel" INHERITS "TopPanel" {
    // 这里可以添加动画相关的样式定义
    // 1.27版本的动画支持有限，主要通过JASS控制
}

// ============================================================================
// 调试和开发辅助样式
// ============================================================================

// 调试边框样式
Frame "BACKDROP" "DebugBorder" {
    BackdropBackground "",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.004,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-button-border.blp",
    BackdropBackgroundInsets 0.0 0.0 0.0 0.0,
}

// 网格辅助线样式
Frame "BACKDROP" "GridHelper" {
    BackdropBackground "UI\\Widgets\\EscMenu\\Human\\blank-background.blp",
    BackdropCornerFlags "",
    BackdropBackgroundSize 0.001,
}
