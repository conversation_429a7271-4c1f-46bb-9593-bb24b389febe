import { createToken, Lexer, TokenType } from 'chevrotain';

// JASS关键字
export const Function = createToken({ name: 'Function', pattern: /function/ });
export const EndFunction = createToken({ name: 'EndFunction', pattern: /endfunction/ });
export const Takes = createToken({ name: 'Takes', pattern: /takes/ });
export const Returns = createToken({ name: 'Returns', pattern: /returns/ });
export const Nothing = createToken({ name: 'Nothing', pattern: /nothing/ });
export const Local = createToken({ name: 'Local', pattern: /local/ });
export const Set = createToken({ name: 'Set', pattern: /set/ });
export const Call = createToken({ name: 'Call', pattern: /call/ });
export const Return = createToken({ name: 'Return', pattern: /return/ });
export const If = createToken({ name: 'If', pattern: /if/ });
export const Then = createToken({ name: 'Then', pattern: /then/ });
export const Else = createToken({ name: 'Else', pattern: /else/ });
export const ElseIf = createToken({ name: 'ElseIf', pattern: /elseif/ });
export const EndIf = createToken({ name: 'EndIf', pattern: /endif/ });
export const Loop = createToken({ name: 'Loop', pattern: /loop/ });
export const EndLoop = createToken({ name: 'EndLoop', pattern: /endloop/ });
export const ExitWhen = createToken({ name: 'ExitWhen', pattern: /exitwhen/ });
export const Globals = createToken({ name: 'Globals', pattern: /globals/ });
export const EndGlobals = createToken({ name: 'EndGlobals', pattern: /endglobals/ });
export const Constant = createToken({ name: 'Constant', pattern: /constant/ });
export const Array = createToken({ name: 'Array', pattern: /array/ });
export const Type = createToken({ name: 'Type', pattern: /type/ });
export const Extends = createToken({ name: 'Extends', pattern: /extends/ });
export const Native = createToken({ name: 'Native', pattern: /native/ });
export const Debug = createToken({ name: 'Debug', pattern: /debug/ });

// 逻辑运算符
export const And = createToken({ name: 'And', pattern: /and/ });
export const Or = createToken({ name: 'Or', pattern: /or/ });
export const Not = createToken({ name: 'Not', pattern: /not/ });

// JASS基本类型
export const IntegerType = createToken({ name: 'IntegerType', pattern: /integer/ });
export const RealType = createToken({ name: 'RealType', pattern: /real/ });
export const BooleanType = createToken({ name: 'BooleanType', pattern: /boolean/ });
export const StringType = createToken({ name: 'StringType', pattern: /string/ });
export const HandleType = createToken({ name: 'HandleType', pattern: /handle/ });
export const CodeType = createToken({ name: 'CodeType', pattern: /code/ });

// 魔兽3特定类型
export const UnitType = createToken({ name: 'UnitType', pattern: /unit/ });
export const PlayerType = createToken({ name: 'PlayerType', pattern: /player/ });
export const LocationType = createToken({ name: 'LocationType', pattern: /location/ });
export const RectType = createToken({ name: 'RectType', pattern: /rect/ });
export const RegionType = createToken({ name: 'RegionType', pattern: /region/ });
export const TimerType = createToken({ name: 'TimerType', pattern: /timer/ });
export const TriggerType = createToken({ name: 'TriggerType', pattern: /trigger/ });
export const GroupType = createToken({ name: 'GroupType', pattern: /group/ });
export const ForceType = createToken({ name: 'ForceType', pattern: /force/ });
export const EffectType = createToken({ name: 'EffectType', pattern: /effect/ });
export const SoundType = createToken({ name: 'SoundType', pattern: /sound/ });
export const FrameHandleType = createToken({ name: 'FrameHandleType', pattern: /framehandle/ });

// 字面量
export const True = createToken({ name: 'True', pattern: /true/ });
export const False = createToken({ name: 'False', pattern: /false/ });
export const Null = createToken({ name: 'Null', pattern: /null/ });

// 标识符和字面量
export const Identifier = createToken({ 
    name: 'Identifier', 
    pattern: /[a-zA-Z_][a-zA-Z0-9_]*/ 
});

export const IntegerLiteral = createToken({ 
    name: 'IntegerLiteral', 
    pattern: /0[xX][0-9a-fA-F]+|0[0-7]+|\d+/ 
});

export const RealLiteral = createToken({ 
    name: 'RealLiteral', 
    pattern: /\d+\.\d+/ 
});

export const StringLiteral = createToken({ 
    name: 'StringLiteral', 
    pattern: /"([^"\\]|\\.)*"/ 
});

export const RawCode = createToken({ 
    name: 'RawCode', 
    pattern: /'[^']{4}'/ 
});

// 运算符
export const Plus = createToken({ name: 'Plus', pattern: /\+/ });
export const Minus = createToken({ name: 'Minus', pattern: /-/ });
export const Multiply = createToken({ name: 'Multiply', pattern: /\*/ });
export const Divide = createToken({ name: 'Divide', pattern: /\// });
export const Modulo = createToken({ name: 'Modulo', pattern: /%/ });

export const Equal = createToken({ name: 'Equal', pattern: /==/ });
export const NotEqual = createToken({ name: 'NotEqual', pattern: /!=/ });
export const LessThan = createToken({ name: 'LessThan', pattern: /</ });
export const LessEqual = createToken({ name: 'LessEqual', pattern: /<=/ });
export const GreaterThan = createToken({ name: 'GreaterThan', pattern: />/ });
export const GreaterEqual = createToken({ name: 'GreaterEqual', pattern: />=/ });

export const Assign = createToken({ name: 'Assign', pattern: /=/ });

// 分隔符
export const LeftParen = createToken({ name: 'LeftParen', pattern: /\(/ });
export const RightParen = createToken({ name: 'RightParen', pattern: /\)/ });
export const LeftBracket = createToken({ name: 'LeftBracket', pattern: /\[/ });
export const RightBracket = createToken({ name: 'RightBracket', pattern: /\]/ });
export const Comma = createToken({ name: 'Comma', pattern: /,/ });
export const Dot = createToken({ name: 'Dot', pattern: /\./ });

// 注释和空白
export const LineComment = createToken({ 
    name: 'LineComment', 
    pattern: /\/\/.*/, 
    group: Lexer.SKIPPED 
});

export const BlockComment = createToken({ 
    name: 'BlockComment', 
    pattern: /\/\*[\s\S]*?\*\//, 
    group: Lexer.SKIPPED 
});

export const WhiteSpace = createToken({ 
    name: 'WhiteSpace', 
    pattern: /\s+/, 
    group: Lexer.SKIPPED 
});

export const NewLine = createToken({ 
    name: 'NewLine', 
    pattern: /\r?\n/, 
    group: Lexer.SKIPPED 
});

// 所有token的数组（顺序很重要）
export const allTokens: TokenType[] = [
    // 注释和空白必须在前面
    LineComment,
    BlockComment,
    WhiteSpace,
    NewLine,
    
    // 关键字（必须在标识符之前）
    Function, EndFunction, Takes, Returns, Nothing,
    Local, Set, Call, Return,
    If, Then, Else, ElseIf, EndIf,
    Loop, EndLoop, ExitWhen,
    Globals, EndGlobals, Constant, Array,
    Type, Extends, Native, Debug,
    And, Or, Not,
    
    // 类型关键字
    IntegerType, RealType, BooleanType, StringType, HandleType, CodeType,
    UnitType, PlayerType, LocationType, RectType, RegionType, TimerType,
    TriggerType, GroupType, ForceType, EffectType, SoundType, FrameHandleType,
    
    // 字面量关键字
    True, False, Null,
    
    // 运算符（多字符的在前面）
    Equal, NotEqual, LessEqual, GreaterEqual,
    Plus, Minus, Multiply, Divide, Modulo,
    LessThan, GreaterThan, Assign,
    
    // 分隔符
    LeftParen, RightParen, LeftBracket, RightBracket,
    Comma, Dot,
    
    // 字面量
    RealLiteral, IntegerLiteral, StringLiteral, RawCode,
    
    // 标识符（必须在最后）
    Identifier
];

// 创建词法分析器
export const JassLexer = new Lexer(allTokens);
