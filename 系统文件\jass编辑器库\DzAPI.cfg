DzAPI_Map_GetMapLevel
DzAPI_Map_SaveServerValue
DzAPI_Map_GetServerValue
DzAPI_Map_StoreInteger
DzAPI_Map_GetStoredInteger
DzAPI_Map_StoreReal
DzAPI_Map_GetStoredReal
DzAPI_Map_StoreBoolean
DzAPI_Map_GetStoredBoolean
DzAPI_Map_StoreString
DzAPI_Map_GetStoredString
DzAPI_Map_FlushStoredMission
DzAPI_Map_GetLadderLevel
DzAPI_Map_IsRedVIP
DzAPI_Map_IsBlueVIP
DzAPI_Map_GetLadderRank
DzAPI_Map_GetMapLevelRank
DzAPI_Map_GetServerValueErrorCode
DzAPI_Map_GetGuildName
DzAPI_Map_GetGuildRole
GetPlayerServerValueSuccess
DzAPI_Map_IsRPGLobby
DzAPI_Map_GetStoredUnitType
DzAPI_Map_GetStoredAbilityId
DzAPI_Map_GetGameStartTime
DzAPI_Map_HasMallItem
DzAPI_Map_GetMapConfig
DzAPI_Map_SavePublicArchive
DzAPI_Map_GetPublicArchive
DzAPI_Map_UseConsumablesItem
DzAPI_Map_OrpgTrigger
DzAPI_Map_GetServerArchiveDrop
DzAPI_Map_GetServerArchiveEquip
DzAPI_Map_Global_StoreString
DzAPI_Map_Global_GetStoreString
DzAPI_Map_Global_ChangeMsg
DzAPI_Map_ServerArchive
DzAPI_Map_SaveServerArchive
DzAPI_Map_IsRPGQuickMatch
DzAPI_Map_GetMallItemCount
DzAPI_Map_ConsumeMallItem
DzAPI_Map_EnablePlatformSettings
DzAPI_Map_IsBuyReforged
DzAPI_Map_IsPlatformVIP
DzAPI_Map_PlayedGames
DzAPI_Map_CommentCount
DzAPI_Map_FriendCount
DzAPI_Map_IsConnoisseur
DzAPI_Map_IsAuthor
DzAPI_Map_IsBattleNetAccount
DzAPI_Map_CommentTotalCount
DzAPI_Map_Statistics
DzAPI_Map_Returns
DzAPI_Map_ContinuousCount
DzAPI_Map_IsPlayer
DzAPI_Map_MapsTotalPlayed
DzAPI_Map_MapsLevel
DzAPI_Map_MapsConsumeGold
DzAPI_Map_MapsConsumeLumber
DzAPI_Map_MapsConsumeLv1
DzAPI_Map_MapsConsumeLv2
DzAPI_Map_MapsConsumeLv3
DzAPI_Map_MapsConsumeLv4
DzAPI_Map_GetForumData
DzAPI_Map_OpenMall
DzAPI_Map_GameResult_CommitData
DzAPI_Map_GameResult_CommitTitle
DzAPI_Map_GameResult_CommitPlayerRank
DzAPI_Map_GameResult_CommitGameMode
DzAPI_Map_GameResult_CommitGameResult
DzAPI_Map_GameResult_CommitGameResultNoEnd
DzAPI_Map_GetLotteryUsedCount
DzAPI_Map_GetSinceLastPlayedSeconds
DzAPI_Map_CancelQuickBuy
DzAPI_Map_QuickBuy
DzAPI_Map_CustomRankCount
DzAPI_Map_CustomRankPlayerName
DzAPI_Map_CustomRankValue
