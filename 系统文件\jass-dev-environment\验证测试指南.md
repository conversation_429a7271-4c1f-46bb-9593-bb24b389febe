# JASS扩展验证测试指南

## 如何确认扩展工作正常

### 第一步：打开测试文件
1. 在VS Code中打开 `test-validation.j` 文件
2. 观察是否有红色波浪线标记错误

### 第二步：检查错误检测
扩展应该检测到以下错误类型：

#### 语法错误（应该有红色波浪线）：
- 第5行：`unknowntype` - 未知类型
- 第8行：重复声明变量 `test_var`
- 第13行：缺少 `endfunction`
- 第16-17行：返回类型不匹配
- 第21行：调用未定义函数
- 第25行：参数数量不匹配
- 第29行：缺少 `takes` 关键字
- 第34行：使用未声明变量
- 第39行：类型不匹配赋值
- 第44行：缺少 `endloop`
- 第53行：缺少 `endif`

#### 正确代码（不应该有错误）：
- 第48-52行：`CorrectFunction` 应该没有任何错误标记

### 第三步：测试实时检测
1. 在正确的函数中故意输入错误，比如：
   ```jass
   function TestReal takes nothing returns nothing
       call UndefinedFunc()  // 应该立即显示红色波浪线
   endfunction
   ```

2. 修正错误，红色波浪线应该消失

### 第四步：对比地图编辑器
1. 将同样的错误代码复制到YDWE地图编辑器
2. 尝试保存地图
3. 对比两者检测到的错误是否一致

### 第五步：测试你的实际代码
1. 打开你的 `war3map.j` 文件
2. 故意制造一些错误
3. 看扩展是否能检测到

## 预期结果
- ✅ 错误代码有红色波浪线
- ✅ 正确代码没有错误标记  
- ✅ 实时错误检测
- ✅ 错误信息准确
- ✅ 与地图编辑器检测结果一致

## 如果发现问题
如果扩展没有检测到某些错误，或者误报了正确的代码，请告诉我具体情况，我会帮你调整。
