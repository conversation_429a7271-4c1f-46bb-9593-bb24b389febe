/**
 * JASS项目分析器 - 严格基于用户实际项目文件
 * 作者: CODEX
 * 原则: 只从用户的实际JASS文件中学习，不使用任何预设知识
 */

const fs = require('fs');
const path = require('path');

class JassProjectAnalyzer {
    constructor() {
        // 从用户项目中学习到的所有定义
        this.userFunctions = new Map();      // 用户定义的函数
        this.userGlobals = new Map();        // 用户定义的全局变量
        this.userTypes = new Set();          // 用户使用的类型
        this.userConstants = new Map();      // 用户定义的常量
        
        // 分析状态
        this.isAnalyzed = false;
        this.analysisErrors = [];
        
        // 项目路径
        this.projectRoot = null;
    }
    
    /**
     * 设置项目根目录并开始分析
     */
    analyzeProject(projectRoot) {
        this.projectRoot = projectRoot;
        this.analysisErrors = [];
        
        console.log('开始分析用户JASS项目...');
        
        try {
            // 第一步：分析主地图文件
            this.analyzeMainMapFile();
            
            // 第二步：分析所有JASS库文件
            this.analyzeJassLibraries();
            
            // 第三步：验证分析结果
            this.validateAnalysisResults();
            
            this.isAnalyzed = true;
            console.log('项目分析完成');
            console.log(`发现函数: ${this.userFunctions.size}`);
            console.log(`发现全局变量: ${this.userGlobals.size}`);
            console.log(`发现类型: ${this.userTypes.size}`);
            
        } catch (error) {
            this.analysisErrors.push(`项目分析失败: ${error.message}`);
            console.error('项目分析失败:', error);
        }
    }
    
    /**
     * 分析主地图文件 war3map.j
     */
    analyzeMainMapFile() {
        const mapFilePath = path.join(this.projectRoot, 'hayx', 'map', 'war3map.j');
        
        if (!fs.existsSync(mapFilePath)) {
            throw new Error(`找不到主地图文件: ${mapFilePath}`);
        }
        
        console.log('分析主地图文件...');
        this.analyzeFile(mapFilePath);
    }
    
    /**
     * 分析JASS库文件夹
     */
    analyzeJassLibraries() {
        const jassDir = path.join(this.projectRoot, 'hayx', 'jass');
        
        if (!fs.existsSync(jassDir)) {
            console.log('警告: 找不到JASS库文件夹');
            return;
        }
        
        console.log('分析JASS库文件...');
        this.analyzeDirectory(jassDir);
    }
    
    /**
     * 递归分析目录中的所有.j文件
     */
    analyzeDirectory(dirPath) {
        try {
            const files = fs.readdirSync(dirPath);
            
            for (const file of files) {
                const fullPath = path.join(dirPath, file);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    this.analyzeDirectory(fullPath);
                } else if (file.endsWith('.j')) {
                    this.analyzeFile(fullPath);
                }
            }
        } catch (error) {
            console.error(`分析目录失败 ${dirPath}:`, error.message);
        }
    }
    
    /**
     * 分析单个JASS文件
     */
    analyzeFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const relativePath = path.relative(this.projectRoot, filePath);
            
            console.log(`  分析文件: ${relativePath}`);
            
            // 提取所有定义
            this.extractDefinitions(content, relativePath);
            
        } catch (error) {
            console.error(`分析文件失败 ${filePath}:`, error.message);
        }
    }
    
    /**
     * 从文件内容中提取所有定义
     */
    extractDefinitions(content, sourceFile) {
        const lines = content.split('\n');
        let inGlobals = false;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const lineNumber = i + 1;
            
            // 跳过空行和注释
            if (line === '' || line.startsWith('//') || line.startsWith('#')) {
                continue;
            }
            
            // 检查globals块
            if (line === 'globals') {
                inGlobals = true;
                continue;
            }
            if (line === 'endglobals') {
                inGlobals = false;
                continue;
            }
            
            // 提取函数定义
            if (line.startsWith('function ')) {
                this.extractFunction(line, sourceFile, lineNumber);
            }
            
            // 提取全局变量（在globals块中或以特定前缀开头）
            if (inGlobals || line.match(/^\s*\w+\s+(gg_|udg_)/)) {
                this.extractGlobalVariable(line, sourceFile, lineNumber);
            }
            
            // 提取常量定义
            if (line.startsWith('constant ')) {
                this.extractConstant(line, sourceFile, lineNumber);
            }
            
            // 提取类型定义
            if (line.startsWith('type ')) {
                this.extractType(line, sourceFile, lineNumber);
            }
        }
    }
    
    /**
     * 提取函数定义
     */
    extractFunction(line, sourceFile, lineNumber) {
        // 匹配: function 函数名 takes 参数 returns 返回类型
        const match = line.match(/function\s+(\w+)\s+takes\s+(.*?)\s+returns\s+(\w+)/);
        
        if (match) {
            const [, name, paramsStr, returnType] = match;
            
            const functionDef = {
                name: name,
                params: this.parseParameters(paramsStr),
                returnType: returnType,
                sourceFile: sourceFile,
                lineNumber: lineNumber
            };
            
            this.userFunctions.set(name, functionDef);
            this.userTypes.add(returnType);
            
            // 记录参数类型
            functionDef.params.forEach(param => {
                this.userTypes.add(param.type);
            });
        }
    }
    
    /**
     * 解析函数参数
     */
    parseParameters(paramsStr) {
        if (paramsStr.trim() === 'nothing') {
            return [];
        }
        
        const params = [];
        const parts = paramsStr.split(',');
        
        for (const part of parts) {
            const trimmed = part.trim();
            const match = trimmed.match(/(\w+)\s+(\w+)/);
            if (match) {
                const [, type, name] = match;
                params.push({ type, name });
            }
        }
        
        return params;
    }
    
    /**
     * 提取全局变量
     */
    extractGlobalVariable(line, sourceFile, lineNumber) {
        // 匹配各种全局变量格式
        const patterns = [
            /^\s*(\w+)\s+array\s+(\w+)/,           // type array name
            /^\s*(\w+)\s+(\w+)\s*=\s*(.+)/,       // type name = value
            /^\s*(\w+)\s+(\w+)\s*$/                // type name
        ];
        
        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                const type = match[1];
                const name = match[2];
                const value = match[3] || null;
                
                this.userGlobals.set(name, {
                    name: name,
                    type: type,
                    value: value,
                    sourceFile: sourceFile,
                    lineNumber: lineNumber
                });
                
                this.userTypes.add(type);
                break;
            }
        }
    }
    
    /**
     * 提取常量定义
     */
    extractConstant(line, sourceFile, lineNumber) {
        const match = line.match(/constant\s+(\w+)\s+(\w+)\s*=\s*(.+)/);
        if (match) {
            const [, type, name, value] = match;
            
            this.userConstants.set(name, {
                name: name,
                type: type,
                value: value,
                sourceFile: sourceFile,
                lineNumber: lineNumber
            });
            
            this.userTypes.add(type);
        }
    }
    
    /**
     * 提取类型定义
     */
    extractType(line, sourceFile, lineNumber) {
        const match = line.match(/type\s+(\w+)\s+extends\s+(\w+)/);
        if (match) {
            const [, newType, baseType] = match;
            this.userTypes.add(newType);
            this.userTypes.add(baseType);
        }
    }
    
    /**
     * 验证分析结果
     */
    validateAnalysisResults() {
        if (this.userFunctions.size === 0) {
            this.analysisErrors.push('警告: 没有发现任何函数定义');
        }
        
        if (this.userGlobals.size === 0) {
            this.analysisErrors.push('警告: 没有发现任何全局变量');
        }
        
        console.log('分析结果验证完成');
    }
    
    /**
     * 获取分析结果摘要
     */
    getAnalysisSummary() {
        return {
            isAnalyzed: this.isAnalyzed,
            functionsCount: this.userFunctions.size,
            globalsCount: this.userGlobals.size,
            typesCount: this.userTypes.size,
            constantsCount: this.userConstants.size,
            errors: this.analysisErrors
        };
    }
}

module.exports = JassProjectAnalyzer;
