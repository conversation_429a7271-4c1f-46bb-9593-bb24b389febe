// JASS验证测试文件 - 测试各种错误类型
// 这个文件包含故意的错误，用来测试VS Code扩展是否能像地图编辑器一样检测到它们

globals
    integer test_var = 0
    // 错误1: 未声明的变量类型
    unknowntype bad_var = 5
    
    // 错误2: 重复声明
    integer test_var = 1
endglobals

// 错误3: 缺少endfunction
function TestFunction1 takes nothing returns nothing
    call BJDebugMsg("测试函数1")

// 错误4: 参数类型错误
function TestFunction2 takes integer x returns string
    return x  // 应该返回string但返回了integer
endfunction

// 错误5: 未声明的函数调用
function TestFunction3 takes nothing returns nothing
    call UndefinedFunction()
endfunction

// 错误6: 参数数量不匹配
function TestFunction4 takes nothing returns nothing
    call BJDebugMsg()  // BJDebugMsg需要一个string参数
endfunction

// 错误7: 语法错误 - 缺少takes
function TestFunction5 returns nothing
    call BJDebugMsg("测试")
endfunction

// 错误8: 变量未声明
function TestFunction6 takes nothing returns nothing
    set undefined_var = 5
endfunction

// 错误9: 类型不匹配的赋值
function TestFunction7 takes nothing returns nothing
    local string s
    set s = 123  // 字符串变量赋值整数
endfunction

// 错误10: 缺少endloop
function TestFunction8 takes nothing returns nothing
    loop
        call BJDebugMsg("循环测试")

// 正确的函数 - 应该没有错误
function CorrectFunction takes integer x, string s returns boolean
    local integer i = x + 1
    call BJDebugMsg(s + I2S(i))
    return true
endfunction

// 错误11: if语句缺少endif
function TestFunction9 takes nothing returns nothing
    if true then
        call BJDebugMsg("测试if")

// 错误12: 数组越界（如果扩展支持检测）
function TestFunction10 takes nothing returns nothing
    local integer array arr
    set arr[8192] = 1  // 数组索引超出范围
endfunction
