//===========================================================================
// 任意单位施放技能 - 变大持续伤害效果
//===========================================================================

// 全局变量声明
globals
    trigger gg_trg_AnyUnitSpellEffect = null
endglobals

// 定时器回调函数 - 每秒伤害
function Trig_AnyUnitSpellDamageTimer takes nothing returns nothing
    local timer ydl_timer = GetExpiredTimer()
    local unit ydl_caster = YDLocalGet(ydl_timer, unit, "caster")
    local real ydl_damage = YDLocalGet(ydl_timer, real, "damage")
    local real ydl_count = YDLocalGet(ydl_timer, real, "count")
    local group ydl_group
    local unit ydl_unit
    local effect ydl_effect

    // 减少剩余次数
    set ydl_count = ydl_count - 1
    call YDLocalSet(ydl_timer, real, "count", ydl_count)

    // 创建每秒伤害光圈特效
    set ydl_effect = AddSpecialEffect("Abilities\\Spells\\Human\\Thunderclap\\ThunderClapCaster.mdl", GetUnitX(ydl_caster), GetUnitY(ydl_caster))
    call DestroyEffect(ydl_effect)

    // 对周围敌人造成伤害
    set ydl_group = CreateGroup()
    call GroupEnumUnitsInRange(ydl_group, GetUnitX(ydl_caster), GetUnitY(ydl_caster), 300.0, Filter(null))
    loop
        set ydl_unit = FirstOfGroup(ydl_group)
        exitwhen ydl_unit == null
        call GroupRemoveUnit(ydl_group, ydl_unit)

        // 检查是否为敌人且存活
        if IsUnitEnemy(ydl_unit, GetOwningPlayer(ydl_caster)) and GetUnitState(ydl_unit, UNIT_STATE_LIFE) > 0.405 and not IsUnitType(ydl_unit, UNIT_TYPE_STRUCTURE) then
            call UnitDamageTarget(ydl_caster, ydl_unit, ydl_damage, true, false, ATTACK_TYPE_CHAOS, DAMAGE_TYPE_MAGIC, WEAPON_TYPE_WHOKNOWS)
            // 单体受伤特效
            set ydl_effect = AddSpecialEffect("Abilities\\Spells\\Other\\Stampede\\StampedeMissileDeath.mdl", GetUnitX(ydl_unit), GetUnitY(ydl_unit))
            call DestroyEffect(ydl_effect)
        endif
    endloop

    // 检查是否结束
    if ydl_count <= 0 then
        // 恢复原始大小
        call SetUnitScale(ydl_caster, 1.0, 1.0, 1.0)
        // 结束特效
        set ydl_effect = AddSpecialEffectTarget("Abilities\\Spells\\Human\\Polymorph\\PolyMorphDoneGround.mdl", ydl_caster, "origin")
        call DestroyEffect(ydl_effect)

        // 清理定时器数据并销毁
        call YDLocal3Release()
        call DestroyTimer(ydl_timer)
    endif

    // 清理局部变量
    call DestroyGroup(ydl_group)
    set ydl_group = null
    set ydl_unit = null
    set ydl_timer = null
    set ydl_caster = null
    set ydl_effect = null
endfunction

function Trig_AnyUnitSpellEffectActions takes nothing returns nothing
    local timer ydl_timer
    local unit ydl_caster
    local real ydl_heroAgi
    local real ydl_skillLevel
    local real ydl_skillDamage
    local real ydl_critChance
    local real ydl_critDamage
    local real ydl_baseDamage
    local real ydl_finalDamage
    local effect ydl_effect

    // 获取触发单位
    set ydl_caster = GetTriggerUnit()

    // 显示调试信息
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "单位开始施放技能并获得变大效果")

    // 获取英雄属性（如果是英雄）
    if IsUnitType(ydl_caster, UNIT_TYPE_HERO) then
        set ydl_heroAgi = I2R(GetHeroAgi(ydl_caster, true))
        set ydl_skillLevel = 1.0  // 默认技能等级
        set ydl_skillDamage = 0.0  // 默认技能伤害加成
        set ydl_critChance = 10.0  // 默认暴击几率10%
        set ydl_critDamage = 2.0   // 默认暴击伤害2倍
    else
        set ydl_heroAgi = 10.0     // 非英雄单位默认敏捷
        set ydl_skillLevel = 1.0
        set ydl_skillDamage = 0.0
        set ydl_critChance = 5.0
        set ydl_critDamage = 1.5
    endif

    // 计算每秒伤害
    set ydl_baseDamage = ydl_heroAgi * 1.5 * ydl_skillLevel
    set ydl_finalDamage = ydl_baseDamage * (1.0 + ydl_skillDamage)

    // 判断暴击
    if GetRandomReal(1.0, 100.0) <= ydl_critChance then
        set ydl_finalDamage = ydl_finalDamage * ydl_critDamage
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "暴击伤害触发")
    endif

    // 变大特效和动画
    call SetUnitScale(ydl_caster, 1.5, 1.5, 1.5)  // 变大1.5倍
    set ydl_effect = AddSpecialEffectTarget("Abilities\\Spells\\Human\\Polymorph\\PolyMorphTarget.mdl", ydl_caster, "origin")
    call DestroyEffect(ydl_effect)
    set ydl_effect = AddSpecialEffectTarget("Abilities\\Spells\\Orc\\WarStomp\\WarStompCaster.mdl", ydl_caster, "origin")
    call DestroyEffect(ydl_effect)

    // 创建持续伤害定时器
    set ydl_timer = CreateTimer()
    call YDLocalSet(ydl_timer, unit, "caster", ydl_caster)
    call YDLocalSet(ydl_timer, real, "damage", ydl_finalDamage)
    call YDLocalSet(ydl_timer, real, "count", 15.0)  // 持续15秒
    call TimerStart(ydl_timer, 1.0, true, function Trig_AnyUnitSpellDamageTimer)

    // 施法音效
    set ydl_effect = AddSpecialEffectTarget("Abilities\\Spells\\Orc\\WarStomp\\WarStompCaster.mdl", ydl_caster, "overhead")
    call DestroyEffect(ydl_effect)

    // 清理局部变量
    set ydl_timer = null
    set ydl_caster = null
endfunction

function InitTrig_AnyUnitSpellEffect takes nothing returns nothing
    set gg_trg_AnyUnitSpellEffect = CreateTrigger()
    call TriggerRegisterAnyUnitEventBJ(gg_trg_AnyUnitSpellEffect, EVENT_PLAYER_UNIT_SPELL_EFFECT)
    call TriggerAddAction(gg_trg_AnyUnitSpellEffect, function Trig_AnyUnitSpellEffectActions)
endfunction

//===========================================================================
// 使用说明：
// 1. 将此代码复制到地图编辑器的触发器中
// 2. 在主初始化函数中调用 InitTrig_AnyUnitSpellEffect()
// 3. 这个触发器会响应任意单位开始施放技能的事件，没有任何条件限制
// 4. 每当有单位施放技能时，会显示信息并在施法位置创建特效
//===========================================================================
