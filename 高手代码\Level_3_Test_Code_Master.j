// Level 3 测试代码 - 代码宗师功能验证
// 这个文件包含各种代码质量问题，用于测试Level 3的检测能力

function TestMemoryLeaks takes nothing returns nothing
    local location loc1
    local group grp1
    local effect eff1
    local timer tmr1
    
    // 内存泄漏测试1: Location泄漏
    set loc1 = GetRectCenter(gg_rct_qy1)
    call CreateUnit(Player(0), 'hfoo', GetLocationX(loc1), GetLocationY(loc1), 0)
    // 缺少: call RemoveLocation(loc1)
    
    // 内存泄漏测试2: Group泄漏
    set grp1 = CreateGroup()
    call GroupEnumUnitsInRange(grp1, 0, 0, 500, null)
    // 缺少: call DestroyGroup(grp1)
    
    // 内存泄漏测试3: Effect泄漏
    set eff1 = AddSpecialEffect("Abilities\\Spells\\Human\\HolyBolt\\HolyBoltSpecialArt.mdl", 0, 0)
    // 缺少: call DestroyEffect(eff1)
    
    // 内存泄漏测试4: Timer泄漏
    set tmr1 = CreateTimer()
    call TimerStart(tmr1, 1.0, false, null)
    // 缺少: call DestroyTimer(tmr1)
    
    // 内存泄漏测试5: 函数参数中的Location泄漏（最危险！）
    call CreateUnit(Player(0), 'hfoo', GetLocationX(GetRectCenter(gg_rct_qy2)), GetLocationY(GetRectCenter(gg_rct_qy2)), 0)
endfunction

function TestPerformanceIssues takes nothing returns nothing
    local integer i
    local location tempLoc
    local group tempGroup
    
    // 性能问题测试1: 循环中的Location创建
    set i = 0
    loop
        exitwhen i > 100
        set tempLoc = GetUnitLoc(udg_YX[i])  // 性能杀手！
        call CreateUnit(Player(0), 'hfoo', GetLocationX(tempLoc), GetLocationY(tempLoc), 0)
        set i = i + 1
    endloop
    
    // 性能问题测试2: 循环中的Group操作
    set i = 0
    loop
        exitwhen i > 50
        set tempGroup = CreateGroup()  // 性能问题！
        call GroupEnumUnitsInRange(tempGroup, 0, 0, 500, null)
        call DestroyGroup(tempGroup)
        set i = i + 1
    endloop
    
    // 性能问题测试3: 嵌套循环
    local integer j
    set i = 0
    loop
        exitwhen i > 10
        set j = 0
        loop
            exitwhen j > 10
            loop  // 三层嵌套！
                exitwhen j > 5
                call GetUnitLoc(udg_YX[i])  // 在深度嵌套中调用Location
                set j = j + 1
            endloop
            set j = j + 1
        endloop
        set i = i + 1
    endloop
endfunction

function TestStyleIssues takes nothing returns nothing
    // 风格问题测试1: 局部变量命名不规范
    local integer badName  // 应该是 ydl_badName
    local real AnotherBadName  // 应该是 ydl_anotherBadName
    
    // 风格问题测试2: 魔法数字
    set badName = 12345  // 魔法数字
    if badName > 9999 then  // 另一个魔法数字
        set badName = 88888  // 又一个魔法数字
    endif
    
    // 风格问题测试3: 复杂逻辑缺少注释
    if badName > 1000 and badName < 5000 and AnotherBadName > 0.5 and AnotherBadName < 2.0 then
        // 复杂条件但没有解释
    endif
    
    // 风格问题测试4: 行过长
    call CreateUnit(Player(0), 'hfoo', GetLocationX(GetRectCenter(gg_rct_qy1)), GetLocationY(GetRectCenter(gg_rct_qy1)), GetRandomReal(0, 360))
    
    // 风格问题测试5: 缩进不一致
  set badName = 1  // 2个空格
      set AnotherBadName = 2.0  // 6个空格
        set badName = 3  // 8个空格
endfunction

// 风格问题测试6: 函数缺少注释
function UncommentedFunction takes integer param1, real param2, boolean param3, string param4, unit param5, player param6 returns nothing
    // 这个函数有太多参数（6个）且没有注释说明
    local integer result = param1 * 100
    if param3 then
        set result = result + R2I(param2)
    endif
endfunction

function TestBestPractices takes nothing returns nothing
    local location loc
    local group grp
    
    // 最佳实践测试1: 正确的资源管理
    set loc = GetRectCenter(gg_rct_qy1)
    call CreateUnit(Player(0), 'hfoo', GetLocationX(loc), GetLocationY(loc), 0)
    call RemoveLocation(loc)  // 正确清理
    
    // 最佳实践测试2: 使用坐标替代Location
    call CreateUnit(Player(0), 'hfoo', GetUnitX(udg_YX[0]), GetUnitY(udg_YX[0]), 0)  // 更高效
    
    // 最佳实践测试3: 预分配资源
    set grp = CreateGroup()
    // 在循环外创建，循环内重复使用
    local integer i = 0
    loop
        exitwhen i > 10
        call GroupClear(grp)
        call GroupEnumUnitsInRange(grp, 0, 0, 500, null)
        // 处理group...
        set i = i + 1
    endloop
    call DestroyGroup(grp)  // 最后清理
endfunction

// 这是一个超长函数，用于测试函数长度检查
function VeryLongFunction takes nothing returns nothing
    local integer i = 0
    local integer j = 0
    local integer k = 0
    local real x = 0
    local real y = 0
    local location loc
    local group grp
    local effect eff
    
    // 第10行
    set i = 1
    set j = 2
    set k = 3
    set x = 4.0
    set y = 5.0
    
    // 第20行
    loop
        exitwhen i > 100
        set loc = GetUnitLoc(udg_YX[i])
        set x = GetLocationX(loc)
        set y = GetLocationY(loc)
        call RemoveLocation(loc)
        
        // 第30行
        if x > 0 and y > 0 then
            set grp = CreateGroup()
            call GroupEnumUnitsInRange(grp, x, y, 500, null)
            
            // 第40行
            loop
                set eff = AddSpecialEffect("test.mdl", x, y)
                call DestroyEffect(eff)
                set j = j + 1
                exitwhen j > 10
            endloop
            
            // 第50行
            call DestroyGroup(grp)
        endif
        
        set i = i + 1
    endloop
    
    // 这个函数超过50行，应该被标记为过长
endfunction

function TestComplexExpression takes nothing returns nothing
    local integer ydl_value
    local boolean ydl_result
    
    // 复杂表达式测试
    set ydl_result = (udg_SL > 100) and (udg_boshu < 50) and (GetRandomInt(1, 100) > 50) and (udg_n > 0.5) and (GetPlayerState(Player(0), PLAYER_STATE_RESOURCE_GOLD) > 1000)
    
    // 单行多函数调用
    call CreateUnit(Player(0), 'hfoo', GetUnitX(GetTriggerUnit()), GetUnitY(GetTriggerUnit()), GetRandomReal(0, 360))
endfunction
