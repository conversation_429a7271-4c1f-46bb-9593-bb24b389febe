import { <PERSON><PERSON><PERSON>exer } from '../parser/JassLexer';
import { JassParser } from '../parser/JassParser';
import { SymbolTable } from './SymbolTable';
import { <PERSON>Checker } from './TypeChecker';
import { ApiDatabase } from './ApiDatabase';

export interface CompileOptions {
    warcraft3Version: string;
    enableYDWE: boolean;
    enableDZAPI: boolean;
    enableJassHelper: boolean;
    strictMode: boolean;
    treatWarningsAsErrors: boolean;
}

export interface CompileError {
    line: number;
    column: number;
    length: number;
    message: string;
    severity: 'error' | 'warning';
    code: string;
    file?: string;
}

export interface CompileResult {
    success: boolean;
    errors: CompileError[];
    warnings: CompileError[];
    symbolTable: SymbolTable;
    ast?: any;
}

export class JassCompiler {
    private lexer: typeof JassLexer;
    private parser: JassParser;
    private symbolTable: SymbolTable;
    private typeChecker: <PERSON>Checker;
    private apiDatabase: ApiDatabase;

    constructor() {
        this.lexer = JassLexer;
        this.parser = new JassParser();
        this.symbolTable = new SymbolTable();
        this.apiDatabase = new ApiDatabase();
        this.typeChecker = new TypeChecker(this.symbolTable, this.apiDatabase);
    }

    public compile(sourceCode: string, options: CompileOptions, fileName?: string): CompileResult {
        const result: CompileResult = {
            success: false,
            errors: [],
            warnings: [],
            symbolTable: this.symbolTable
        };

        try {
            // 1. 词法分析
            const lexResult = this.lexer.tokenize(sourceCode);
            
            if (lexResult.errors.length > 0) {
                lexResult.errors.forEach(error => {
                    result.errors.push({
                        line: error.line || 1,
                        column: error.column || 1,
                        length: error.length || 1,
                        message: `词法错误: ${error.message}`,
                        severity: 'error',
                        code: 'lexer-error',
                        file: fileName
                    });
                });
                return result;
            }

            // 2. 语法分析
            this.parser.input = lexResult.tokens;
            const cst = this.parser.program();
            
            if (this.parser.errors.length > 0) {
                this.parser.errors.forEach(error => {
                    result.errors.push({
                        line: error.token?.startLine || 1,
                        column: error.token?.startColumn || 1,
                        length: error.token?.image?.length || 1,
                        message: `语法错误: ${error.message}`,
                        severity: 'error',
                        code: 'parser-error',
                        file: fileName
                    });
                });
                return result;
            }

            result.ast = cst;

            // 3. 初始化API数据库
            this.apiDatabase.initialize(options);

            // 4. 符号表构建
            this.buildSymbolTable(cst, result, options);

            // 5. 类型检查
            this.performTypeChecking(cst, result, options);

            // 6. 语义分析
            this.performSemanticAnalysis(cst, result, options);

            // 7. 确定编译结果
            const hasErrors = result.errors.length > 0;
            const hasWarningsAsErrors = options.treatWarningsAsErrors && result.warnings.length > 0;
            
            result.success = !hasErrors && !hasWarningsAsErrors;

            if (result.success) {
                console.log(`✅ 编译成功: ${fileName || '代码'}`);
            } else {
                console.log(`❌ 编译失败: ${result.errors.length} 个错误, ${result.warnings.length} 个警告`);
            }

        } catch (error) {
            result.errors.push({
                line: 1,
                column: 1,
                length: 1,
                message: `编译器内部错误: ${error}`,
                severity: 'error',
                code: 'compiler-error',
                file: fileName
            });
        }

        return result;
    }

    private buildSymbolTable(cst: any, result: CompileResult, options: CompileOptions): void {
        // 重置符号表
        this.symbolTable.clear();
        
        // 添加内置类型
        this.symbolTable.addBuiltinTypes();
        
        // 添加API函数
        this.symbolTable.addApiFunctions(this.apiDatabase.getAllFunctions());

        try {
            // 遍历CST构建符号表
            this.traverseForSymbols(cst, result);
        } catch (error) {
            result.errors.push({
                line: 1,
                column: 1,
                length: 1,
                message: `符号表构建错误: ${error}`,
                severity: 'error',
                code: 'symbol-table-error'
            });
        }
    }

    private traverseForSymbols(node: any, result: CompileResult): void {
        if (!node || !node.children) return;

        // 处理全局变量声明
        if (node.children.globalDeclaration) {
            node.children.globalDeclaration.forEach((globalDecl: any) => {
                this.processGlobalDeclaration(globalDecl, result);
            });
        }

        // 处理函数声明
        if (node.children.functionDeclaration) {
            node.children.functionDeclaration.forEach((funcDecl: any) => {
                this.processFunctionDeclaration(funcDecl, result);
            });
        }

        // 处理原生函数声明
        if (node.children.nativeDeclaration) {
            node.children.nativeDeclaration.forEach((nativeDecl: any) => {
                this.processNativeDeclaration(nativeDecl, result);
            });
        }

        // 处理类型声明
        if (node.children.typeDeclaration) {
            node.children.typeDeclaration.forEach((typeDecl: any) => {
                this.processTypeDeclaration(typeDecl, result);
            });
        }
    }

    private processGlobalDeclaration(globalDecl: any, result: CompileResult): void {
        // 处理全局变量声明的逻辑
        if (globalDecl.children.globalVariable) {
            globalDecl.children.globalVariable.forEach((varDecl: any) => {
                const varName = this.extractIdentifier(varDecl);
                const varType = this.extractType(varDecl);
                
                if (varName && varType) {
                    if (this.symbolTable.hasSymbol(varName)) {
                        result.errors.push({
                            line: this.getLine(varDecl),
                            column: this.getColumn(varDecl),
                            length: varName.length,
                            message: `全局变量 '${varName}' 重复定义`,
                            severity: 'error',
                            code: 'duplicate-global'
                        });
                    } else {
                        this.symbolTable.addGlobalVariable(varName, varType);
                    }
                }
            });
        }
    }

    private processFunctionDeclaration(funcDecl: any, result: CompileResult): void {
        const funcName = this.extractIdentifier(funcDecl);
        const returnType = this.extractReturnType(funcDecl);
        const parameters = this.extractParameters(funcDecl);
        
        if (funcName && returnType) {
            if (this.symbolTable.hasFunction(funcName)) {
                result.errors.push({
                    line: this.getLine(funcDecl),
                    column: this.getColumn(funcDecl),
                    length: funcName.length,
                    message: `函数 '${funcName}' 重复定义`,
                    severity: 'error',
                    code: 'duplicate-function'
                });
            } else {
                this.symbolTable.addFunction(funcName, returnType, parameters);
            }
        }
    }

    private processNativeDeclaration(nativeDecl: any, result: CompileResult): void {
        const funcName = this.extractIdentifier(nativeDecl);
        const returnType = this.extractReturnType(nativeDecl);
        const parameters = this.extractParameters(nativeDecl);
        
        if (funcName && returnType) {
            this.symbolTable.addNativeFunction(funcName, returnType, parameters);
        }
    }

    private processTypeDeclaration(typeDecl: any, result: CompileResult): void {
        const typeName = this.extractIdentifier(typeDecl);
        const baseType = this.extractBaseType(typeDecl);
        
        if (typeName && baseType) {
            if (this.symbolTable.hasType(typeName)) {
                result.errors.push({
                    line: this.getLine(typeDecl),
                    column: this.getColumn(typeDecl),
                    length: typeName.length,
                    message: `类型 '${typeName}' 重复定义`,
                    severity: 'error',
                    code: 'duplicate-type'
                });
            } else {
                this.symbolTable.addType(typeName, baseType);
            }
        }
    }

    private performTypeChecking(cst: any, result: CompileResult, options: CompileOptions): void {
        try {
            this.typeChecker.check(cst, result, options);
        } catch (error) {
            result.errors.push({
                line: 1,
                column: 1,
                length: 1,
                message: `类型检查错误: ${error}`,
                severity: 'error',
                code: 'type-check-error'
            });
        }
    }

    private performSemanticAnalysis(cst: any, result: CompileResult, options: CompileOptions): void {
        // 语义分析：检查未使用的变量、函数等
        this.checkUnusedSymbols(result);
        
        // 检查函数调用
        this.checkFunctionCalls(cst, result);
        
        // 检查变量使用
        this.checkVariableUsage(cst, result);
    }

    private checkUnusedSymbols(result: CompileResult): void {
        // 检查未使用的符号
        const unusedSymbols = this.symbolTable.getUnusedSymbols();
        unusedSymbols.forEach(symbol => {
            result.warnings.push({
                line: symbol.line || 1,
                column: symbol.column || 1,
                length: symbol.name.length,
                message: `未使用的${symbol.type}: '${symbol.name}'`,
                severity: 'warning',
                code: 'unused-symbol'
            });
        });
    }

    private checkFunctionCalls(cst: any, result: CompileResult): void {
        // 检查函数调用的合法性
        // 这里需要遍历CST找到所有函数调用并验证
    }

    private checkVariableUsage(cst: any, result: CompileResult): void {
        // 检查变量使用的合法性
        // 检查变量是否在使用前声明等
    }

    // 辅助方法
    private extractIdentifier(node: any): string | null {
        if (node.children && node.children.Identifier && node.children.Identifier[0]) {
            return node.children.Identifier[0].image;
        }
        return null;
    }

    private extractType(node: any): string | null {
        // 从节点中提取类型信息
        if (node.children && node.children.typeReference) {
            return this.extractTypeFromReference(node.children.typeReference[0]);
        }
        return null;
    }

    private extractTypeFromReference(typeRef: any): string | null {
        if (!typeRef.children) return null;
        
        // 检查各种类型token
        const typeTokens = [
            'IntegerType', 'RealType', 'BooleanType', 'StringType', 'HandleType', 'CodeType',
            'UnitType', 'PlayerType', 'LocationType', 'RectType', 'RegionType', 'TimerType',
            'TriggerType', 'GroupType', 'ForceType', 'EffectType', 'SoundType', 'FrameHandleType',
            'Nothing', 'Identifier'
        ];
        
        for (const tokenType of typeTokens) {
            if (typeRef.children[tokenType] && typeRef.children[tokenType][0]) {
                return typeRef.children[tokenType][0].image;
            }
        }
        
        return null;
    }

    private extractReturnType(node: any): string | null {
        // 提取函数返回类型
        if (node.children && node.children.typeReference) {
            const typeRefs = node.children.typeReference;
            if (typeRefs.length > 1) {
                return this.extractTypeFromReference(typeRefs[1]); // 返回类型是第二个
            }
        }
        return null;
    }

    private extractParameters(node: any): Array<{name: string, type: string}> {
        const params: Array<{name: string, type: string}> = [];
        
        if (node.children && node.children.parameterList && node.children.parameterList[0]) {
            const paramList = node.children.parameterList[0];
            if (paramList.children && paramList.children.parameter) {
                paramList.children.parameter.forEach((param: any) => {
                    const name = this.extractIdentifier(param);
                    const type = this.extractType(param);
                    if (name && type) {
                        params.push({ name, type });
                    }
                });
            }
        }
        
        return params;
    }

    private extractBaseType(node: any): string | null {
        // 提取类型声明的基类型
        if (node.children && node.children.typeReference) {
            const typeRefs = node.children.typeReference;
            if (typeRefs.length > 1) {
                return this.extractTypeFromReference(typeRefs[1]); // 基类型是第二个
            }
        }
        return null;
    }

    private getLine(node: any): number {
        // 从节点中提取行号
        if (node.location && node.location.startLine) {
            return node.location.startLine;
        }
        return 1;
    }

    private getColumn(node: any): number {
        // 从节点中提取列号
        if (node.location && node.location.startColumn) {
            return node.location.startColumn;
        }
        return 1;
    }
}
