import { CompileOptions } from './JassCompiler';

export interface ApiFunction {
    name: string;
    returnType: string;
    parameters: ApiParameter[];
    category: 'native' | 'ydwe' | 'dzapi' | 'user';
    version?: string;
    description?: string;
}

export interface ApiParameter {
    name: string;
    type: string;
    optional?: boolean;
}

export interface ApiType {
    name: string;
    baseType?: string;
    category: 'primitive' | 'handle' | 'custom';
}

export class ApiDatabase {
    private functions: Map<string, ApiFunction> = new Map();
    private types: Map<string, ApiType> = new Map();
    private initialized: boolean = false;

    public initialize(options: CompileOptions): void {
        if (this.initialized) return;

        this.functions.clear();
        this.types.clear();

        // 添加基本类型
        this.addPrimitiveTypes();
        
        // 添加原生JASS函数
        this.addNativeFunctions(options.warcraft3Version);
        
        // 添加YDWE API
        if (options.enableYDWE) {
            this.addYDWEFunctions();
        }
        
        // 添加DZAPI
        if (options.enableDZAPI) {
            this.addDZAPIFunctions();
        }

        this.initialized = true;
    }

    private addPrimitiveTypes(): void {
        const primitiveTypes: ApiType[] = [
            { name: 'integer', category: 'primitive' },
            { name: 'real', category: 'primitive' },
            { name: 'boolean', category: 'primitive' },
            { name: 'string', category: 'primitive' },
            { name: 'handle', category: 'handle' },
            { name: 'code', category: 'primitive' },
            { name: 'nothing', category: 'primitive' }
        ];

        const handleTypes: ApiType[] = [
            { name: 'unit', baseType: 'handle', category: 'handle' },
            { name: 'player', baseType: 'handle', category: 'handle' },
            { name: 'location', baseType: 'handle', category: 'handle' },
            { name: 'rect', baseType: 'handle', category: 'handle' },
            { name: 'region', baseType: 'handle', category: 'handle' },
            { name: 'timer', baseType: 'handle', category: 'handle' },
            { name: 'trigger', baseType: 'handle', category: 'handle' },
            { name: 'group', baseType: 'handle', category: 'handle' },
            { name: 'force', baseType: 'handle', category: 'handle' },
            { name: 'effect', baseType: 'handle', category: 'handle' },
            { name: 'sound', baseType: 'handle', category: 'handle' },
            { name: 'framehandle', baseType: 'handle', category: 'handle' },
            { name: 'multiboard', baseType: 'handle', category: 'handle' },
            { name: 'dialog', baseType: 'handle', category: 'handle' },
            { name: 'button', baseType: 'handle', category: 'handle' },
            { name: 'leaderboard', baseType: 'handle', category: 'handle' },
            { name: 'lightning', baseType: 'handle', category: 'handle' },
            { name: 'image', baseType: 'handle', category: 'handle' },
            { name: 'ubersplat', baseType: 'handle', category: 'handle' },
            { name: 'hashtable', baseType: 'handle', category: 'handle' }
        ];

        [...primitiveTypes, ...handleTypes].forEach(type => {
            this.types.set(type.name, type);
        });
    }

    private addNativeFunctions(version: string): void {
        const nativeFunctions: ApiFunction[] = [
            // 单位相关
            {
                name: 'GetAttacker',
                returnType: 'unit',
                parameters: [],
                category: 'native',
                description: '获取攻击者单位'
            },
            {
                name: 'GetTriggerUnit',
                returnType: 'unit',
                parameters: [],
                category: 'native',
                description: '获取触发事件的单位'
            },
            {
                name: 'CreateUnit',
                returnType: 'unit',
                parameters: [
                    { name: 'whichPlayer', type: 'player' },
                    { name: 'unitid', type: 'integer' },
                    { name: 'x', type: 'real' },
                    { name: 'y', type: 'real' },
                    { name: 'face', type: 'real' }
                ],
                category: 'native',
                description: '创建单位'
            },
            {
                name: 'KillUnit',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichUnit', type: 'unit' }
                ],
                category: 'native',
                description: '杀死单位'
            },
            {
                name: 'RemoveUnit',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichUnit', type: 'unit' }
                ],
                category: 'native',
                description: '移除单位'
            },
            {
                name: 'GetUnitX',
                returnType: 'real',
                parameters: [
                    { name: 'whichUnit', type: 'unit' }
                ],
                category: 'native',
                description: '获取单位X坐标'
            },
            {
                name: 'GetUnitY',
                returnType: 'real',
                parameters: [
                    { name: 'whichUnit', type: 'unit' }
                ],
                category: 'native',
                description: '获取单位Y坐标'
            },
            {
                name: 'SetUnitX',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichUnit', type: 'unit' },
                    { name: 'newX', type: 'real' }
                ],
                category: 'native',
                description: '设置单位X坐标'
            },
            {
                name: 'SetUnitY',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichUnit', type: 'unit' },
                    { name: 'newY', type: 'real' }
                ],
                category: 'native',
                description: '设置单位Y坐标'
            },
            {
                name: 'GetHeroAgi',
                returnType: 'integer',
                parameters: [
                    { name: 'whichHero', type: 'unit' },
                    { name: 'includeBonuses', type: 'boolean' }
                ],
                category: 'native',
                description: '获取英雄敏捷'
            },
            {
                name: 'SetHeroAgi',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichHero', type: 'unit' },
                    { name: 'newAgi', type: 'integer' },
                    { name: 'permanent', type: 'boolean' }
                ],
                category: 'native',
                description: '设置英雄敏捷'
            },
            {
                name: 'UnitDamageTarget',
                returnType: 'boolean',
                parameters: [
                    { name: 'whichUnit', type: 'unit' },
                    { name: 'target', type: 'unit' },
                    { name: 'amount', type: 'real' },
                    { name: 'attack', type: 'boolean' },
                    { name: 'ranged', type: 'boolean' },
                    { name: 'attackType', type: 'integer' },
                    { name: 'damageType', type: 'integer' },
                    { name: 'weaponType', type: 'integer' }
                ],
                category: 'native',
                description: '单位对目标造成伤害'
            },

            // 玩家相关
            {
                name: 'GetLocalPlayer',
                returnType: 'player',
                parameters: [],
                category: 'native',
                description: '获取本地玩家'
            },
            {
                name: 'Player',
                returnType: 'player',
                parameters: [
                    { name: 'number', type: 'integer' }
                ],
                category: 'native',
                description: '根据编号获取玩家'
            },

            // 显示相关
            {
                name: 'DisplayTextToPlayer',
                returnType: 'nothing',
                parameters: [
                    { name: 'toPlayer', type: 'player' },
                    { name: 'x', type: 'real' },
                    { name: 'y', type: 'real' },
                    { name: 'message', type: 'string' }
                ],
                category: 'native',
                description: '向玩家显示文本'
            },

            // 类型转换
            {
                name: 'I2R',
                returnType: 'real',
                parameters: [
                    { name: 'i', type: 'integer' }
                ],
                category: 'native',
                description: '整数转实数'
            },
            {
                name: 'R2I',
                returnType: 'integer',
                parameters: [
                    { name: 'r', type: 'real' }
                ],
                category: 'native',
                description: '实数转整数'
            },
            {
                name: 'I2S',
                returnType: 'string',
                parameters: [
                    { name: 'i', type: 'integer' }
                ],
                category: 'native',
                description: '整数转字符串'
            },
            {
                name: 'R2S',
                returnType: 'string',
                parameters: [
                    { name: 'r', type: 'real' }
                ],
                category: 'native',
                description: '实数转字符串'
            },
            {
                name: 'S2I',
                returnType: 'integer',
                parameters: [
                    { name: 's', type: 'string' }
                ],
                category: 'native',
                description: '字符串转整数'
            },
            {
                name: 'S2R',
                returnType: 'real',
                parameters: [
                    { name: 's', type: 'string' }
                ],
                category: 'native',
                description: '字符串转实数'
            },

            // 计时器相关
            {
                name: 'CreateTimer',
                returnType: 'timer',
                parameters: [],
                category: 'native',
                description: '创建计时器'
            },
            {
                name: 'DestroyTimer',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichTimer', type: 'timer' }
                ],
                category: 'native',
                description: '销毁计时器'
            },
            {
                name: 'TimerStart',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichTimer', type: 'timer' },
                    { name: 'timeout', type: 'real' },
                    { name: 'periodic', type: 'boolean' },
                    { name: 'handlerFunc', type: 'code' }
                ],
                category: 'native',
                description: '启动计时器'
            },

            // 触发器相关
            {
                name: 'CreateTrigger',
                returnType: 'trigger',
                parameters: [],
                category: 'native',
                description: '创建触发器'
            },
            {
                name: 'DestroyTrigger',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichTrigger', type: 'trigger' }
                ],
                category: 'native',
                description: '销毁触发器'
            },
            {
                name: 'TriggerRegisterPlayerUnitEventSimple',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichTrigger', type: 'trigger' },
                    { name: 'whichPlayer', type: 'player' },
                    { name: 'whichPlayerUnitEvent', type: 'integer' }
                ],
                category: 'native',
                description: '注册玩家单位事件'
            },
            {
                name: 'TriggerAddCondition',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichTrigger', type: 'trigger' },
                    { name: 'condition', type: 'code' }
                ],
                category: 'native',
                description: '添加触发器条件'
            },
            {
                name: 'TriggerAddAction',
                returnType: 'nothing',
                parameters: [
                    { name: 'whichTrigger', type: 'trigger' },
                    { name: 'actionFunc', type: 'code' }
                ],
                category: 'native',
                description: '添加触发器动作'
            },
            {
                name: 'Condition',
                returnType: 'code',
                parameters: [
                    { name: 'func', type: 'code' }
                ],
                category: 'native',
                description: '创建条件'
            }
        ];

        nativeFunctions.forEach(func => {
            this.functions.set(func.name, func);
        });
    }

    private addYDWEFunctions(): void {
        const ydweFunctions: ApiFunction[] = [
            {
                name: 'YDLocalInitialize',
                returnType: 'nothing',
                parameters: [],
                category: 'ydwe',
                description: '初始化YDLocal系统'
            },
            {
                name: 'YDLocal1Set',
                returnType: 'nothing',
                parameters: [
                    { name: 'type', type: 'string' },
                    { name: 'key', type: 'string' },
                    { name: 'value', type: 'handle' }
                ],
                category: 'ydwe',
                description: '设置YDLocal变量'
            },
            {
                name: 'YDLocal1Get',
                returnType: 'handle',
                parameters: [
                    { name: 'type', type: 'string' },
                    { name: 'key', type: 'string' }
                ],
                category: 'ydwe',
                description: '获取YDLocal变量'
            },
            {
                name: 'YDLocal1Release',
                returnType: 'nothing',
                parameters: [],
                category: 'ydwe',
                description: '释放YDLocal资源'
            },
            {
                name: 'YDUserDataSet',
                returnType: 'nothing',
                parameters: [
                    { name: 'player', type: 'player' },
                    { name: 'key', type: 'handle' },
                    { name: 'name', type: 'string' },
                    { name: 'type', type: 'string' },
                    { name: 'value', type: 'handle' }
                ],
                category: 'ydwe',
                description: '设置用户数据'
            },
            {
                name: 'YDUserDataGet',
                returnType: 'handle',
                parameters: [
                    { name: 'player', type: 'player' },
                    { name: 'key', type: 'handle' },
                    { name: 'name', type: 'string' },
                    { name: 'type', type: 'string' }
                ],
                category: 'ydwe',
                description: '获取用户数据'
            },
            {
                name: 'YDWECreateEffectOnUnit',
                returnType: 'effect',
                parameters: [
                    { name: 'targetUnit', type: 'unit' },
                    { name: 'effectPath', type: 'string' }
                ],
                category: 'ydwe',
                description: '在单位上创建特效'
            }
        ];

        ydweFunctions.forEach(func => {
            this.functions.set(func.name, func);
        });
    }

    private addDZAPIFunctions(): void {
        const dzapiFunctions: ApiFunction[] = [
            {
                name: 'DzGetGameUI',
                returnType: 'framehandle',
                parameters: [],
                category: 'dzapi',
                description: '获取游戏UI根框架'
            },
            {
                name: 'DzCreateFrameByTagName',
                returnType: 'framehandle',
                parameters: [
                    { name: 'typeName', type: 'string' },
                    { name: 'name', type: 'string' },
                    { name: 'parent', type: 'framehandle' },
                    { name: 'inherits', type: 'string' },
                    { name: 'createContext', type: 'integer' }
                ],
                category: 'dzapi',
                description: '根据标签名创建框架'
            },
            {
                name: 'DzFrameSetPoint',
                returnType: 'nothing',
                parameters: [
                    { name: 'frame', type: 'framehandle' },
                    { name: 'point', type: 'integer' },
                    { name: 'relativeFrame', type: 'framehandle' },
                    { name: 'relativePoint', type: 'integer' },
                    { name: 'x', type: 'real' },
                    { name: 'y', type: 'real' }
                ],
                category: 'dzapi',
                description: '设置框架位置'
            },
            {
                name: 'DzFrameSetSize',
                returnType: 'nothing',
                parameters: [
                    { name: 'frame', type: 'framehandle' },
                    { name: 'width', type: 'real' },
                    { name: 'height', type: 'real' }
                ],
                category: 'dzapi',
                description: '设置框架大小'
            },
            {
                name: 'DzFrameSetText',
                returnType: 'nothing',
                parameters: [
                    { name: 'frame', type: 'framehandle' },
                    { name: 'text', type: 'string' }
                ],
                category: 'dzapi',
                description: '设置框架文本'
            },
            {
                name: 'DzFrameShow',
                returnType: 'nothing',
                parameters: [
                    { name: 'frame', type: 'framehandle' },
                    { name: 'visible', type: 'boolean' }
                ],
                category: 'dzapi',
                description: '显示或隐藏框架'
            }
        ];

        dzapiFunctions.forEach(func => {
            this.functions.set(func.name, func);
        });
    }

    public hasFunction(name: string): boolean {
        return this.functions.has(name);
    }

    public getFunction(name: string): ApiFunction | undefined {
        return this.functions.get(name);
    }

    public getAllFunctions(): ApiFunction[] {
        return Array.from(this.functions.values());
    }

    public hasType(name: string): boolean {
        return this.types.has(name);
    }

    public getType(name: string): ApiType | undefined {
        return this.types.get(name);
    }

    public getAllTypes(): ApiType[] {
        return Array.from(this.types.values());
    }

    public isTypeCompatible(sourceType: string, targetType: string): boolean {
        if (sourceType === targetType) return true;
        
        const source = this.getType(sourceType);
        const target = this.getType(targetType);
        
        if (!source || !target) return false;
        
        // 检查继承关系
        if (source.baseType === targetType) return true;
        if (target.baseType === sourceType) return true;
        
        // handle类型兼容性
        if (target.name === 'handle' && source.baseType === 'handle') return true;
        
        return false;
    }
}
