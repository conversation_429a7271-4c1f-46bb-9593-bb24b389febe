import { SymbolTable, FunctionSymbol, VariableSymbol } from './SymbolTable';
import { ApiDatabase } from './ApiDatabase';
import { CompileResult, CompileError, CompileOptions } from './JassCompiler';

export class TypeChecker {
    private symbolTable: SymbolTable;
    private apiDatabase: ApiDatabase;

    constructor(symbolTable: SymbolTable, apiDatabase: ApiDatabase) {
        this.symbolTable = symbolTable;
        this.apiDatabase = apiDatabase;
    }

    public check(cst: any, result: CompileResult, options: CompileOptions): void {
        this.checkNode(cst, result, options);
    }

    private checkNode(node: any, result: CompileResult, options: CompileOptions): void {
        if (!node || !node.children) return;

        // 检查函数声明
        if (node.children.functionDeclaration) {
            node.children.functionDeclaration.forEach((funcDecl: any) => {
                this.checkFunctionDeclaration(funcDecl, result, options);
            });
        }

        // 检查全局变量声明
        if (node.children.globalDeclaration) {
            node.children.globalDeclaration.forEach((globalDecl: any) => {
                this.checkGlobalDeclaration(globalDecl, result, options);
            });
        }

        // 检查类型声明
        if (node.children.typeDeclaration) {
            node.children.typeDeclaration.forEach((typeDecl: any) => {
                this.checkTypeDeclaration(typeDecl, result, options);
            });
        }
    }

    private checkFunctionDeclaration(funcDecl: any, result: CompileResult, options: CompileOptions): void {
        const funcName = this.extractIdentifier(funcDecl);
        if (!funcName) return;

        // 进入函数作用域
        this.symbolTable.enterFunction(funcName);

        try {
            // 检查参数
            this.checkParameters(funcDecl, result, options);

            // 检查函数体中的语句
            if (funcDecl.children.statement) {
                funcDecl.children.statement.forEach((stmt: any) => {
                    this.checkStatement(stmt, result, options);
                });
            }

            // 检查返回值
            this.checkFunctionReturns(funcDecl, result, options);

        } finally {
            // 退出函数作用域
            this.symbolTable.exitFunction();
        }
    }

    private checkGlobalDeclaration(globalDecl: any, result: CompileResult, options: CompileOptions): void {
        if (globalDecl.children.globalVariable) {
            globalDecl.children.globalVariable.forEach((varDecl: any) => {
                this.checkGlobalVariable(varDecl, result, options);
            });
        }
    }

    private checkGlobalVariable(varDecl: any, result: CompileResult, options: CompileOptions): void {
        const varName = this.extractIdentifier(varDecl);
        const varType = this.extractType(varDecl);

        if (!varName || !varType) return;

        // 检查类型是否存在
        if (!this.symbolTable.hasType(varType)) {
            result.errors.push({
                line: this.getLine(varDecl),
                column: this.getColumn(varDecl),
                length: varType.length,
                message: `未知类型: ${varType}`,
                severity: 'error',
                code: 'unknown-type'
            });
        }

        // 检查初始值类型
        if (varDecl.children.expression) {
            const initExpr = varDecl.children.expression[0];
            const exprType = this.checkExpression(initExpr, result, options);
            
            if (exprType && !this.isTypeCompatible(exprType, varType)) {
                result.errors.push({
                    line: this.getLine(varDecl),
                    column: this.getColumn(varDecl),
                    length: varName.length,
                    message: `类型不匹配: 无法将 ${exprType} 赋值给 ${varType}`,
                    severity: 'error',
                    code: 'type-mismatch'
                });
            }
        }
    }

    private checkTypeDeclaration(typeDecl: any, result: CompileResult, options: CompileOptions): void {
        const typeName = this.extractIdentifier(typeDecl);
        const baseType = this.extractBaseType(typeDecl);

        if (!typeName || !baseType) return;

        // 检查基类型是否存在
        if (!this.symbolTable.hasType(baseType)) {
            result.errors.push({
                line: this.getLine(typeDecl),
                column: this.getColumn(typeDecl),
                length: baseType.length,
                message: `未知基类型: ${baseType}`,
                severity: 'error',
                code: 'unknown-base-type'
            });
        }
    }

    private checkParameters(funcDecl: any, result: CompileResult, options: CompileOptions): void {
        if (funcDecl.children.parameterList && funcDecl.children.parameterList[0]) {
            const paramList = funcDecl.children.parameterList[0];
            if (paramList.children.parameter) {
                paramList.children.parameter.forEach((param: any) => {
                    this.checkParameter(param, result, options);
                });
            }
        }
    }

    private checkParameter(param: any, result: CompileResult, options: CompileOptions): void {
        const paramName = this.extractIdentifier(param);
        const paramType = this.extractType(param);

        if (!paramName || !paramType) return;

        // 检查参数类型是否存在
        if (!this.symbolTable.hasType(paramType)) {
            result.errors.push({
                line: this.getLine(param),
                column: this.getColumn(param),
                length: paramType.length,
                message: `未知参数类型: ${paramType}`,
                severity: 'error',
                code: 'unknown-param-type'
            });
        }
    }

    private checkStatement(stmt: any, result: CompileResult, options: CompileOptions): void {
        if (!stmt.children) return;

        // 检查局部变量声明
        if (stmt.children.localDeclaration) {
            this.checkLocalDeclaration(stmt.children.localDeclaration[0], result, options);
        }

        // 检查set语句
        if (stmt.children.setStatement) {
            this.checkSetStatement(stmt.children.setStatement[0], result, options);
        }

        // 检查call语句
        if (stmt.children.callStatement) {
            this.checkCallStatement(stmt.children.callStatement[0], result, options);
        }

        // 检查return语句
        if (stmt.children.returnStatement) {
            this.checkReturnStatement(stmt.children.returnStatement[0], result, options);
        }

        // 检查if语句
        if (stmt.children.ifStatement) {
            this.checkIfStatement(stmt.children.ifStatement[0], result, options);
        }

        // 检查loop语句
        if (stmt.children.loopStatement) {
            this.checkLoopStatement(stmt.children.loopStatement[0], result, options);
        }

        // 检查exitwhen语句
        if (stmt.children.exitWhenStatement) {
            this.checkExitWhenStatement(stmt.children.exitWhenStatement[0], result, options);
        }
    }

    private checkLocalDeclaration(localDecl: any, result: CompileResult, options: CompileOptions): void {
        const varName = this.extractIdentifier(localDecl);
        const varType = this.extractType(localDecl);

        if (!varName || !varType) return;

        // 检查类型是否存在
        if (!this.symbolTable.hasType(varType)) {
            result.errors.push({
                line: this.getLine(localDecl),
                column: this.getColumn(localDecl),
                length: varType.length,
                message: `未知类型: ${varType}`,
                severity: 'error',
                code: 'unknown-type'
            });
        }

        // 检查变量名是否符合规范
        if (options.strictMode && !varName.startsWith('ydl_') && !varName.startsWith('ydul_')) {
            result.warnings.push({
                line: this.getLine(localDecl),
                column: this.getColumn(localDecl),
                length: varName.length,
                message: `建议使用 ydl_ 或 ydul_ 前缀命名局部变量: ${varName}`,
                severity: 'warning',
                code: 'naming-convention'
            });
        }

        // 检查初始值类型
        if (localDecl.children.expression) {
            const initExpr = localDecl.children.expression[0];
            const exprType = this.checkExpression(initExpr, result, options);
            
            if (exprType && !this.isTypeCompatible(exprType, varType)) {
                result.errors.push({
                    line: this.getLine(localDecl),
                    column: this.getColumn(localDecl),
                    length: varName.length,
                    message: `类型不匹配: 无法将 ${exprType} 赋值给 ${varType}`,
                    severity: 'error',
                    code: 'type-mismatch'
                });
            }
        }
    }

    private checkSetStatement(setStmt: any, result: CompileResult, options: CompileOptions): void {
        if (!setStmt.children.variableReference || !setStmt.children.expression) return;

        const varRef = setStmt.children.variableReference[0];
        const expr = setStmt.children.expression[0];

        const varName = this.extractIdentifier(varRef);
        if (!varName) return;

        // 检查变量是否存在
        const variable = this.symbolTable.getVariable(varName);
        if (!variable) {
            result.errors.push({
                line: this.getLine(setStmt),
                column: this.getColumn(setStmt),
                length: varName.length,
                message: `未声明的变量: ${varName}`,
                severity: 'error',
                code: 'undeclared-variable'
            });
            return;
        }

        // 检查是否试图修改常量
        if (variable.constant) {
            result.errors.push({
                line: this.getLine(setStmt),
                column: this.getColumn(setStmt),
                length: varName.length,
                message: `无法修改常量: ${varName}`,
                severity: 'error',
                code: 'modify-constant'
            });
        }

        // 检查赋值类型兼容性
        const exprType = this.checkExpression(expr, result, options);
        if (exprType && !this.isTypeCompatible(exprType, variable.type)) {
            result.errors.push({
                line: this.getLine(setStmt),
                column: this.getColumn(setStmt),
                length: varName.length,
                message: `类型不匹配: 无法将 ${exprType} 赋值给 ${variable.type}`,
                severity: 'error',
                code: 'type-mismatch'
            });
        }
    }

    private checkCallStatement(callStmt: any, result: CompileResult, options: CompileOptions): void {
        if (!callStmt.children.functionCall) return;

        const funcCall = callStmt.children.functionCall[0];
        this.checkFunctionCall(funcCall, result, options);
    }

    private checkFunctionCall(funcCall: any, result: CompileResult, options: CompileOptions): string | null {
        const funcName = this.extractIdentifier(funcCall);
        if (!funcName) return null;

        // 检查函数是否存在
        const func = this.symbolTable.getFunction(funcName);
        if (!func) {
            result.errors.push({
                line: this.getLine(funcCall),
                column: this.getColumn(funcCall),
                length: funcName.length,
                message: `未声明的函数: ${funcName}`,
                severity: 'error',
                code: 'undeclared-function'
            });
            return null;
        }

        // 检查参数
        this.checkFunctionArguments(funcCall, func, result, options);

        return func.returnType;
    }

    private checkFunctionArguments(funcCall: any, func: FunctionSymbol, result: CompileResult, options: CompileOptions): void {
        const args: any[] = [];
        
        if (funcCall.children.argumentList && funcCall.children.argumentList[0]) {
            const argList = funcCall.children.argumentList[0];
            if (argList.children.expression) {
                args.push(...argList.children.expression);
            }
        }

        // 检查参数数量
        if (args.length !== func.parameters.length) {
            result.errors.push({
                line: this.getLine(funcCall),
                column: this.getColumn(funcCall),
                length: func.name.length,
                message: `参数数量不匹配: ${func.name} 需要 ${func.parameters.length} 个参数，但提供了 ${args.length} 个`,
                severity: 'error',
                code: 'argument-count-mismatch'
            });
            return;
        }

        // 检查参数类型
        for (let i = 0; i < args.length; i++) {
            const argType = this.checkExpression(args[i], result, options);
            const expectedType = func.parameters[i].type;

            if (argType && !this.isTypeCompatible(argType, expectedType)) {
                result.errors.push({
                    line: this.getLine(args[i]),
                    column: this.getColumn(args[i]),
                    length: 1,
                    message: `参数类型不匹配: 第${i + 1}个参数期望 ${expectedType}，但得到 ${argType}`,
                    severity: 'error',
                    code: 'argument-type-mismatch'
                });
            }
        }
    }

    private checkReturnStatement(returnStmt: any, result: CompileResult, options: CompileOptions): void {
        // 这里需要检查返回值类型是否与函数声明匹配
        // 需要知道当前函数的返回类型
    }

    private checkIfStatement(ifStmt: any, result: CompileResult, options: CompileOptions): void {
        // 检查条件表达式
        if (ifStmt.children.expression) {
            const conditionType = this.checkExpression(ifStmt.children.expression[0], result, options);
            if (conditionType && conditionType !== 'boolean') {
                result.warnings.push({
                    line: this.getLine(ifStmt),
                    column: this.getColumn(ifStmt),
                    length: 1,
                    message: `if条件应该是boolean类型，但得到 ${conditionType}`,
                    severity: 'warning',
                    code: 'condition-type'
                });
            }
        }

        // 检查语句块
        if (ifStmt.children.statement) {
            ifStmt.children.statement.forEach((stmt: any) => {
                this.checkStatement(stmt, result, options);
            });
        }
    }

    private checkLoopStatement(loopStmt: any, result: CompileResult, options: CompileOptions): void {
        // 检查循环体中的语句
        if (loopStmt.children.statement) {
            loopStmt.children.statement.forEach((stmt: any) => {
                this.checkStatement(stmt, result, options);
            });
        }
    }

    private checkExitWhenStatement(exitWhenStmt: any, result: CompileResult, options: CompileOptions): void {
        // 检查exitwhen条件
        if (exitWhenStmt.children.expression) {
            const conditionType = this.checkExpression(exitWhenStmt.children.expression[0], result, options);
            if (conditionType && conditionType !== 'boolean') {
                result.warnings.push({
                    line: this.getLine(exitWhenStmt),
                    column: this.getColumn(exitWhenStmt),
                    length: 1,
                    message: `exitwhen条件应该是boolean类型，但得到 ${conditionType}`,
                    severity: 'warning',
                    code: 'condition-type'
                });
            }
        }
    }

    private checkExpression(expr: any, result: CompileResult, options: CompileOptions): string | null {
        if (!expr || !expr.children) return null;

        // 检查不同类型的表达式
        if (expr.children.orExpression) {
            return this.checkOrExpression(expr.children.orExpression[0], result, options);
        }

        return null;
    }

    private checkOrExpression(orExpr: any, result: CompileResult, options: CompileOptions): string | null {
        // 简化处理，实际需要完整的表达式类型推导
        return 'boolean';
    }

    private isTypeCompatible(sourceType: string, targetType: string): boolean {
        if (sourceType === targetType) return true;
        
        // 使用API数据库检查类型兼容性
        return this.apiDatabase.isTypeCompatible(sourceType, targetType);
    }

    // 辅助方法
    private extractIdentifier(node: any): string | null {
        if (node.children && node.children.Identifier && node.children.Identifier[0]) {
            return node.children.Identifier[0].image;
        }
        return null;
    }

    private extractType(node: any): string | null {
        if (node.children && node.children.typeReference) {
            return this.extractTypeFromReference(node.children.typeReference[0]);
        }
        return null;
    }

    private extractTypeFromReference(typeRef: any): string | null {
        if (!typeRef.children) return null;
        
        const typeTokens = [
            'IntegerType', 'RealType', 'BooleanType', 'StringType', 'HandleType', 'CodeType',
            'UnitType', 'PlayerType', 'LocationType', 'RectType', 'RegionType', 'TimerType',
            'TriggerType', 'GroupType', 'ForceType', 'EffectType', 'SoundType', 'FrameHandleType',
            'Nothing', 'Identifier'
        ];
        
        for (const tokenType of typeTokens) {
            if (typeRef.children[tokenType] && typeRef.children[tokenType][0]) {
                return typeRef.children[tokenType][0].image;
            }
        }
        
        return null;
    }

    private extractBaseType(node: any): string | null {
        if (node.children && node.children.typeReference) {
            const typeRefs = node.children.typeReference;
            if (typeRefs.length > 1) {
                return this.extractTypeFromReference(typeRefs[1]);
            }
        }
        return null;
    }

    private getLine(node: any): number {
        if (node.location && node.location.startLine) {
            return node.location.startLine;
        }
        return 1;
    }

    private getColumn(node: any): number {
        if (node.location && node.location.startColumn) {
            return node.location.startColumn;
        }
        return 1;
    }
}
