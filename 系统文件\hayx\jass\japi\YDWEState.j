//	globals
//		private constant integer ITEM_DATA_ART                  = 1
//		private constant integer ITEM_DATA_UBERTIP              = 3
//		private constant integer ITEM_DATA_NAME                 = 4
//
//		private constant integer BUFF_DATA_ART                  = 1
//		private constant integer BUFF_DATA_TIP                  = 2
//		private constant integer BUFF_DATA_UBERTIP              = 3
//	endglobals
//
//	native EXGetItemDataString takes integer itemcode, integer data_type returns string
//	native EXSetItemDataString takes integer itemcode, integer data_type, string value returns boolean
//	native EXGetBuffDataString takes integer buffcode, integer data_type returns string
//	native EXSetBuffDataString takes integer buffcode, integer data_type, string value returns boolean
