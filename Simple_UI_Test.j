//===========================================================================
// 基于HIVE教程的简单UI测试 - 使用你项目的DzAPI
//===========================================================================

globals
    trigger gg_trg_SimpleUI = null
    framehandle TestFrame = null
endglobals

//===========================================================================
// 基础API测试
//===========================================================================
function TestBasicAPI takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 基础API测试 ===|r")
    
    // 测试DzGetGameUI
    if DzGetGameUI() != null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ DzGetGameUI 可用: " + I2S(DzGetGameUI()) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ DzGetGameUI 不可用|r")
        return
    endif
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 基础测试通过|r")
endfunction

//===========================================================================
// 创建简单UI测试
//===========================================================================
function CreateSimpleUI takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 创建简单UI ===|r")
    
    // 创建一个简单的背景框架
    set TestFrame = DzCreateFrameByTagName("BACKDROP", "TestFrame", DzGetGameUI(), "", 0)
    
    if TestFrame != null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 框架创建成功: " + I2S(TestFrame) + "|r")
        
        // 设置大小和位置
        call DzFrameSetSize(TestFrame, 0.2, 0.1)
        call DzFrameSetAbsolutePoint(TestFrame, 4, 0.4, 0.3)
        
        // 设置纹理
        call DzFrameSetTexture(TestFrame, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
        
        // 显示框架
        call DzFrameShow(TestFrame, true)
        
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ UI创建完成，应该能看到一个灰色面板|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ 框架创建失败|r")
    endif
endfunction

//===========================================================================
// 切换UI显示
//===========================================================================
function ToggleUI takes nothing returns nothing
    if TestFrame != null then
        local boolean isVisible = DzFrameIsVisible(TestFrame)
        call DzFrameShow(TestFrame, not isVisible)
        
        if not isVisible then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[UI] 已显示|r")
        else
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[UI] 已隐藏|r")
        endif
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] 请先创建UI|r")
    endif
endfunction

//===========================================================================
// 隐藏原生UI（基于HIVE教程）
//===========================================================================
function HideNativeUI takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 隐藏原生UI ===|r")
    
    // 尝试隐藏黑色背景条
    local framehandle backdrop = DzGetFrameByName("ConsoleUIBackdrop", 0)
    if backdrop != null then
        call DzFrameShow(backdrop, false)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 隐藏了ConsoleUIBackdrop|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ 找不到ConsoleUIBackdrop|r")
    endif
endfunction

//===========================================================================
// 命令处理
//===========================================================================
function HandleCommands takes nothing returns nothing
    local string command = GetEventPlayerChatString()
    
    if command == "-test" then
        call TestBasicAPI()
    elseif command == "-create" then
        call CreateSimpleUI()
    elseif command == "-toggle" then
        call ToggleUI()
    elseif command == "-hide" then
        call HideNativeUI()
    elseif command == "-help" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 简单UI测试命令 ===|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-test    基础API测试|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-create  创建简单UI|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-toggle  切换UI显示|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-hide    隐藏原生UI|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-help    显示帮助|r")
    endif
endfunction

//===========================================================================
// 初始化函数
//===========================================================================
function InitTrig_SimpleUI takes nothing returns nothing
    set gg_trg_SimpleUI = CreateTrigger()
    
    call TriggerRegisterPlayerChatEvent(gg_trg_SimpleUI, Player(0), "-test", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_SimpleUI, Player(0), "-create", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_SimpleUI, Player(0), "-toggle", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_SimpleUI, Player(0), "-hide", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_SimpleUI, Player(0), "-help", true)
    
    call TriggerAddAction(gg_trg_SimpleUI, function HandleCommands)
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 简单UI测试系统已加载 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -help 查看命令|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700建议先输入 -test 测试基础功能|r")
endfunction

//===========================================================================
// 主函数
//===========================================================================
function main takes nothing returns nothing
    call InitTrig_SimpleUI()
endfunction
