# 🌟 高光效果备选方案

## 🎯 目标效果
实现真正的"高光"效果：**鼠标悬停时按钮变亮/发光**，而不是边框选择框。

## 🎨 方案选择

### **方案1：键盘高光效果（当前使用）**
```fdf
Frame "HIGHLIGHT" "CollectionSlotHighlightTemplate" {
    HighlightType "FILETEXTURE",
    HighlightAlphaFile "UI\\Widgets\\Glues\\GlueScreen-Button-KeyboardHighlight.blp",
    HighlightAlphaMode "ADD",
}
```
**效果**：柔和的发光效果，类似键盘焦点高光

### **方案2：人族按钮高光**
```fdf
Frame "HIGHLIGHT" "CollectionSlotHighlightTemplate" {
    HighlightType "FILETEXTURE",
    HighlightAlphaFile "UI\\Widgets\\Console\\Human\\human-inventory-button-background.blp",
    HighlightAlphaMode "ADD",
}
```
**效果**：使用人族风格的亮色按钮背景叠加

### **方案3：命令按钮激活高光**
```fdf
Frame "HIGHLIGHT" "CollectionSlotHighlightTemplate" {
    HighlightType "FILETEXTURE",
    HighlightAlphaFile "UI\\Widgets\\Console\\CommandButton\\CommandButtonActiveHighlight.blp",
    HighlightAlphaMode "ADD",
}
```
**效果**：类似技能按钮激活时的高光效果

### **方案4：纯色发光效果**
```fdf
Frame "HIGHLIGHT" "CollectionSlotHighlightTemplate" {
    HighlightType "SHADE",
    HighlightColor 1.0 1.0 0.8 0.3,  // 淡黄色发光
}
```
**效果**：纯色叠加，可以自定义颜色和透明度

### **方案5：英雄选择高光**
```fdf
Frame "HIGHLIGHT" "CollectionSlotHighlightTemplate" {
    HighlightType "FILETEXTURE",
    HighlightAlphaFile "UI\\Widgets\\Glues\\SinglePlayerSkirmish\\human-race-button-highlight.blp",
    HighlightAlphaMode "ADD",
}
```
**效果**：英雄选择界面的高光效果

## 🔧 如何更换方案

1. **打开文件**：`UI界面模板\war3mapImported\CollectionPanel.fdf`

2. **找到高光模板**：
   ```fdf
   Frame "HIGHLIGHT" "CollectionSlotHighlightTemplate" {
       HighlightType "FILETEXTURE",
       HighlightAlphaFile "当前贴图路径",
       HighlightAlphaMode "ADD",
   }
   ```

3. **替换HighlightAlphaFile**：将路径替换为上述任一方案的路径

4. **同时修改开关按钮**：
   ```fdf
   Frame "HIGHLIGHT" "ToggleButtonHighlightTemplate" {
       // 使用相同的设置
   }
   ```

## 🎨 颜色发光方案详解

如果选择方案4（纯色发光），可以调整颜色：

```fdf
// 金色发光
HighlightColor 1.0 0.8 0.2 0.3,

// 蓝色发光  
HighlightColor 0.3 0.7 1.0 0.3,

// 绿色发光
HighlightColor 0.3 1.0 0.3 0.3,

// 红色发光
HighlightColor 1.0 0.3 0.3 0.3,

// 白色发光（最亮）
HighlightColor 1.0 1.0 1.0 0.4,
```

**颜色格式**：`R G B A`（红 绿 蓝 透明度，范围0.0-1.0）

## 🧪 测试建议

1. **先测试方案1**（当前）- 最安全的选择
2. **如果效果不明显**，尝试方案3或方案4
3. **如果想要自定义颜色**，使用方案4
4. **如果想要游戏原生感觉**，尝试方案2或方案5

## 📝 注意事项

- **路径使用双反斜杠** `\\`
- **HighlightAlphaMode "ADD"** 是加法混合，让效果叠加变亮
- **如果效果太强**，可以尝试 `"BLEND"` 模式
- **测试时注意**：有些贴图可能在某些版本中不存在

选择你喜欢的方案，我可以帮你应用！
