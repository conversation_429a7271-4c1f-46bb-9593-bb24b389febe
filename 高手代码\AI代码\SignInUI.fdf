﻿Frame "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" "HeroSelectorButton" {
    Width 0.30,
    Height 0.30,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
    ControlBackdrop "HeroSelectorButtonBackdrop",
    Frame "BAC<PERSON>DROP" "HeroSelectorButtonBackdrop" {
        BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.012,
        BackdropBackgroundSize 0.032,
        BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
        BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
    }
    Frame "BACKDROP" "HeroSelectorButtonIcon" {
        Width 0.10,
        Height 0.10,
        BackdropBackground "ReplaceableTextures\CommandButtons\BTNArthas.blp",
    }
}
