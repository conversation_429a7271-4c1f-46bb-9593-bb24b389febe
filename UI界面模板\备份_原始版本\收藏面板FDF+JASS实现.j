globals
    // 全局变量：存储 UI 框架和触发器
    integer PanelFrame = 0              // 主背景框架（带边框的面板）
    integer TitleFrame = 0              // 大标题（"收藏面板"）
    integer DetailTitleFrame = 0        // 小标题（"详情"）
    integer DetailFrame = 0             // 详情文本区域
    integer ToggleButton = 0            // 开关按钮
    integer ToggleButtonBackdrop = 0    // 开关按钮背景
    integer array CollectionSlots[16]   // 16 个收藏槽按钮
    integer array CollectionSlotBackdrops[16] // 16 个收藏槽图标背景
    trigger EscTrigger = null           // ESC 键触发器
    trigger ToggleTrigger = null        // 开关按钮触发器
    trigger array HoverTriggers[16]     // 每个收藏槽的悬停触发器
    boolean IsPanelVisible = false      // 面板是否可见
endglobals

// 更新详情文本：鼠标悬停时显示收藏品描述
function UpdateDetail takes integer slot returns nothing
    call DzFrameSetText(DetailFrame, udg_CollectionDescriptions[slot]) // 从 GUI 数组读取描述
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "显示详情: 收藏品 " + I2S(slot))
endfunction

// 鼠标悬停事件：检测哪个槽被悬停并显示描述
function OnSlotHover takes nothing returns nothing
    call YDWELocalVariableInitiliation() // 初始化 YDWE 局部变量作用域
    call YDWESetLocalVariableInteger("frame", DzGetTriggerUIEventFrame()) // 存储当前触发框架
    call YDWESetLocalVariableInteger("i", 0) // 循环计数器
    loop
        exitwhen YDWEGetLocalVariableInteger("i") >= 16
        if CollectionSlots[YDWEGetLocalVariableInteger("i")] == YDWEGetLocalVariableInteger("frame") then
            call UpdateDetail(YDWEGetLocalVariableInteger("i"))
            call YDWELocalVariableEnd()
            return
        endif
        call YDWESetLocalVariableInteger("i", YDWEGetLocalVariableInteger("i") + 1)
    endloop
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "未找到匹配槽: " + I2S(YDWEGetLocalVariableInteger("frame")))
    call YDWELocalVariableEnd()
endfunction

// 鼠标离开事件：重置详情文本
function OnSlotLeave takes nothing returns nothing
    call DzFrameSetText(DetailFrame, "选择一个收藏品查看详情") // 重置为默认文本
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "详情已重置")
endfunction

// 开关面板：控制显示/隐藏
function TogglePanel takes nothing returns nothing
    call YDWELocalVariableInitiliation()
    call YDWESetLocalVariableInteger("i", 0)
    if IsPanelVisible then
        // 关闭面板：只保留大标题和小标题
        call DzFrameShow(PanelFrame, false)
        call DzFrameShow(DetailFrame, false)
        call DzFrameShow(ToggleButton, true) // 开关按钮始终可见
        call DzFrameShow(ToggleButtonBackdrop, true)
        loop
            exitwhen YDWEGetLocalVariableInteger("i") >= 16
            call DzFrameShow(CollectionSlots[YDWEGetLocalVariableInteger("i")], false)
            call DzFrameShow(CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")], false)
            call YDWESetLocalVariableInteger("i", YDWEGetLocalVariableInteger("i") + 1)
        endloop
        call DzFrameShow(TitleFrame, true)
        call DzFrameShow(DetailTitleFrame, true)
        set IsPanelVisible = false
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "收藏面板已关闭，仅保留标题")
    else
        // 打开面板：显示所有内容
        call DzFrameShow(PanelFrame, true)
        call DzFrameShow(TitleFrame, true)
        call DzFrameShow(DetailTitleFrame, true)
        call DzFrameShow(DetailFrame, true)
        call DzFrameShow(ToggleButton, true)
        call DzFrameShow(ToggleButtonBackdrop, true)
        loop
            exitwhen YDWEGetLocalVariableInteger("i") >= 16
            call DzFrameShow(CollectionSlots[YDWEGetLocalVariableInteger("i")], true)
            call DzFrameShow(CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")], true)
            call YDWESetLocalVariableInteger("i", YDWEGetLocalVariableInteger("i") + 1)
        endloop
        set IsPanelVisible = true
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "收藏面板已显示")
    endif
    call YDWELocalVariableEnd()
endfunction

// 初始化收藏面板：创建所有 UI 元素并设置固定大小和位置
function InitCollectionPanel takes nothing returns nothing
    call YDWELocalVariableInitiliation()
    call YDWESetLocalVariableInteger("i", 0)              // 循环计数器
    call YDWESetLocalVariableInteger("gameUI", DzGetGameUI()) // 游戏 UI 框架
    call YDWESetLocalVariableInteger("heroButton", DzFrameGetHeroBarButton(0)) // 第一个英雄按钮
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "开始初始化收藏面板...")
    
    // 加载 FDF 文件：定义 UI 样式
    call DzLoadToc("war3mapImported\\CustomButton.toc") // 修改路径需确保 FDF 文件存在
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "FDF 文件已加载: war3mapImported\\CustomButton.toc")
    
    // 创建主背景框架（固定大小）
    set PanelFrame = DzCreateFrameByTagName("BACKDROP", "CollectionPanelBackdropInstance", YDWEGetLocalVariableInteger("gameUI"), "CollectionPanelBackdrop", 0)
    if PanelFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "PanelFrame 创建失败，检查 FDF 和 TOC")
        call YDWELocalVariableEnd()
        return
    endif
    call DzFrameSetSize(PanelFrame, 0.4, 0.3) // 固定大小，宽度 0.4，高度 0.3（可调整）
    call DzFrameSetAbsolutePoint(PanelFrame, 4, 0.4, 0.32) // 固定位置，屏幕中心偏上（X: 0.4, Y: 0.32，可调整）
    call DzFrameShow(PanelFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "PanelFrame 创建: " + I2S(PanelFrame))

    // 创建大标题（固定大小和相对位置）
    set TitleFrame = DzCreateFrameByTagName("TEXT", "CollectionTitleInstance", PanelFrame, "CollectionTitle", 0)
    if TitleFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "TitleFrame 创建失败")
        call YDWELocalVariableEnd()
        return
    endif
    call DzFrameSetSize(TitleFrame, 0.2, 0.03) // 固定大小，宽度 0.2，高度 0.03（可调整）
    call DzFrameSetFont(TitleFrame, "fonts.ttf", 0.020, 0) // 字体和大小可调整
    call DzFrameSetText(TitleFrame, "收藏面板") // 可改为其他文字
    call DzFrameSetPoint(TitleFrame, 1, PanelFrame, 1, 0.0, -0.01) // 相对主框架顶部，Y 偏移 -0.01（可调整）
    call DzFrameShow(TitleFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "TitleFrame 创建: " + I2S(TitleFrame) + ", 文字: " + DzFrameGetText(TitleFrame))

    // 创建 16 个收藏槽（固定大小和相对位置）
    loop
        exitwhen YDWEGetLocalVariableInteger("i") >= 16
        set CollectionSlots[YDWEGetLocalVariableInteger("i")] = DzCreateFrameByTagName("BUTTON", "CollectionSlot" + I2S(YDWEGetLocalVariableInteger("i")), PanelFrame, "", 0)
        if CollectionSlots[YDWEGetLocalVariableInteger("i")] == 0 then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "CollectionSlot " + I2S(YDWEGetLocalVariableInteger("i")) + " 创建失败")
            call YDWELocalVariableEnd()
            return
        endif
        set CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")] = DzCreateFrameByTagName("BACKDROP", "CollectionSlotBackdrop" + I2S(YDWEGetLocalVariableInteger("i")), CollectionSlots[YDWEGetLocalVariableInteger("i")], "", 0)
        if CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")] == 0 then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "CollectionSlotBackdrop " + I2S(YDWEGetLocalVariableInteger("i")) + " 创建失败")
            call YDWELocalVariableEnd()
            return
        endif
        
        call DzFrameSetSize(CollectionSlots[YDWEGetLocalVariableInteger("i")], 0.05, 0.05) // 固定大小，可调整
        call DzFrameSetSize(CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")], 0.05, 0.05)
        // 位置：相对主框架左上角，X: 0.01 + 横向间隔，Y: -0.07 + 纵向间隔（可调整）
        call DzFrameSetPoint(CollectionSlots[YDWEGetLocalVariableInteger("i")], 0, PanelFrame, 0, 0.01 + (YDWEGetLocalVariableInteger("i") - (YDWEGetLocalVariableInteger("i") / 4) * 4) * 0.055, -0.07 - (YDWEGetLocalVariableInteger("i") / 4) * 0.055)
        call DzFrameSetPoint(CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")], 4, CollectionSlots[YDWEGetLocalVariableInteger("i")], 4, 0.0, 0.0)
        
        if udg_CollectionIcons[YDWEGetLocalVariableInteger("i")] == null or udg_CollectionIcons[YDWEGetLocalVariableInteger("i")] == "" then
            call DzFrameSetTexture(CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")], "ReplaceableTextures\\CommandButtons\\BTNPeasant.blp", 0) // 默认图标，可改
        else
            call DzFrameSetTexture(CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")], udg_CollectionIcons[YDWEGetLocalVariableInteger("i")], 0)
        endif
        
        call DzFrameShow(CollectionSlots[YDWEGetLocalVariableInteger("i")], false)
        call DzFrameShow(CollectionSlotBackdrops[YDWEGetLocalVariableInteger("i")], false)
        
        set HoverTriggers[YDWEGetLocalVariableInteger("i")] = CreateTrigger()
        call TriggerAddAction(HoverTriggers[YDWEGetLocalVariableInteger("i")], function OnSlotHover)
        call DzFrameSetScriptByCode(CollectionSlots[YDWEGetLocalVariableInteger("i")], 2, function OnSlotHover, false)
        call DzFrameSetScriptByCode(CollectionSlots[YDWEGetLocalVariableInteger("i")], 3, function OnSlotLeave, false)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "CollectionSlot " + I2S(YDWEGetLocalVariableInteger("i")) + " 创建: " + I2S(CollectionSlots[YDWEGetLocalVariableInteger("i")]))
        call YDWESetLocalVariableInteger("i", YDWEGetLocalVariableInteger("i") + 1)
    endloop

    // 创建小标题（固定大小和相对位置）
    set DetailTitleFrame = DzCreateFrameByTagName("TEXT", "CollectionDetailTitleInstance", PanelFrame, "CollectionDetailTitle", 0)
    if DetailTitleFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "DetailTitleFrame 创建失败")
        call YDWELocalVariableEnd()
        return
    endif
    call DzFrameSetSize(DetailTitleFrame, 0.12, 0.03) // 固定大小，可调整
    call DzFrameSetFont(DetailTitleFrame, "fonts.ttf", 0.020, 0) // 字体大小可调整
    call DzFrameSetText(DetailTitleFrame, "详情") // 可改文字
    call DzFrameSetPoint(DetailTitleFrame, 5, CollectionSlots[0], 5, 0.30, 0.0) // 右对齐第一列图标，X 偏移 0.02（可调整）
    call DzFrameShow(DetailTitleFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "DetailTitleFrame 创建: " + I2S(DetailTitleFrame) + ", 文字: " + DzFrameGetText(DetailTitleFrame))

    // 创建详情文本（固定大小和相对位置）
    set DetailFrame = DzCreateFrameByTagName("TEXT", "CollectionDetailTextInstance", PanelFrame, "CollectionDetailText", 0)
    if DetailFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "DetailFrame 创建失败")
        call YDWELocalVariableEnd()
        return
    endif
    call DzFrameSetSize(DetailFrame, 0.12, 0.17) // 固定大小，可调整
    call DzFrameSetFont(DetailFrame, "fonts.ttf", 0.014, 0) // 字体大小可调整
    call DzFrameSetText(DetailFrame, "选择一个收藏品查看详情") // 默认文本，可改
    call DzFrameSetPoint(DetailFrame, 2, CollectionSlots[0], 5, 0.30, -0.01) // 右对齐第一列图标，X 偏移 0.02，Y 偏移 -0.01（可调整）
    call DzFrameShow(DetailFrame, false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "DetailFrame 创建: " + I2S(DetailFrame) + ", 文字: " + DzFrameGetText(DetailFrame))

    // 创建开关按钮（固定大小和位置）
    set ToggleButton = DzCreateFrameByTagName("GLUEBUTTON", "ToggleButtonInstance", YDWEGetLocalVariableInteger("gameUI"), "ToggleButton", 0)
    if ToggleButton == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "ToggleButton 创建失败")
        call YDWELocalVariableEnd()
        return
    endif
    call DzFrameSetSize(ToggleButton, 0.04, 0.04) // 固定大小，可调整
    call DzFrameSetAbsolutePoint(ToggleButton, 3, 0.08, 0.50) // 固定位置，屏幕左上角（X: 0.15, Y: 0.40，可调整）
    call DzFrameShow(ToggleButton, true)
    set ToggleButtonBackdrop = DzFrameFindByName("ToggleButtonBackdrop", 0)
    if ToggleButtonBackdrop == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "ToggleButtonBackdrop 未找到，尝试手动创建")
        set ToggleButtonBackdrop = DzCreateFrameByTagName("BACKDROP", "ToggleButtonBackdropManual", ToggleButton, "", 0)
        call DzFrameSetSize(ToggleButtonBackdrop, 0.04, 0.04) // 固定大小，可调整
        call DzFrameSetPoint(ToggleButtonBackdrop, 4, ToggleButton, 4, 0.0, 0.0)
        call DzFrameSetTexture(ToggleButtonBackdrop, "UI\\Widgets\\EscMenu\\Human\\human-options-button-background.blp", 0) // 图标可改
    endif
    call DzFrameShow(ToggleButtonBackdrop, true)
    
    // 设置触发器
    set ToggleTrigger = CreateTrigger()
    call TriggerAddAction(ToggleTrigger, function TogglePanel)
    call DzFrameSetScriptByCode(ToggleButton, 1, function TogglePanel, false)

    set EscTrigger = CreateTrigger()
    call DzTriggerRegisterKeyEvent(EscTrigger, 27, 0, true, null)
    call TriggerAddAction(EscTrigger, function TogglePanel)
    
    set IsPanelVisible = false
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "收藏面板初始化完成，点击开关按钮或按 ESC 控制显示")
    call YDWELocalVariableEnd()
endfunction

// 主函数：地图启动时调用
function Main takes nothing returns nothing
    local trigger t = CreateTrigger()
    call TriggerRegisterTimerEvent(t, 0.10, false) // 延迟 0.10 秒初始化，可调整时间
    call TriggerAddAction(t, function InitCollectionPanel)
    set t = null
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "Main 函数已调用")
    // 备注：此处不再监听窗口大小变化，因为 UI 已固定大小
endfunction
