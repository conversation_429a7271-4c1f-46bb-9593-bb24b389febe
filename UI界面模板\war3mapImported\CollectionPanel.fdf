// 主面板容器 - 固定尺寸，不随屏幕缩放
Frame "FRAME" "CollectionPanelContainer" {
    Width 0.35,   // 减小宽度，避免宽屏拉伸
    Height 0.28,  // 稍微减小高度

    // 阴影层 - 在主背景下方
    Frame "BACKDROP" "CollectionPanelShadow" {
        Width 0.35,
        Height 0.28,
        SetPoint TOPLEFT, "CollectionPanelContainer", TOPLEFT, 0.003, -0.003,  // 右下偏移创造阴影
        BackdropTileBackground,
        BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
        BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-options-menu-border.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.016,
        BackdropBackgroundInsets 0.006 0.006 0.006 0.006,
        AlphaMode "BLEND",  // 半透明阴影效果
    }

    // 主背景层 - 在阴影上方
    Frame "BACKDROP" "CollectionPanelBackdrop" {
        Width 0.35,
        Height 0.28,
        SetPoint TOPLEFT, "CollectionPanelContainer", TOPLEFT, 0, 0,
        BackdropTileBackground,
        BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
        BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-options-menu-border.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.016,
        BackdropBackgroundInsets 0.006 0.006 0.006 0.006,
    }
}

Frame "TEXT" "CollectionTitle" {
    DecorateFileNames,
    Width 0.2,
    Height 0.03,
    FrameFont "MasterFont", 0.015, "",
    FontColor 0.9 0.7 1.0 1.0,  // 淡紫色标题，配合亡灵风格
    FontShadowOffset 0.002 -0.002,
    FontShadowColor 0.1 0.0 0.2 1.0,  // 深紫色阴影
    FontJustificationH JUSTIFYCENTER,
    FontJustificationV JUSTIFYMIDDLE,
}

Frame "TEXT" "CollectionDetailTitle" {
    DecorateFileNames,
    Width 0.12,
    Height 0.03,
    FrameFont "MasterFont", 0.012, "",
    FontColor 0.8 0.9 1.0 1.0,  // 冰蓝色副标题
    FontShadowOffset 0.002 -0.002,
    FontShadowColor 0.1 0.0 0.3 1.0,  // 深紫色阴影
    FontJustificationH JUSTIFYLEFT,
    FontJustificationV JUSTIFYTOP,
}

Frame "TEXT" "CollectionDetailText" {
    DecorateFileNames,
    Width 0.12,
    Height 0.17,
    FrameFont "MasterFont", 0.01, "",
    FontColor 0.8 0.8 0.9 1.0,  // 淡灰紫色正文
    FontShadowOffset 0.001 -0.001,
    FontShadowColor 0.1 0.0 0.2 1.0,  // 深紫色阴影
    FontJustificationH JUSTIFYLEFT,
    FontJustificationV JUSTIFYTOP,
}

// 收藏槽按钮 - 基于HIVE教程的完整BUTTON实现
Frame "BUTTON" "CollectionSlotButton" {
    Width 0.039,
    Height 0.039,

    // 正确的ControlStyle - 基于HIVE教程
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",

    // 设置正常状态背景
    ControlBackdrop "CollectionSlotButtonBack",
    Frame "BACKDROP" "CollectionSlotButtonBack" {
        BackdropBackground "UI\Widgets\Console\Undead\undead-inventory-button-background.blp",
        BackdropBlendAll,
    }

    // 鼠标悬停高光 - 亡灵族紫色魔法高光
    ControlMouseOverHighlight "CollectionSlotButtonHighlight",
    Frame "HIGHLIGHT" "CollectionSlotButtonHighlight" {
        HighlightType "FILETEXTURE",
        HighlightAlphaFile "UI\Widgets\EscMenu\Undead\undead-options-button-highlight.blp",
        HighlightAlphaMode "ADD",
    }

    // 按钮图标子框架
    Frame "BACKDROP" "CollectionSlotIcon" {
        SetAllPoints,
        BackdropBackground "ReplaceableTextures\CommandButtons\BTNSelectHeroOn.blp",
    }
}

// 开关按钮 - 基于HIVE教程的完整BUTTON实现
Frame "BUTTON" "ToggleButton" {
    Width 0.04,
    Height 0.04,

    // 正确的ControlStyle - 基于HIVE教程
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",

    // 设置正常状态背景
    ControlBackdrop "ToggleButtonBack",
    Frame "BACKDROP" "ToggleButtonBack" {
        BackdropBackground "UI\Widgets\Console\Undead\undead-inventory-button-background.blp",
        BackdropBlendAll,
    }

    // 鼠标悬停高光 - 亡灵族紫色魔法高光
    ControlMouseOverHighlight "ToggleButtonHighlight",
    Frame "HIGHLIGHT" "ToggleButtonHighlight" {
        HighlightType "FILETEXTURE",
        HighlightAlphaFile "UI\Widgets\EscMenu\Undead\undead-options-button-highlight.blp",
        HighlightAlphaMode "ADD",
    }

    // 按钮图标子框架
    Frame "BACKDROP" "ToggleButtonIcon" {
        SetAllPoints,
        BackdropBackground "ReplaceableTextures\CommandButtons\BTNCancel.blp",
    }
}
