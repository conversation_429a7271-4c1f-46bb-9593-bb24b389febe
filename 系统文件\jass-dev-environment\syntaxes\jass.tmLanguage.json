{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "JASS", "patterns": [{"include": "#comments"}, {"include": "#keywords"}, {"include": "#functions"}, {"include": "#types"}, {"include": "#strings"}, {"include": "#numbers"}, {"include": "#operators"}, {"include": "#ydwe-api"}, {"include": "#dz-api"}], "repository": {"comments": {"patterns": [{"name": "comment.line.double-slash.jass", "begin": "//", "end": "$"}]}, "keywords": {"patterns": [{"name": "keyword.control.jass", "match": "\\b(if|then|else|elseif|endif|loop|endloop|exitwhen|return|and|or|not)\\b"}, {"name": "keyword.other.jass", "match": "\\b(function|endfunction|takes|returns|nothing|local|set|call|type|extends|array|constant|native|globals|endglobals)\\b"}, {"name": "constant.language.jass", "match": "\\b(true|false|null)\\b"}]}, "functions": {"patterns": [{"name": "entity.name.function.jass", "match": "\\b([A-Za-z_][A-Za-z0-9_]*)\\s*(?=\\()"}, {"name": "entity.name.function.definition.jass", "begin": "\\b(function)\\s+([A-Za-z_][A-Za-z0-9_]*)", "beginCaptures": {"1": {"name": "keyword.other.jass"}, "2": {"name": "entity.name.function.jass"}}, "end": "\\b(endfunction)\\b", "endCaptures": {"1": {"name": "keyword.other.jass"}}}]}, "types": {"patterns": [{"name": "storage.type.jass", "match": "\\b(integer|real|boolean|string|handle|unit|player|location|region|rect|sound|effect|unitpool|itempool|timer|trigger|triggercondition|triggeraction|event|force|group|lightning|weathereffect|terraindeformation|quest|questitem|defeatcondition|timerdialog|leaderboard|multiboard|multiboarditem|trackable|dialog|button|texttag|image|ubersplat)\\b"}]}, "strings": {"patterns": [{"name": "string.quoted.double.jass", "begin": "\"", "end": "\"", "patterns": [{"name": "constant.character.escape.jass", "match": "\\\\."}]}]}, "numbers": {"patterns": [{"name": "constant.numeric.jass", "match": "\\b\\d+(\\.\\d+)?\\b"}, {"name": "constant.numeric.hex.jass", "match": "\\b0x[0-9A-Fa-f]+\\b"}]}, "operators": {"patterns": [{"name": "keyword.operator.jass", "match": "(\\+|\\-|\\*|\\/|\\%|\\=|\\!\\=|\\<|\\>|\\<\\=|\\>\\=)"}]}, "ydwe-api": {"patterns": [{"name": "support.function.ydwe.jass", "match": "\\b(YDTriggerRegisterEnterRectSimple|YDTriggerRegisterLeaveRectSimple|YDLocal1Set|YDLocal1Get|YDLocal1Release|YDLocalInitialize|YDUserDataSet|YDUserDataGet|YDUserDataClear|YDWETimerDestroyEffect|YDWECreateEffectOnUnit|YDWEGetItemLevel|YDWEGetItemLife|YDWESetItemLife|YDWEGetUnitAbilityLevel|YDWEGetUnitAbilityDataReal)\\b"}]}, "dz-api": {"patterns": [{"name": "support.function.dzapi.jass", "match": "\\b(DzFrameGetParent|DzFrameSetPoint|DzFrameSetSize|DzCreateFrameByTagName|DzFrameSetTexture|DzFrameShow|DzTriggerRegisterSyncData|DzGetTriggerSyncData|DzSyncData|DzFrameSetText|DzFrameGetText|DzFrameSetTextAlignment)\\b"}]}}, "scopeName": "source.jass"}