{"name": "jass-full-compiler", "displayName": "JASS Full Compiler", "description": "完整的JASS编译器 - 像地图编辑器一样的严格代码验证", "version": "1.0.0", "publisher": "jass-compiler", "engines": {"vscode": "^1.74.0"}, "categories": ["Programming Languages", "Linters", "Compilers"], "keywords": ["jass", "warcraft3", "compiler", "魔兽争霸3", "编译器"], "activationEvents": ["onLanguage:jass"], "main": "./out/extension.js", "contributes": {"languages": [{"id": "jass", "aliases": ["JASS", "jass"], "extensions": [".j", ".ai"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "jass", "scopeName": "source.jass", "path": "./syntaxes/jass.tmLanguage.json"}], "commands": [{"command": "jass.compile", "title": "编译JASS代码", "category": "JASS", "icon": "$(play)"}, {"command": "jass.validateProject", "title": "验证整个项目", "category": "JASS", "icon": "$(check-all)"}, {"command": "jass.showCompileOutput", "title": "显示编译输出", "category": "JASS", "icon": "$(output)"}], "keybindings": [{"command": "jass.compile", "key": "ctrl+shift+b", "when": "editorTextFocus && resourceExtname == .j"}, {"command": "jass.validateProject", "key": "ctrl+shift+alt+b", "when": "resourceExtname == .j"}], "menus": {"editor/context": [{"when": "resourceExtname == .j", "command": "jass.compile", "group": "jass@1"}, {"when": "resourceExtname == .j", "command": "jass.validateProject", "group": "jass@2"}], "editor/title": [{"when": "resourceExtname == .j", "command": "jass.compile", "group": "navigation"}]}, "configuration": {"type": "object", "title": "JASS Full Compiler", "properties": {"jass.compiler.enabled": {"type": "boolean", "default": true, "description": "启用JASS完整编译器"}, "jass.compiler.warcraft3Version": {"type": "string", "enum": ["1.27", "1.31", "1.32"], "default": "1.27", "description": "魔兽争霸3版本"}, "jass.compiler.strictMode": {"type": "boolean", "default": true, "description": "严格模式（像地图编辑器一样严格）"}, "jass.compiler.enableYDWE": {"type": "boolean", "default": true, "description": "启用YDWE API支持"}, "jass.compiler.enableDZAPI": {"type": "boolean", "default": true, "description": "启用DZAPI支持"}, "jass.compiler.enableJassHelper": {"type": "boolean", "default": false, "description": "启用JASS Helper扩展语法"}, "jass.compiler.showDetailedErrors": {"type": "boolean", "default": true, "description": "显示详细的错误信息"}, "jass.compiler.autoCompileOnSave": {"type": "boolean", "default": true, "description": "保存时自动编译"}, "jass.compiler.treatWarningsAsErrors": {"type": "boolean", "default": false, "description": "将警告视为错误"}}}, "problemMatchers": [{"name": "jass", "owner": "jass", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}], "taskDefinitions": [{"type": "jass", "required": ["task"], "properties": {"task": {"type": "string", "description": "JASS编译任务类型"}, "file": {"type": "string", "description": "要编译的JASS文件"}}}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "vsce": "^2.15.0"}, "dependencies": {"chevrotain": "^10.4.2"}, "repository": {"type": "git", "url": "https://github.com/jass-compiler/vscode-extension"}, "license": "MIT"}