/**
 * 用户JASS项目分析器 - 严格基于用户实际项目
 * 只从用户的实际文件中学习，不使用任何预设知识
 */

const fs = require('fs');
const path = require('path');

class UserProjectAnalyzer {
    constructor() {
        // 从用户项目中提取的所有定义
        this.userFunctions = new Map();      // 用户定义的函数
        this.userGlobals = new Map();        // 用户全局变量
        this.userNatives = new Map();        // 用户native函数
        this.userTypes = new Set();          // 用户使用的类型
        this.userConstants = new Map();      // 用户常量
        
        // 分析进度
        this.analyzedFiles = [];
        this.totalFiles = 0;
        this.isComplete = false;
    }
    
    /**
     * 开始分析用户项目
     */
    analyzeUserProject() {
        console.log('=== 开始系统性分析用户JASS项目 ===');
        
        // 第一步：分析主地图文件
        this.analyzeMainMapFile();
        
        // 第二步：分析所有JASS库文件
        this.analyzeAllJassFiles();
        
        // 第三步：生成分析报告
        this.generateAnalysisReport();
        
        this.isComplete = true;
        console.log('=== 项目分析完成 ===');
    }
    
    /**
     * 分析主地图文件
     */
    analyzeMainMapFile() {
        const mapFile = '../hayx/map/war3map.j';
        console.log('分析主地图文件: war3map.j');
        
        if (fs.existsSync(mapFile)) {
            this.analyzeFile(mapFile, 'war3map.j');
        } else {
            console.log('警告: 找不到主地图文件');
        }
    }
    
    /**
     * 分析所有JASS库文件
     */
    analyzeAllJassFiles() {
        console.log('分析JASS库文件...');
        
        // 分析主要JASS文件
        const jassFiles = [
            'AttackShockwave.j',
            'BlizzardAPI.j', 
            'DzAPI.j',
            'YDWEBase.j',
            'Base/YDWEBase_common.j',
            'Base/YDWEBase_hashtable.j',
            'YDWELocalVariable.j',
            'YDWETimerSystem.j',
            'YDWEEventDamageData.j',
            'YDWEMemoryLeakHelper.j'
        ];
        
        for (const file of jassFiles) {
            const fullPath = `../hayx/jass/${file}`;
            if (fs.existsSync(fullPath)) {
                console.log(`  分析: ${file}`);
                this.analyzeFile(fullPath, file);
            }
        }
        
        // 递归分析其他目录
        this.analyzeDirectory('../hayx/jass');
    }
    
    /**
     * 递归分析目录
     */
    analyzeDirectory(dirPath) {
        try {
            if (!fs.existsSync(dirPath)) return;
            
            const files = fs.readdirSync(dirPath);
            
            for (const file of files) {
                const fullPath = path.join(dirPath, file);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    this.analyzeDirectory(fullPath);
                } else if (file.endsWith('.j') && !this.analyzedFiles.includes(fullPath)) {
                    this.analyzeFile(fullPath, file);
                }
            }
        } catch (error) {
            console.log(`分析目录失败 ${dirPath}:`, error.message);
        }
    }
    
    /**
     * 分析单个文件
     */
    analyzeFile(filePath, fileName) {
        try {
            if (this.analyzedFiles.includes(filePath)) return;
            
            const content = fs.readFileSync(filePath, 'utf8');
            this.extractAllDefinitions(content, fileName);
            this.analyzedFiles.push(filePath);
            this.totalFiles++;
            
        } catch (error) {
            console.log(`分析文件失败 ${filePath}:`, error.message);
        }
    }
    
    /**
     * 从文件内容中提取所有定义
     */
    extractAllDefinitions(content, sourceFile) {
        const lines = content.split('\n');
        let inGlobals = false;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const lineNumber = i + 1;
            
            // 跳过空行和注释
            if (line === '' || line.startsWith('//') || line.startsWith('#')) {
                continue;
            }
            
            // 检查globals块
            if (line === 'globals') {
                inGlobals = true;
                continue;
            }
            if (line === 'endglobals') {
                inGlobals = false;
                continue;
            }
            
            // 提取函数定义
            if (line.startsWith('function ')) {
                this.extractFunction(line, sourceFile, lineNumber);
            }
            
            // 提取native函数
            if (line.startsWith('native ')) {
                this.extractNative(line, sourceFile, lineNumber);
            }
            
            // 提取全局变量
            if (inGlobals || this.isGlobalVariable(line)) {
                this.extractGlobalVariable(line, sourceFile, lineNumber);
            }
            
            // 提取常量
            if (line.startsWith('constant ')) {
                this.extractConstant(line, sourceFile, lineNumber);
            }
            
            // 提取类型定义
            if (line.startsWith('type ')) {
                this.extractType(line, sourceFile, lineNumber);
            }
        }
    }
    
    /**
     * 提取函数定义
     */
    extractFunction(line, sourceFile, lineNumber) {
        // 匹配: function 函数名 takes 参数 returns 返回类型
        const match = line.match(/function\s+(\w+)\s+takes\s+(.*?)\s+returns\s+(\w+)/);
        
        if (match) {
            const [, name, paramsStr, returnType] = match;
            
            this.userFunctions.set(name, {
                name: name,
                params: this.parseParameters(paramsStr),
                returnType: returnType,
                sourceFile: sourceFile,
                lineNumber: lineNumber,
                type: 'function'
            });
            
            this.userTypes.add(returnType);
            
            // 记录参数类型
            const params = this.parseParameters(paramsStr);
            params.forEach(param => {
                this.userTypes.add(param.type);
            });
        }
    }
    
    /**
     * 提取native函数
     */
    extractNative(line, sourceFile, lineNumber) {
        // 匹配: native 函数名 takes 参数 returns 返回类型
        const match = line.match(/native\s+(\w+)\s+takes\s+(.*?)\s+returns\s+(\w+)/);
        
        if (match) {
            const [, name, paramsStr, returnType] = match;
            
            this.userNatives.set(name, {
                name: name,
                params: this.parseParameters(paramsStr),
                returnType: returnType,
                sourceFile: sourceFile,
                lineNumber: lineNumber,
                type: 'native'
            });
            
            this.userTypes.add(returnType);
            
            // 记录参数类型
            const params = this.parseParameters(paramsStr);
            params.forEach(param => {
                this.userTypes.add(param.type);
            });
        }
    }
    
    /**
     * 解析函数参数
     */
    parseParameters(paramsStr) {
        if (paramsStr.trim() === 'nothing') {
            return [];
        }
        
        const params = [];
        const parts = paramsStr.split(',');
        
        for (const part of parts) {
            const trimmed = part.trim();
            const match = trimmed.match(/(\w+)\s+(\w+)/);
            if (match) {
                const [, type, name] = match;
                params.push({ type, name });
                this.userTypes.add(type);
            }
        }
        
        return params;
    }
    
    /**
     * 提取全局变量
     */
    extractGlobalVariable(line, sourceFile, lineNumber) {
        // 匹配各种全局变量格式
        const patterns = [
            /^\s*(\w+)\s+array\s+(\w+)/,           // type array name
            /^\s*(\w+)\s+(\w+)\s*=\s*(.+)/,       // type name = value
            /^\s*(\w+)\s+(\w+)\s*$/                // type name
        ];
        
        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                const type = match[1];
                const name = match[2];
                const value = match[3] || null;
                
                this.userGlobals.set(name, {
                    name: name,
                    type: type,
                    value: value,
                    sourceFile: sourceFile,
                    lineNumber: lineNumber
                });
                
                this.userTypes.add(type);
                break;
            }
        }
    }
    
    /**
     * 提取常量定义
     */
    extractConstant(line, sourceFile, lineNumber) {
        const match = line.match(/constant\s+(\w+)\s+(\w+)\s*=\s*(.+)/);
        if (match) {
            const [, type, name, value] = match;
            
            this.userConstants.set(name, {
                name: name,
                type: type,
                value: value,
                sourceFile: sourceFile,
                lineNumber: lineNumber
            });
            
            this.userTypes.add(type);
        }
    }
    
    /**
     * 提取类型定义
     */
    extractType(line, sourceFile, lineNumber) {
        const match = line.match(/type\s+(\w+)\s+extends\s+(\w+)/);
        if (match) {
            const [, newType, baseType] = match;
            this.userTypes.add(newType);
            this.userTypes.add(baseType);
        }
    }
    
    /**
     * 检查是否是全局变量
     */
    isGlobalVariable(line) {
        return /^\s*\w+(?:\s+array)?\s+\w+(?:\s*=|$)/.test(line) && 
               !line.startsWith('function') && 
               !line.startsWith('native') &&
               !line.startsWith('local') &&
               !line.startsWith('//');
    }
    
    /**
     * 生成分析报告
     */
    generateAnalysisReport() {
        console.log('\n=== 用户项目分析报告 ===');
        console.log(`分析文件数量: ${this.totalFiles}`);
        console.log(`发现函数: ${this.userFunctions.size}`);
        console.log(`发现native函数: ${this.userNatives.size}`);
        console.log(`发现全局变量: ${this.userGlobals.size}`);
        console.log(`发现类型: ${this.userTypes.size}`);
        console.log(`发现常量: ${this.userConstants.size}`);
        
        // 显示一些示例
        console.log('\n=== 函数示例 ===');
        let count = 0;
        for (const [name, func] of this.userFunctions) {
            if (count < 5) {
                console.log(`${name}(${func.params.map(p => `${p.type} ${p.name}`).join(', ')}) -> ${func.returnType}`);
                count++;
            }
        }
        
        console.log('\n=== Native函数示例 ===');
        count = 0;
        for (const [name, func] of this.userNatives) {
            if (count < 5) {
                console.log(`${name}(${func.params.map(p => `${p.type} ${p.name}`).join(', ')}) -> ${func.returnType}`);
                count++;
            }
        }
    }
    
    /**
     * 验证代码是否符合用户项目标准
     */
    validateCode(code) {
        const errors = [];
        const warnings = [];
        
        const lines = code.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const lineNumber = i + 1;
            
            if (line === '' || line.startsWith('//')) continue;
            
            // 检查函数调用
            if (line.includes('call ')) {
                this.validateFunctionCall(line, lineNumber, errors);
            }
            
            // 检查变量使用
            this.validateVariableUsage(line, lineNumber, errors);
            
            // 检查类型使用
            this.validateTypeUsage(line, lineNumber, errors);
        }
        
        return { errors, warnings };
    }
    
    /**
     * 验证函数调用
     */
    validateFunctionCall(line, lineNumber, errors) {
        const match = line.match(/call\s+(\w+)\s*\(/);
        if (match) {
            const functionName = match[1];
            
            // 检查函数是否存在
            if (!this.userFunctions.has(functionName) && !this.userNatives.has(functionName)) {
                errors.push({
                    line: lineNumber,
                    message: `未定义的函数: ${functionName}`
                });
            }
        }
    }
    
    /**
     * 验证变量使用
     */
    validateVariableUsage(line, lineNumber, errors) {
        // 检查全局变量使用
        const globalVarMatch = line.match(/\b(udg_\w+|gg_\w+)\b/);
        if (globalVarMatch) {
            const varName = globalVarMatch[1];
            if (!this.userGlobals.has(varName)) {
                errors.push({
                    line: lineNumber,
                    message: `未定义的全局变量: ${varName}`
                });
            }
        }
    }
    
    /**
     * 验证类型使用
     */
    validateTypeUsage(line, lineNumber, errors) {
        // 检查变量声明中的类型
        const typeMatch = line.match(/(?:local\s+)?(\w+)\s+\w+/);
        if (typeMatch && !line.startsWith('function') && !line.startsWith('native')) {
            const typeName = typeMatch[1];
            if (!this.userTypes.has(typeName)) {
                errors.push({
                    line: lineNumber,
                    message: `未知的类型: ${typeName}`
                });
            }
        }
    }
}

module.exports = UserProjectAnalyzer;
