# 🎯 核心开发标准 - 个人习惯和要求

#


## 📋 **标准变量名**

### **全局变量命名**
```jass
// 基础变量
integer SL = 0                        // 数量 (ShuLiang)
integer SJ/*时间*/ = 0                // 时间 (ShiJian)
unit array YX                         // 英雄数组 (YingXiong)
unit array zz/*助手*/                 // 助手单位数组
integer array CS/*通关次数*/          // 通关次数数组 (CiShu)

// UI相关变量
integer UI_MainPanel = 0              // 主面板
integer array UI_Frames              // UI框架数组
boolean UI_Visible = false           // UI显示状态

// 技能相关变量
trigger SkillTrigger = null           // 技能触发器
```

### **局部变量命名**
```jass
// YDWE局部变量键名
"a"         // 攻击者 (Attacker)
"b"         // 目标 (Target)
"lv"        // 等级 (Level)
"sh"        // 伤害 (ShangHai)
"caster"    // 施法者
"target"    // 目标单位
"damage"    // 伤害值
"timer"     // 定时器

// 普通局部变量
local timer ydl_timer                 // 定时器
local unit ydl_caster                 // 施法者
local real ydl_damage                 // 伤害
local effect ydl_effect               // 特效
local group ydl_group                 // 单位组
```

## 🧮 **标准公式**

### **伤害计算公式**
```jass
// 基础伤害公式
基础伤害 = 英雄敏捷 * 技能系数 * 技能等级
最终伤害 = 基础伤害 * (1.0 + 技能伤害加成)

// 具体实现
call YDLocal1Set(real, "baseDamage", (I2R(GetHeroAgi(caster, true)) * 3.0 * skillLevel))
call YDLocal1Set(real, "finalDamage", (baseDamage * (1.0 + skillDamageBonus)))

// 暴击计算
if GetRandomReal(1.0, 100.0) <= critChance then
    set finalDamage = finalDamage * critMultiplier
endif
```

### **坐标计算公式**
```jass
// 贝塞尔曲线 (二次)
currentX = (1-t)*(1-t)*startX + 2*(1-t)*t*controlX + t*t*endX
currentY = (1-t)*(1-t)*startY + 2*(1-t)*t*controlY + t*t*endY

// 圆形分布
angle = i * 6.28318 / totalCount
x = centerX + radius * Cos(angle)
y = centerY + radius * Sin(angle)

// 距离计算
distance = SquareRoot((x2-x1)*(x2-x1) + (y2-y1)*(y2-y1))
```

## 🔧 **标准逻辑模式**

### **技能模板**
```jass
function SkillActions takes nothing returns nothing
    local timer ydl_timer
    local unit ydl_attacker = GetAttacker()
    local unit ydl_target = GetTriggerUnit()
    local real ydl_level
    local real ydl_heroAgi
    local real ydl_skillDamage
    local real ydl_baseDamage
    local real ydl_finalDamage

    // 1. 获取技能数据
    set ydl_level = YDUserDataGet(unit, ydl_attacker, "技能名", real)
    set ydl_heroAgi = I2R(GetHeroAgi(ydl_attacker, true))
    set ydl_skillDamage = YDUserDataGet(unit, ydl_attacker, "技能伤害", real)

    // 2. 计算伤害
    set ydl_baseDamage = ydl_heroAgi * 3.0 * ydl_level
    set ydl_finalDamage = ydl_baseDamage * (1.0 + ydl_skillDamage)

    // 3. 技能逻辑
    [技能具体实现]

    // 4. 清理资源
    set ydl_timer = null
    set ydl_attacker = null
    set ydl_target = null
endfunction
```

### **UI创建模板**
```jass
function Create技能名UI takes nothing returns nothing
    local integer ydl_mainPanel

    // 调试输出
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "[UI调试] 开始创建界面")

    // 创建主面板
    set ydl_mainPanel = DzCreateFrameByTagName("BACKDROP", "MainPanel", DzGetGameUI(), "", 0)
    if ydl_mainPanel == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "[错误] 面板创建失败")
        return
    endif

    // 设置面板属性
    call DzFrameSetSize(ydl_mainPanel, 0.4, 0.3)
    call DzFrameSetAbsolutePoint(ydl_mainPanel, 4, 0.4, 0.35)
    call DzFrameSetTexture(ydl_mainPanel, "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp", 0)

    // 初始隐藏
    call DzFrameShow(ydl_mainPanel, false)

    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "[UI调试] 界面创建完成")
    set ydl_mainPanel = null
endfunction
```

### **定时器管理模式**
```jass
// 创建定时器并绑定数据
set ydl_timer = CreateTimer()
call YDLocalSet(ydl_timer, unit, "caster", ydl_caster)
call YDLocalSet(ydl_timer, real, "damage", ydl_damage)
call YDLocalSet(ydl_timer, real, "count", 10.0)
call TimerStart(ydl_timer, 0.1, true, function TimerCallback)

// 定时器回调
function TimerCallback takes nothing returns nothing
    local timer ydl_timer = GetExpiredTimer()
    local unit ydl_caster = YDLocalGet(ydl_timer, unit, "caster")
    local real ydl_count = YDLocalGet(ydl_timer, real, "count")

    // 减少计数
    set ydl_count = ydl_count - 1
    call YDLocalSet(ydl_timer, real, "count", ydl_count)

    // 检查结束
    if ydl_count <= 0 then
        call YDLocalClear(ydl_timer)
        call DestroyTimer(ydl_timer)
    endif

    set ydl_timer = null
    set ydl_caster = null
endfunction
```

## 🎨 **个人习惯**

### **代码风格**
- 使用中文注释：`/*时间*/`、`/*助手*/`
- 变量名简洁：`SL`(数量)、`SJ`(时间)、`YX`(英雄)
- 调试信息必须包含：`[调试]`、`[错误]`标识
- 颜色代码：成功`|cFF00FF00`、错误`|cFFFF0000`、警告`|cFFFFFF00`

### **函数命名**
- 技能函数：`技能名Actions`
- UI函数：`Create技能名UI`、`Toggle技能名UI`
- 初始化：`Init技能名`、`Init技能名System`

### **错误处理**
```jass
// 必须包含的错误检查
if frame == 0 then
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] 框架创建失败|r")
    return
endif

// 必须包含的调试输出
call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[调试] 操作完成|r")
```

### **资源管理**
```jass
// 必须清理的资源
set ydl_timer = null
set ydl_unit = null
set ydl_effect = null
call DestroyGroup(ydl_group)
call YDLocalClear(ydl_timer)
```

## 🚫 **严格禁止**

### **禁用API**
- `DzCreateFrame` - 使用`DzCreateFrameByTagName`替代
- `BlzSetUnitMaxHP` - 1.24环境不支持
- 像素坐标计算 - 使用相对坐标

### **禁用模式**
- 不使用`local`变量存储重要数据 - 使用YDWE系统
- 不直接操作句柄 - 通过验证函数
- 不省略错误检查 - 必须包含完整验证

---

**这就是我的核心标准** - 简洁、实用、经过验证！
