//===========================================================================
// 快速测试 - 最简单的验证方法
//===========================================================================

globals
    trigger gg_trg_QuickTest = null
endglobals

//===========================================================================
// 最基础的API测试
//===========================================================================
function BasicAPITest takes nothing returns nothing
    local real width
    local real height
    local real ratio
    local integer testFrame
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 开始基础API测试 ===|r")
    
    // 测试1：DzAPI基础功能
    if DzGetGameUI() != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ DzAPI 正常工作|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ DzAPI 不可用，请检查环境|r")
        return
    endif
    
    // 测试2：分辨率获取
    set width = DzGetClientWidth()
    set height = DzGetClientHeight()
    
    if width > 0 and height > 0 then
        set ratio = width / height
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 分辨率检测成功: " + I2S(R2I(width)) + "x" + I2S(R2I(height)) + "|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700  宽高比: " + R2S(ratio) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ 分辨率获取失败|r")
        return
    endif
    
    // 测试3：创建简单框架
    set testFrame = DzCreateFrameByTagName("BACKDROP", "TestFrame", DzGetGameUI(), "", 0)
    
    if testFrame != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 框架创建成功|r")
        
        // 设置测试框架
        call DzFrameSetSize(testFrame, 0.2, 0.1)
        call DzFrameSetAbsolutePoint(testFrame, 4, 0.4, 0.3)
        call DzFrameSetTexture(testFrame, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
        call DzFrameShow(testFrame, true)
        
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 测试面板已显示在屏幕中央|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00  如果看到一个灰色面板，说明基础功能正常|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ 框架创建失败|r")
        return
    endif
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 基础测试完成，可以继续完整测试 ===|r")
endfunction

//===========================================================================
// 分辨率适配测试
//===========================================================================
function ResolutionAdaptTest takes nothing returns nothing
    local real width = DzGetClientWidth()
    local real height = DzGetClientHeight()
    local real ratio = width / height
    local string adaptMode
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 分辨率适配测试 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700当前分辨率: " + I2S(R2I(width)) + "x" + I2S(R2I(height)) + "|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700宽高比: " + R2S(ratio) + "|r")
    
    // 判断适配模式
    if ratio > 1.8 then
        set adaptMode = "超宽屏模式 (21:9+)"
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600适配模式: " + adaptMode + "|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600建议: 使用黑边适配，UI居中显示|r")
    elseif ratio > 1.6 then
        set adaptMode = "宽屏模式 (16:9/16:10)"
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00适配模式: " + adaptMode + "|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00建议: 标准适配，轻微压缩|r")
    elseif ratio > 1.2 then
        set adaptMode = "标准模式 (4:3/5:4)"
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB适配模式: " + adaptMode + "|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB建议: 无需适配，原生显示|r")
    else
        set adaptMode = "竖屏模式"
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000适配模式: " + adaptMode + "|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000警告: 不支持竖屏显示|r")
    endif
endfunction

//===========================================================================
// 完整UI测试
//===========================================================================
function FullUITest takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 开始完整UI测试 ===|r")
    
    // 先执行基础测试
    call BasicAPITest()
    call TriggerSleepAction(1.0)
    
    // 分辨率适配测试
    call ResolutionAdaptTest()
    call TriggerSleepAction(1.0)
    
    // 启动完整UI系统
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700正在启动完整UI系统...|r")
    call InitAdaptiveUI()
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 完整测试完成 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -toggle 切换UI显示|r")
endfunction

//===========================================================================
// 触发器初始化
//===========================================================================
function InitTrig_QuickTest takes nothing returns nothing
    set gg_trg_QuickTest = CreateTrigger()
    
    // 注册聊天命令
    call TriggerRegisterPlayerChatEvent(gg_trg_QuickTest, Player(0), "-test", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_QuickTest, Player(0), "-basic", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_QuickTest, Player(0), "-resolution", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_QuickTest, Player(0), "-full", true)
    
    call TriggerAddAction(gg_trg_QuickTest, function TestCommandHandler)
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 快速测试系统已加载 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入以下命令进行测试:|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-basic     基础API测试|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-resolution 分辨率检测测试|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-full      完整UI测试|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-test      执行所有测试|r")
endfunction

//===========================================================================
// 命令处理函数
//===========================================================================
function TestCommandHandler takes nothing returns nothing
    local string command = GetEventPlayerChatString()
    
    if command == "-basic" then
        call BasicAPITest()
    elseif command == "-resolution" then
        call ResolutionAdaptTest()
    elseif command == "-full" then
        call FullUITest()
    elseif command == "-test" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 执行完整测试序列 ===|r")
        call BasicAPITest()
        call TriggerSleepAction(2.0)
        call ResolutionAdaptTest()
        call TriggerSleepAction(2.0)
        call FullUITest()
    endif
endfunction

//===========================================================================
// 主函数
//===========================================================================
function main takes nothing returns nothing
    call InitTrig_QuickTest()
endfunction
