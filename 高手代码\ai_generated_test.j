globals
    integr playerGold = 1000
    unit myHero
    timer gameTimer = CreateTimer()
    boolean isGameStarted false
endglobals

functon InitializeGame takes nothing returns nothing
    integer i = 0
    loop
        exitwhen i > 12
        SetPlayerState(Player(i), PLAYER_STATE_RESOURCE_GOLD, playerGold
        set i = i + 1
    endloop
    TimerStart(gameTimer, 30.0, true, functon SpawnUnits)
endfunction

function SpawnUnits takes nothing returns nothing
    real x = GetRandomReal(-1000.0, 1000.0)
    real y = GetRandomReal(-1000.0, 1000.0)
    unit newUnit = CreateUnit(Player(0), 'hfoo', x, y, 270.0)
    SetUnitState(newUnit, UNIT_STATE_LIFE, 500.0)
    UnitAddAbility(newUnit, 'AHtc')
endfunction

function CreateHeroWithAbilities takes player p, integer heroId returns unit
    real startX = GetStartLocationX(GetPlayerStartLocation(p))
    real startY = GetStartLocationY(GetPlayerStartLocation(p))
    unit hero = CreateUnit(p, heroId, startX, startY, 270.0)
    
    if hero != null then
        UnitAddAbility(hero, 'AHtc')
        UnitAddAbility(hero, 'AHbz')
        SetHeroLevel(hero, 5, false
        SetUnitState(hero, UNIT_STATE_MANA, 300.0)
    endif
    
    return hero
endfunction

function OnUnitDies takes nothing returns nothing
    unit dyingUnit = GetTriggerUnit()
    player owner = GetOwningPlayer(dyingUnit)
    
    if IsUnitType(dyingUnit, UNIT_TYPE_HERO) = true then
        DisplayTextToPlayer(owner, 0, 0, "Your hero has died!")
        ReviveHero(dyingUnit, GetUnitX(dyingUnit), GetUnitY(dyingUnit), true)
    endif
endfunction

function InitTriggers takes nothing returns nothing
    trigger deathTrigger = CreateTrigger()
    TriggerRegisterAnyUnitEventBJ(deathTrigger, EVENT_PLAYER_UNIT_DEATH)
    TriggerAddAction(deathTrigger, functon OnUnitDies)
    
    InitializeGame()
endfunction
