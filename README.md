# 🎮 魔兽争霸3 JASS开发项目

## 🎯 项目概述

这是一个专注于魔兽争霸3 JASS脚本开发的项目，包含技能系统、UI界面、开发工具和智能约束系统。

### 🔧 技术栈
- **游戏版本**: 魔兽争霸3 1.24/1.27
- **开发工具**: YDWE (You Dao World Editor)
- **编程语言**: JASS + GUI
- **扩展API**: DzAPI, JAPI, YDWE API
- **开发环境**: VS Code + JASS扩展

## 📁 项目结构

### 🎯 核心文件
- **技能文件/**: 完整的JASS技能实现
- **UI_Templates/**: UI界面开发模板
- **文档/**: 开发知识库和最佳实践
- **系统文件/**: 开发环境和工具配置

### 🔥 技能系统
1. **fireball_skill.j** - 火球术弧形投射物
   - 贝塞尔曲线轨迹
   - 范围伤害效果
   - YDWE变量系统

2. **crocodile_ultimate.j** - 变大持续伤害
   - 单位缩放效果
   - 持续伤害机制
   - 定时器管理

3. **dark_arrow_rain.j** - 黑暗箭雨
   - 仿暴风雪效果
   - 多箭下落动画
   - 范围持续伤害

4. **BasicTest.j** - 基础测试
   - 环境验证
   - DZAPI测试

### 🎨 UI系统
- **Basic_Panel.j** - 基础面板模板
- **Button_Grid.j** - 按钮网格模板
- **UI_Quick_Start_Template.j** - 快速启动模板
- **FDF_Library/** - 样式库
- **TOC_Files/** - TOC文件库

## 🤖 智能开发系统

### 📋 Verified_JASS_Intelligence_System.md
基于项目真实代码构建的AI智能约束系统，包含：
- 经过验证的代码模板
- 项目特定的API约束
- 智能错误检测和修复
- 标准化的提示词模板

### 🎯 核心特性
- **环境约束**: 严格的1.24/1.27兼容性
- **代码风格**: 统一的命名规范和结构模式
- **错误预防**: 基于项目经验的错误检测
- **自动修复**: 常见问题的自动修复建议

## 🚀 快速开始

### 1. 环境准备
```
1. 安装YDWE编辑器
2. 配置VS Code + JASS扩展
3. 导入项目到编辑器
```

### 2. 技能开发
```jass
// 使用验证过的技能模板
function Trig_技能名称Actions takes nothing returns nothing
    local timer ydl_timer
    YDLocalInitialize()
    
    // 获取基础单位
    call YDLocal1Set(unit, "a", GetAttacker())
    call YDLocal1Set(unit, "b", GetTriggerUnit())
    
    // 技能逻辑
    
    call YDLocal1Release()
    set ydl_timer = null
endfunction
```

### 3. UI开发
```jass
// 使用验证过的UI模板
local framehandle ydl_frame
set ydl_frame = DzCreateFrameByTagName("BACKDROP", "FrameName", DzGetGameUI(), "", 0)
call DzFrameSetSize(ydl_frame, 0.3, 0.2)
call DzFrameShow(ydl_frame, true)
set ydl_frame = null
```

## 📚 文档资源

### 🎯 核心文档
- **关键信息.md** - 核心概念和重要发现
- **正确案例.md** - 验证过的正确代码示例
- **错误案例.md** - 常见错误和解决方案
- **722工作日志.md** - 开发工作记录

### 🔧 开发工具
- **jass-dev-environment/** - 完整开发环境
- **jass-full-compiler/** - JASS编译器
- **.vscode/snippets/** - VS Code代码片段

## 🎯 开发规范

### 📋 代码风格
- 全局变量使用`udg_`前缀
- 局部变量使用`ydl_`前缀
- 函数命名使用`Trig_技能名称Actions`格式
- 包含完整的资源管理和错误检查

### 🔒 环境约束
- 严格遵循1.24/1.27版本兼容性
- 使用YDWE局部变量系统
- 禁用不兼容的API函数
- 包含调试信息输出

### 🎨 UI约束
- 使用`DzCreateFrameByTagName`创建框架
- 坐标使用相对值或标准化像素值
- 包含完整的显示/隐藏控制
- 遵循项目UI风格规范

## 📈 项目特色

### ✅ 经过验证的代码
- 所有技能都经过实际测试
- 模板基于成功运行的代码
- 避免AI生成的错误模式

### 🤖 智能约束系统
- 基于项目真实代码的AI约束
- 自动错误检测和修复建议
- 标准化的开发流程

### 📚 完整知识库
- 详细的开发文档
- 常见问题解决方案
- 最佳实践指南

---

**维护日期**: 2025-07-23  
**项目状态**: 活跃开发中  
**技术支持**: 基于Augment智能开发系统
