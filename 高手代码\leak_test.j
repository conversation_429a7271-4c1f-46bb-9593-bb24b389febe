// ===== 内存泄漏快速测试 =====
// 测试目标: 验证Level 3内存泄漏检测和快速修复功能

function LeakTest takes nothing returns nothing
    local location loc = GetRectCenter(gg_rct_test)
    local group g = CreateGroup()
    local effect e = AddSpecialEffect("Abilities\\Spells\\Human\\HolyBolt\\HolyBoltSpecialArt.mdl", 0, 0)
    local timer t = CreateTimer()
    
    // 使用资源但不清理 - 应该被检测为内存泄漏
    call CreateUnit(Player(0), 'hfoo', GetLocationX(loc), GetLocationY(loc), 0)
    call GroupEnumUnitsInRange(g, 0, 0, 500, null)
    call TimerStart(t, 1.0, false, null)
    
    // ❌ 缺少清理代码 - 应该有快速修复建议:
    // call RemoveLocation(loc)
    // call DestroyGroup(g)
    // call DestroyEffect(e)
    // call DestroyTimer(t)
endfunction

function PerformanceTest takes nothing returns nothing
    local integer i = 0
    local location tempLoc
    
    // ❌ 性能问题: 循环中创建Location - 应该被检测
    loop
        exitwhen i > 10
        set tempLoc = GetUnitLoc(udg_Units[i])  // 性能杀手
        call CreateUnit(Player(0), 'hfoo', GetLocationX(tempLoc), GetLocationY(tempLoc), 0)
        // 缺少: call RemoveLocation(tempLoc)
        set i = i + 1
    endloop
endfunction

// ===== 验证检查点 =====
// 应该检测到4个内存泄漏 (Location, Group, Effect, Timer)
// 应该检测到循环中的Location创建性能问题
// 应该提供快速修复建议 (灯泡图标)
