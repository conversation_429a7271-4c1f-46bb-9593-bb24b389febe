# 攻击触发冲击波技能系统

## 功能介绍
这是一个为魔兽争霸3制作的攻击触发冲击波技能系统，当单位攻击时有概率触发冲击波，对前方扇形区域的敌方单位造成伤害。

## 技能特点
- ✅ **纯JASS实现** - 使用原生JASS语言编写，性能优秀
- ✅ **概率触发** - 攻击时25%概率触发（可根据技能等级提升）
- ✅ **扇形伤害** - 对前方60度扇形区域造成伤害
- ✅ **动态特效** - 冲击波带有移动特效，视觉效果佳
- ✅ **原生特效** - 使用魔兽争霸3原生特效，无需额外资源
- ✅ **内存安全** - 遵循你的项目内存管理规范

## 技能参数
| 参数 | 数值 | 说明 |
|------|------|------|
| 触发概率 | 25% | 每次攻击的触发概率 |
| 伤害系数 | 1.5倍 | 基于攻击力的伤害倍数 |
| 影响范围 | 400 | 冲击波最大传播距离 |
| 影响角度 | 60度 | 前方扇形区域角度 |
| 移动速度 | 800 | 冲击波传播速度 |

## 使用方法

### 1. 物编设置
在物体编辑器中创建一个技能：
- **技能ID**: `A000`
- **技能类型**: 被动技能
- **基于技能**: 建议使用"硬化皮肤"或其他被动技能
- **图标**: 选择合适的冲击波图标

### 2. 代码集成
将以下文件添加到你的项目中：
```
hayx/jass/AttackShockwave.j      // 主要系统文件
hayx/jass/AttackShockwave.cfg    // 配置文件
hayx/jass/AttackShockwaveTest.j  // 测试文件（可选）
```

### 3. 为单位添加技能
```jass
// 为单位添加攻击冲击波技能
call AddAttackShockwaveAbility(hero)

// 设置技能等级（影响触发概率）
call SetAttackShockwaveLevel(hero, 3)
```

### 4. 测试命令
在游戏中输入以下命令进行测试：
- `-shockwave` - 为选中单位添加技能
- `-removeshockwave` - 移除选中单位的技能
- `-testshockwave` - 显示帮助信息

## API接口

### AddAttackShockwaveAbility
```jass
function AddAttackShockwaveAbility takes unit u returns nothing
```
为指定单位添加攻击冲击波技能。

### RemoveAttackShockwaveAbility
```jass
function RemoveAttackShockwaveAbility takes unit u returns nothing
```
移除指定单位的攻击冲击波技能。

### HasAttackShockwaveAbility
```jass
function HasAttackShockwaveAbility takes unit u returns boolean
```
检查指定单位是否拥有攻击冲击波技能。

### SetAttackShockwaveLevel
```jass
function SetAttackShockwaveLevel takes unit u, integer level returns nothing
```
设置指定单位的攻击冲击波技能等级。

## 技能等级效果
- **1级**: 25%触发概率
- **2级**: 35%触发概率  
- **3级**: 45%触发概率
- **4级**: 55%触发概率
- **5级**: 65%触发概率

## 自定义配置
你可以修改 `AttackShockwave.j` 文件中的常量来调整技能参数：

```jass
private constant integer SHOCKWAVE_ABILITY_ID = 'A000'     // 技能ID
private constant real SHOCKWAVE_CHANCE = 0.25              // 基础触发概率
private constant real SHOCKWAVE_DAMAGE_FACTOR = 1.5        // 伤害系数
private constant real SHOCKWAVE_RANGE = 400.0              // 影响范围
private constant real SHOCKWAVE_ANGLE = 60.0               // 影响角度
```

## 兼容性
- ✅ 兼容YDWE编辑器
- ✅ 兼容DZAPI系统
- ✅ 支持KK对战平台
- ✅ 遵循你的项目内存管理规范
- ✅ 使用原生特效，无需额外资源

## 注意事项
1. 确保在物编中创建了对应的技能ID `A000`
2. 技能必须设置为被动技能才能正常工作
3. 冲击波只对敌方单位造成伤害，不会误伤友军
4. 系统会自动处理内存管理，无需手动清理

## 作者
CODEX - 专为LO定制的攻击冲击波技能系统
