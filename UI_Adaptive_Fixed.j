//===========================================================================
// 1.27版本UI自适应系统 - 修复版本（符合项目标准）
//===========================================================================

globals
    trigger gg_trg_UIAdaptive = null
    
    // UI框架句柄（使用framehandle类型）
    framehandle AdaptiveContainer = null
    framehandle TopPanel = null
    framehandle BottomPanel = null
    framehandle MiniMapFrame = null
    
    // 屏幕适配参数
    real SCREEN_WIDTH = 0.8
    real SCREEN_HEIGHT = 0.6
    real ASPECT_RATIO = 1.333
    
    boolean IsUIAdapted = false
endglobals

//===========================================================================
// 屏幕比例检测（使用可用的API）
//===========================================================================
function DetectScreenRatio takes nothing returns nothing
    // 由于DzGetClientWidth可能不存在，使用固定比例或其他方法
    // 这里先使用默认值，后续可以根据实际API调整
    set ASPECT_RATIO = 1.777  // 假设16:9宽屏
    
    if ASPECT_RATIO > 1.6 then
        set SCREEN_WIDTH = 0.8 * (1.333 / ASPECT_RATIO)
        set SCREEN_HEIGHT = 0.6
    else
        set SCREEN_WIDTH = 0.8
        set SCREEN_HEIGHT = 0.6
    endif
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[适配] 宽高比: " + R2S(ASPECT_RATIO) + " 宽度: " + R2S(SCREEN_WIDTH) + "|r")
endfunction

//===========================================================================
// 创建自适应UI
//===========================================================================
function CreateAdaptiveUI takes nothing returns nothing
    local integer i
    local framehandle buttonFrame
    local real buttonSize = 0.04
    local framehandle ResourceText
    local framehandle MapTitle
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 开始创建自适应UI ===|r")
    
    // 检测屏幕比例
    call DetectScreenRatio()
    
    // 创建主容器
    set AdaptiveContainer = DzCreateFrameByTagName("BACKDROP", "AdaptiveContainer", DzGetGameUI(), "", 0)
    if AdaptiveContainer == null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] 容器创建失败|r")
        return
    endif
    
    call DzFrameSetSize(AdaptiveContainer, SCREEN_WIDTH, SCREEN_HEIGHT)
    call DzFrameSetAbsolutePoint(AdaptiveContainer, 4, 0.4, 0.3)
    call DzFrameSetTexture(AdaptiveContainer, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
    call DzFrameShow(AdaptiveContainer, true)
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[成功] 主容器创建: " + I2S(AdaptiveContainer) + "|r")
    
    // 创建顶部资源面板
    set TopPanel = DzCreateFrameByTagName("BACKDROP", "TopPanel", AdaptiveContainer, "", 0)
    if TopPanel != null then
        call DzFrameSetSize(TopPanel, SCREEN_WIDTH - 0.02, 0.08)
        call DzFrameSetPoint(TopPanel, 1, AdaptiveContainer, 1, 0.0, -0.01)
        call DzFrameSetTexture(TopPanel, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
        call DzFrameShow(TopPanel, true)
        
        // 创建资源文本
        set ResourceText = DzCreateFrameByTagName("TEXT", "ResourceText", TopPanel, "", 0)
        if ResourceText != null then
            call DzFrameSetPoint(ResourceText, 4, TopPanel, 4, 0.0, 0.0)
            call DzFrameSetFont(ResourceText, "Fonts\\FRIZQT__.ttf", 0.012, 0)
            call DzFrameSetText(ResourceText, "|cFFFFD700金币: 2656  |cFF87CEEB木材: 705  |cFF90EE90食物: 18/80|r")
            call DzFrameShow(ResourceText, true)
        endif
        
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[成功] 顶部面板创建: " + I2S(TopPanel) + "|r")
    endif
    
    // 创建底部命令面板
    set BottomPanel = DzCreateFrameByTagName("BACKDROP", "BottomPanel", AdaptiveContainer, "", 0)
    if BottomPanel != null then
        call DzFrameSetSize(BottomPanel, SCREEN_WIDTH - 0.02, 0.15)
        call DzFrameSetPoint(BottomPanel, 3, AdaptiveContainer, 3, 0.0, 0.01)
        call DzFrameSetTexture(BottomPanel, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
        call DzFrameShow(BottomPanel, true)
        
        // 创建命令按钮 (简化版，只创建4个)
        set i = 0
        loop
            exitwhen i >= 4
            set buttonFrame = DzCreateFrameByTagName("BACKDROP", "CommandButton" + I2S(i), BottomPanel, "", 0)
            if buttonFrame != null then
                call DzFrameSetSize(buttonFrame, buttonSize, buttonSize)
                call DzFrameSetPoint(buttonFrame, 7, BottomPanel, 7, 
                    0.02 + i * (buttonSize + 0.005), 0.02)
                call DzFrameSetTexture(buttonFrame, "ReplaceableTextures\\CommandButtons\\BTNCancel.blp", 0)
                call DzFrameShow(buttonFrame, true)
            endif
            set i = i + 1
        endloop
        
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[成功] 底部面板创建: " + I2S(BottomPanel) + "|r")
    endif
    
    // 创建小地图
    set MiniMapFrame = DzCreateFrameByTagName("BACKDROP", "MiniMapFrame", AdaptiveContainer, "", 0)
    if MiniMapFrame != null then
        call DzFrameSetSize(MiniMapFrame, 0.15, 0.15)
        call DzFrameSetPoint(MiniMapFrame, 7, AdaptiveContainer, 7, 0.01, 0.01)
        call DzFrameSetTexture(MiniMapFrame, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
        call DzFrameShow(MiniMapFrame, true)
        
        // 创建小地图标题
        set MapTitle = DzCreateFrameByTagName("TEXT", "MapTitle", MiniMapFrame, "", 0)
        if MapTitle != null then
            call DzFrameSetPoint(MapTitle, 2, MiniMapFrame, 2, 0.0, 0.01)
            call DzFrameSetFont(MapTitle, "Fonts\\FRIZQT__.ttf", 0.01, 0)
            call DzFrameSetText(MapTitle, "|cFFFFD700小地图|r")
            call DzFrameShow(MapTitle, true)
        endif
        
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[成功] 小地图创建: " + I2S(MiniMapFrame) + "|r")
    endif
    
    set IsUIAdapted = true
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00=== UI自适应系统创建完成 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -toggle 切换显示|r")
endfunction

//===========================================================================
// 切换UI显示
//===========================================================================
function ToggleAdaptiveUI takes nothing returns nothing
    local boolean isVisible
    
    if IsUIAdapted then
        set isVisible = DzFrameIsVisible(AdaptiveContainer)
        call DzFrameShow(AdaptiveContainer, not isVisible)
        
        if not isVisible then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[UI] 自适应UI已显示|r")
        else
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[UI] 自适应UI已隐藏|r")
        endif
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] UI系统未初始化，请先输入 -ui|r")
    endif
endfunction

//===========================================================================
// 基础API测试
//===========================================================================
function BasicTest takes nothing returns nothing
    local framehandle testFrame
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 基础测试 ===|r")
    
    if DzGetGameUI() != null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ DzAPI 正常工作|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700  GameUI句柄: " + I2S(DzGetGameUI()) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ DzAPI 不可用|r")
        return
    endif
    
    // 测试创建简单框架
    set testFrame = DzCreateFrameByTagName("BACKDROP", "TestFrame", DzGetGameUI(), "", 0)
    if testFrame != null then
        call DzFrameSetSize(testFrame, 0.2, 0.1)
        call DzFrameSetAbsolutePoint(testFrame, 4, 0.4, 0.3)
        call DzFrameSetTexture(testFrame, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
        call DzFrameShow(testFrame, true)
        
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 测试框架创建成功: " + I2S(testFrame) + "|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00  如果看到灰色面板，说明基础功能正常|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ 框架创建失败|r")
        return
    endif
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 基础测试通过，可以使用 -ui 启动UI系统|r")
endfunction

//===========================================================================
// 命令处理
//===========================================================================
function HandleCommands takes nothing returns nothing
    local string command = GetEventPlayerChatString()
    
    if command == "-ui" then
        call CreateAdaptiveUI()
    elseif command == "-toggle" then
        call ToggleAdaptiveUI()
    elseif command == "-test" then
        call BasicTest()
    elseif command == "-help" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== UI自适应系统命令 ===|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-test    基础API测试|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-ui      启动自适应UI|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-toggle  切换UI显示|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-help    显示帮助|r")
    endif
endfunction

//===========================================================================
// 初始化函数
//===========================================================================
function InitTrig_UIAdaptive takes nothing returns nothing
    // 加载TOC文件（如果需要）
    call DzLoadToc("war3mapImported\\UI_Adaptive.toc")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB[加载] TOC文件已加载|r")
    
    set gg_trg_UIAdaptive = CreateTrigger()
    
    call TriggerRegisterPlayerChatEvent(gg_trg_UIAdaptive, Player(0), "-ui", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_UIAdaptive, Player(0), "-toggle", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_UIAdaptive, Player(0), "-test", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_UIAdaptive, Player(0), "-help", true)
    
    call TriggerAddAction(gg_trg_UIAdaptive, function HandleCommands)
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== UI自适应系统已加载 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -help 查看命令帮助|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700建议先输入 -test 进行基础测试|r")
endfunction

//===========================================================================
// 主函数
//===========================================================================
function main takes nothing returns nothing
    call InitTrig_UIAdaptive()
endfunction
