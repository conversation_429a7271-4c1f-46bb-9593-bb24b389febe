//===========================================================================
// 攻击触发冲击波技能系统
// 作者: CODEX
// 功能: 单位攻击时有概率触发冲击波，对前方扇形区域造成伤害
// 使用方法: 为单位添加技能ID 'A000' 即可自动触发
//===========================================================================

library AttackShockwave initializer AttackShockwave_Init

globals
    // 技能配置常量
    private constant integer SHOCKWAVE_ABILITY_ID = 'A000'  // 技能ID，需要在物编中创建
    private constant real SHOCKWAVE_CHANCE = 0.25           // 触发概率 25%
    private constant real SHOCKWAVE_DAMAGE_FACTOR = 1.5     // 伤害系数（攻击力的1.5倍）
    private constant real SHOCKWAVE_RANGE = 400.0           // 冲击波范围
    private constant real SHOCKWAVE_ANGLE = 60.0            // 冲击波角度（度）
    private constant real SHOCKWAVE_SPEED = 800.0           // 冲击波移动速度
    private constant real SHOCKWAVE_DURATION = 0.5          // 冲击波持续时间
    
    // 特效路径 - 使用你项目中的原生特效
    private constant string SHOCKWAVE_EFFECT = "Abilities\\Spells\\Orc\\Shockwave\\ShockwaveMissile.mdx"
    private constant string HIT_EFFECT = "Abilities\\Spells\\Other\\Stampede\\StampedeMissileDeath.mdx"
    
    // 系统变量
    private trigger AttackTrigger = null
    private group TempGroup = null
    private timer ShockwaveTimer = null
    private integer ShockwaveCount = 0
    
    // 冲击波数据数组（简化版本，不使用struct）
    private unit array ShockwaveCaster
    private real array ShockwaveX
    private real array ShockwaveY
    private real array ShockwaveDamage
    private real array ShockwaveAngle
    private real array ShockwaveDistance
    private effect array ShockwaveEffect
    private timer array ShockwaveTimer
    private integer ShockwaveIndex = 0
endglobals

// 检查单位是否在扇形范围内
private function IsUnitInShockwaveRange takes unit target, real centerX, real centerY, real angle, real range returns boolean
    local real dx = GetUnitX(target) - centerX
    local real dy = GetUnitY(target) - centerY
    local real distance = SquareRoot(dx * dx + dy * dy)
    local real targetAngle = Atan2(dy, dx)
    local real angleDiff = RAbsBJ(targetAngle - angle)
    
    // 处理角度差值
    if angleDiff > bj_PI then
        set angleDiff = 2 * bj_PI - angleDiff
    endif
    
    return distance <= range and angleDiff <= (SHOCKWAVE_ANGLE * bj_DEGTORAD / 2)
endfunction

// 对单位造成伤害
private function DamageUnit takes unit source, unit target, real damage returns nothing
    local effect hitFx = AddSpecialEffectTarget(HIT_EFFECT, target, "chest")
    
    call UnitDamageTarget(source, target, damage, true, false, ATTACK_TYPE_NORMAL, DAMAGE_TYPE_NORMAL, WEAPON_TYPE_WHOKNOWS)
    call DestroyEffect(hitFx)
    set hitFx = null
endfunction

// 冲击波移动和伤害处理
private function ShockwaveMove takes nothing returns nothing
    local timer t = GetExpiredTimer()
    local integer index = YDWEGetIntegerByString("ShockwaveTimer", I2S(GetHandleId(t)))
    local real newX
    local real newY
    local unit u
    local real moveDistance = SHOCKWAVE_SPEED * 0.03  // 每0.03秒移动的距离

    set ShockwaveDistance[index] = ShockwaveDistance[index] + moveDistance

    // 计算新位置
    set newX = ShockwaveX[index] + ShockwaveDistance[index] * Cos(ShockwaveAngle[index])
    set newY = ShockwaveY[index] + ShockwaveDistance[index] * Sin(ShockwaveAngle[index])

    // 更新特效位置
    if ShockwaveEffect[index] != null then
        call DestroyEffect(ShockwaveEffect[index])
        set ShockwaveEffect[index] = AddSpecialEffect(SHOCKWAVE_EFFECT, newX, newY)
    endif

    // 检查范围内的敌方单位
    call GroupEnumUnitsInRange(TempGroup, newX, newY, 150.0, null)
    loop
        set u = FirstOfGroup(TempGroup)
        exitwhen u == null
        call GroupRemoveUnit(TempGroup, u)

        if IsUnitEnemy(u, GetOwningPlayer(ShockwaveCaster[index])) and GetUnitState(u, UNIT_STATE_LIFE) > 0.405 and not IsUnitType(u, UNIT_TYPE_STRUCTURE) then
            call DamageUnit(ShockwaveCaster[index], u, ShockwaveDamage[index])
        endif
    endloop

    // 检查是否超出最大范围
    if ShockwaveDistance[index] >= SHOCKWAVE_RANGE then
        call PauseTimer(t)
        call DestroyTimer(t)
        call DestroyEffect(ShockwaveEffect[index])

        // 清理数据
        set ShockwaveCaster[index] = null
        set ShockwaveEffect[index] = null
        set ShockwaveCount = ShockwaveCount - 1
    endif

    set u = null
    set t = null
endfunction

// 创建冲击波
private function CreateShockwave takes unit caster, unit target returns nothing
    local timer t
    local real damage = GetUnitState(caster, UNIT_STATE_DAMAGE_BASE) + GetUnitState(caster, UNIT_STATE_DAMAGE_BONUS)
    local real targetX = GetUnitX(target)
    local real targetY = GetUnitY(target)
    local integer index = ShockwaveIndex

    set damage = damage * SHOCKWAVE_DAMAGE_FACTOR

    // 存储冲击波数据
    set ShockwaveCaster[index] = caster
    set ShockwaveX[index] = GetUnitX(caster)
    set ShockwaveY[index] = GetUnitY(caster)
    set ShockwaveDamage[index] = damage
    set ShockwaveAngle[index] = Atan2(targetY - ShockwaveY[index], targetX - ShockwaveX[index])
    set ShockwaveDistance[index] = 0.0
    set ShockwaveEffect[index] = AddSpecialEffect(SHOCKWAVE_EFFECT, ShockwaveX[index], ShockwaveY[index])

    // 创建计时器
    set t = CreateTimer()
    call YDWESaveIntegerByString("ShockwaveTimer", I2S(GetHandleId(t)), index)
    call TimerStart(t, 0.03, true, function ShockwaveMove)

    set ShockwaveIndex = ShockwaveIndex + 1
    if ShockwaveIndex >= 100 then
        set ShockwaveIndex = 0
    endif

    set ShockwaveCount = ShockwaveCount + 1
    set t = null
endfunction

// 攻击事件处理
private function OnUnitAttacked takes nothing returns nothing
    local unit attacker = GetAttacker()
    local unit target = GetTriggerUnit()
    local real chance = SHOCKWAVE_CHANCE
    local integer level

    // 检查攻击者是否拥有冲击波技能
    if GetUnitAbilityLevel(attacker, SHOCKWAVE_ABILITY_ID) > 0 then
        set level = GetUnitAbilityLevel(attacker, SHOCKWAVE_ABILITY_ID)

        // 根据技能等级调整触发概率
        set chance = SHOCKWAVE_CHANCE + (level - 1) * 0.1

        // 概率触发
        if GetRandomReal(0, 1) <= chance then
            call CreateShockwave(attacker, target)

            // 播放音效（可选）
            // call StartSound(gg_snd_Shockwave, false, false, false, 10, 10)
        endif
    endif

    set attacker = null
    set target = null
endfunction

// 公共接口：为单位添加攻击冲击波技能
function AddAttackShockwaveAbility takes unit u returns nothing
    call UnitAddAbility(u, SHOCKWAVE_ABILITY_ID)
endfunction

// 公共接口：移除单位的攻击冲击波技能
function RemoveAttackShockwaveAbility takes unit u returns nothing
    call UnitRemoveAbility(u, SHOCKWAVE_ABILITY_ID)
endfunction

// 公共接口：检查单位是否拥有攻击冲击波技能
function HasAttackShockwaveAbility takes unit u returns boolean
    return GetUnitAbilityLevel(u, SHOCKWAVE_ABILITY_ID) > 0
endfunction

// 公共接口：设置单位的攻击冲击波技能等级
function SetAttackShockwaveLevel takes unit u, integer level returns nothing
    call SetUnitAbilityLevel(u, SHOCKWAVE_ABILITY_ID, level)
endfunction

// 系统初始化
private function AttackShockwave_Init takes nothing returns nothing
    local integer i = 0
    
    // 创建攻击事件触发器
    set AttackTrigger = CreateTrigger()
    
    // 为所有玩家注册攻击事件
    loop
        exitwhen i >= bj_MAX_PLAYER_SLOTS
        call TriggerRegisterPlayerUnitEvent(AttackTrigger, Player(i), EVENT_PLAYER_UNIT_ATTACKED, null)
        set i = i + 1
    endloop
    
    call TriggerAddAction(AttackTrigger, function OnUnitAttacked)
    
    // 创建临时单位组
    set TempGroup = CreateGroup()
endfunction

endlibrary

#endif // ATTACK_SHOCKWAVE_INCLUDED
