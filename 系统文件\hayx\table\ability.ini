[ACbk]
_parent = "ACbk"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNTheBlackArrow.blp"

[ACnr]
_parent = "ACnr"
-- 影响区域
Area = 650.0
-- 目标允许
targs = "ground,vulnerable,air,invulnerable,hero,friend,organic"

[AEpa]
_parent = "AEpa"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\az_ts_missile.mdx"

[AHab]
_parent = "AHab"
-- 魔法回复加快
DataA = 2.0
-- 等级
levels = 1

[AHdr]
_parent = "AHdr"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNDoom.blp"

[AIbl]
_parent = "AIbl"
-- 创建单位(每个种族)
UnitID = "u013"

[AIbt]
_parent = "AIbt"
-- 持续时间 - 普通
Dur = 1.0
-- 创建单位(每个种族)
UnitID = "u013"

[AIct]
_parent = "AIct"
-- 魔法施放时间间隔
Cool = 0.0
-- 持续时间 - 普通
Dur = 15.0

[AIpg]
_parent = "AIpg"
-- 对召唤单位伤害
DataC = 0.0
-- 单位麻痹时间
DataD = 2.0
-- 英雄麻痹时间
DataE = 2.0
-- 持续时间 - 普通
Dur = 2.0
-- 持续时间 - 英雄
HeroDur = 2.0
-- 目标允许
targs = "enemies,ground,air"

[ANre]
_parent = "ANre"
-- 影响区域
Area = 650.0
-- 目标允许
targs = "ground,vulnerable,air,invulnerable,hero,friend,organic"

[ARal]
_parent = "ARal"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11

[Aall]
_parent = "Aall"
-- 效果 - 施法者
CasterArt = ""
-- 选择单位类型
DataB = 3
-- 显示选择单位按钮
DataC = 0
-- 显示单位指示器
DataD = 0
-- 施法距离
Rng = 600.0
-- 效果 - 目标
TargetArt = ""

[Adis]
_parent = "Adis"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\flamestrike starfire i.mdx"

[Adts]
_parent = "Adts"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNFeedBack.blp"

[Afbt]
_parent = "Afbt"
-- 最大损耗魔法-单位
DataA = 10.0
-- 对召唤单位伤害
DataE = 10.0

[Afla]
_parent = "Afla"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNQuillSprayOff.blp"

[Aflk]
_parent = "Aflk"
-- 效果 -特殊
SpecialArt = "uther.mdx,Abilities\\Spells\\Human\\FlakCannons\\FlakTarget.mdl"

[Afra]
_parent = "Afra"
-- 持续时间 - 普通
Dur = 0.25
-- 持续时间 - 英雄
HeroDur = 0.25

[Afrb]
_parent = "Afrb"
-- 持续时间 - 普通
Dur = 3.0
-- 持续时间 - 英雄
HeroDur = 3.0

[Agyb]
_parent = "Agyb"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSkillz.blp"

[Ane2]
_parent = "Ane2"
-- 效果 - 施法者
CasterArt = ""
-- 施法距离
Rng = 600.0
-- 效果 - 目标
TargetArt = ""

[Aneu]
_parent = "Aneu"
-- 效果 - 施法者
CasterArt = ""
-- 激活范围
DataA = 2500.0
-- 选择单位类型
DataB = 3
-- 显示选择单位按钮
DataC = 0
-- 编辑器后缀
EditorSuffix = "近"
-- 施法距离
Rng = 600.0
-- 效果 - 目标
TargetArt = ""

[Aroc]
_parent = "Aroc"
-- 需求
Requires = ""

[Aspl]
_parent = "Aspl"
-- 效果 - 施法者
CasterArt = "Abilities\\Spells\\Orc\\EtherealForm\\SpiritWalkerChange.mdl"

[Asth]
_parent = "Asth"
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"

[Astn]
_parent = "Astn"
-- 需求
Requires = ""

[Atdp]
_parent = "Atdp"
-- 效果 -特殊
SpecialArt = "az_xuanfeng02.mdx"

[Atsp]
_parent = "Atsp"
-- 影响区域
Area = 400.0
-- 空中时间
DataA = 3.0
-- 最小间隔
DataB = 1.0
-- 持续时间 - 普通
Dur = 3.0
-- 持续时间 - 英雄
HeroDur = 3.0

[Sca2]
_parent = "Sca2"
-- 需求
Requires = ""

[Sch4]
_parent = "Sch4"
-- 效果 -特殊
SpecialArt = "war3mapimported\\auraofsummon.mdx"

[A000]
_parent = "ACac"
-- 影响区域
Area = 1000.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (309)tga.blp"
-- 魔法效果
BuffID = "B02A"
-- 攻击伤害增加(%)
DataA = -0.2
-- 编辑器后缀
EditorSuffix = "2"
-- 名字
Name = "召唤物·死亡之主"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "恐怖嚎叫"
-- 提示工具 - 普通 - 扩展
Ubertip = "降低周围敌人20%攻击力"
-- 种族
race = "human"
-- 目标允许
targs = "ground,enemies,air"

[A001]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNVampiricAura.blp"
-- 名字
Name = "召唤物·吸血光环"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "次级吸血光环"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r提高周围友军10点吸血"

[A002]
_parent = "Aspb"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (367).blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 法术列表
DataA = "A0A8,A0C1,A0BY"
-- 最小法术数量
DataC = 12
-- 最大法术数量
DataD = 12
-- 名字
Name = "·专精·选择"
-- 提示工具 - 普通
Tip = "专精选择"
-- 物品技能
item = 0
-- 种族
race = "human"

[A003]
_parent = "AEme"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 魔法施放时间间隔
Cool = {0.0, 0.0, 0.0}
-- 魔法消耗
Cost = {0, 0, 0}
-- 变形生命值奖励
DataE = {0.0, 0.0, 0.0}
-- 持续时间 - 普通
Dur = {0.0, 0.0, 0.0}
-- 持续时间 - 英雄
HeroDur = {20.0, 20.0, 20.0}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·女妖形态"
-- 变化形态单位
UnitID = {
"H002",
"H00B",
"H018",
}
-- 英雄技能
hero = 0
-- 等级
levels = 3
-- 种族
race = "human"

[A004]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (278).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = {120.0, 120.0, 120.0}
-- 施法持续时间
DataA = {0.0, 0.0, 0.0}
-- 选项
DataC = {1, 1, 1}
-- 动作持续时间
DataD = {0.0, 0.0, 0.0}
-- 使其他技能无效
DataE = {0, 0, 0}
-- 基础命令ID
DataF = {
"militia",
"militia",
"militia",
}
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "·专精·诅咒·女妖形态"
-- 图标 - 学习
ResearchArt = "replaceabletextures\\commandbuttons\\lo (2).blp"
-- 提示工具 - 学习
Researchtip = "|CffFFFF8C未解锁技能|r"
-- 提示工具 - 学习 - 扩展
Researchubertip = "选择专精后解锁"
-- 施法距离
Rng = {700.0, 500.0, 500.0}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
"|Cff73FFB9女妖形态lv1|r[|Cffffc92cR|r]",
"|Cff73FFB9女妖形态lv2|r[|Cffffc92cR|r]",
"|Cff73FFB9女妖形态lv3|r[|Cffffc92cR|r]",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
[=[
|Cff999999CD：160s|r|Cffffc92c
|n主动:|r变为女妖形态，提高|Cffffff80100|r吸血和|Cffffff8c100%|r闪避，持续|Cffffff8c20|r秒]=],
[=[
|Cff999999CD：120s|r|Cffffc92c
|n主动:|r变为女妖形态，提高|Cffffff80100|r吸血和|Cffffff8c100%|r闪避，持续|Cffffff8c20|r秒]=],
[=[
|Cff999999CD：80s|r|Cffffc92c
|n主动:|r变为女妖形态，提高|Cffffff80100|r吸血和|Cffffff8c100%|r闪避，持续|Cffffff8c20|r秒]=],
}
-- 英雄技能
hero = 0
-- 种族
race = "human"

[A005]
_parent = "AHad"
-- 影响区域
Area = 1000.0
-- 魔法效果
BuffID = "B03C"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 防御奖励
DataA = -100.0
-- 名字
Name = "·怪物·死亡之愿"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "self"

[A006]
_parent = "ACtb"
-- 魔法施放时间间隔
Cool = 100.0
-- 伤害
DataA = 400.0
-- 持续时间 - 普通
Dur = 0.01
-- 持续时间 - 英雄
HeroDur = 0.01
-- 名字
Name = "·怪物·投石"
-- 施法距离
Rng = 450.0
-- 种族
race = "human"

[A007]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceoff"
-- 编辑器后缀
EditorSuffix = "7"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·B·7"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "8"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A008]
_parent = "Afae"
-- 魔法效果
BuffID = "B03D"
-- 魔法施放时间间隔
Cool = 20.0
-- 魔法消耗
Cost = 0
-- 防御减少
DataA = 25
-- 持续时间 - 普通
Dur = 20.0
-- 持续时间 - 英雄
HeroDur = 20.0
-- 名字
Name = "·怪物·虚无诅咒"
-- 种族
race = "human"

[A009]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (175)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "animatedead"
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·诅咒·爆炸射击"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9暗影冲击|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r每攻击|Cffffff8c8|r次释放暗影冲击造成|Cffffff8c敏捷x4|r的伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00A]
_parent = "ANdh"
-- 影响区域
Area = 10.0
-- 魔法效果
BuffID = "B03J"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 禁止类型
DataA = 3
-- 失误几率(%)
DataB = 0.0
-- 移动速度减少(%)
DataC = 0.0
-- 持续时间 - 普通
Dur = 2.0
-- 持续时间 - 英雄
HeroDur = 2.0
-- 效果 - 射弹弧度
Missilearc = 0.0
-- 效果 - 投射物图像
Missileart = ""
-- 效果 - 射弹速度
Missilespeed = 0
-- 名字
Name = "召唤物·恐惧"
-- 施法距离
Rng = 99999.0
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00B]
_parent = "Aegr"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (65)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 所受穿刺伤害(%)
DataA = 1.0
-- 所受魔法伤害(%)
DataE = 0.5
-- 编辑器后缀
EditorSuffix = "50%"
-- 名字
Name = "·魔抗"
-- 物品技能
item = 1
-- 种族
race = "unknown"

[A00C]
_parent = "AIlu"
-- 获得木材
DataA = 0

[A00D]
_parent = "AIgo"
-- 获得金钱
DataA = 0

[A00E]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "虚空领主"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "shadowwell.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A00F]
_parent = "AHbh"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNGrabTree.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 重击几率
DataA = 100.0
-- 伤害奖励
DataC = 1.0
-- 持续时间 - 普通
Dur = 1.0
-- 编辑器后缀
EditorSuffix = "憎恶"
-- 名字
Name = "召唤物·重击"
-- 提示工具 - 普通
Tip = "猛击"
-- 提示工具 - 普通 - 扩展
Ubertip = "攻击时眩晕敌人0.25秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A00G]
_parent = "AUau"
-- 影响区域
Area = 100.0
-- 魔法效果
BuffID = "B005"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = 0.0
-- 生命回复增加
DataB = 0.1
-- 百分比奖励
DataC = 1
-- 名字
Name = "·特效·亡者复苏"
-- 效果 - 目标
TargetArt = "Abilities\\Spells\\Items\\ClarityPotion\\ClarityTarget.mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 1
-- 种族
race = ""
-- 目标允许
targs = "self"

[A00H]
_parent = "AIem"
-- 取得经验值
DataA = 0

[A00I]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "军马"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Spells\\Undead\\UnholyAura\\UnholyAura.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A00J]
_parent = "Aloc"
-- 名字
Name = "W·蝗虫"
-- 种族
race = "unknown"

[A00K]
_parent = "ANcl"
-- 影响区域
Area = {400.0, 0.0}
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (278).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = {60.0, 60.0}
-- 施法持续时间
DataA = {0.0, 0.0}
-- 选项
DataC = {1, 1}
-- 动作持续时间
DataD = {0.0, 0.0}
-- 使其他技能无效
DataE = {0, 0}
-- 基础命令ID
DataF = {
"ambush",
"ambush",
}
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "终极技能·女妖形态"
-- 施法距离
Rng = {1500.0, 500.0}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
"|Cff73FFB9终极技能·女妖形态|r[|Cffffc92cR|r]",
"|Cff73FFB9终极技能·女妖形态|r[|Cffffc92cR|r]",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
"|CffFFFF8C主动：|r变为女妖形态，提高|Cffffff8c2000|r全属性和|Cffffff8c20000|r生命值，持续|Cffffff8c20|r秒",
"|CffFFFF8C主动：|r变为女妖形态，提高|Cffffff8c2000|r全属性和|Cffffff8c20000|r生命值，持续|Cffffff8c20|r秒",
}
-- 英雄技能
hero = 0
-- 等级
levels = 2
-- 种族
race = "human"

[A00L]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "终极技能·死亡之力"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9死亡之力|r"
-- 提示工具 - 普通 - 扩展
Ubertip = [=[
|CffA338EE每秒敏捷+4|n每秒攻击力+4|n生命值+5000|n暴击伤害+40%|n致命伤害+40%|n攻击速度+50%|n|r|Cffffc92c
“为了希尔瓦娜斯的胜利”|r]=]
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00M]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·精英单位"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A00N]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (431).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 60.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "终极技能·暗影匕首"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9暗影匕首|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r攻击有几率对周围敌人造成|Cffffff8c敏捷x3+目标2%生命值|r的伤害|n|Cffffc92c主动：|r提高100%闪避，持续12秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00O]
_parent = "AOcr"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 致命一击几率
DataA = 10.0
-- 伤害倍数
DataB = 4.0
-- 编辑器后缀
EditorSuffix = "骷髅弓箭手"
-- 名字
Name = "Z-致命一击"
-- 提示工具 - 普通
Tip = "致命"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动:|r 有几率造成4倍伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A00P]
_parent = "ANcl"
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (278).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 60.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 编辑器后缀
EditorSuffix = "·风行者形态"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·风行"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9女妖形态|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C主动:|r变为女妖形态，提高100%闪避，持续8秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00Q]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 影响区域
Area = 800.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbc (54).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 60.0
-- 施法持续时间
DataA = 0.0
-- 目标类型
DataB = 2
-- 选项
DataC = 3
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "·专精·通灵·死亡之主契约"
-- 施法距离
Rng = 2400.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9通灵领主契约|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = [=[
|CffFFC92C被动：|r召唤物死亡时提高英雄|Cffffff8c20%|r生命值，持续10秒|n|Cffffc92c
主动：|r召唤死亡之主撕裂与暗影界的空间，造成|Cffffff8c5|r秒眩晕并|Cff73ffb9抹杀|r非BOSS敌人，持续|Cffffff8c15|r秒]=]
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground"

[A00R]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (697)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "Q"
-- 名字
Name = "·专精·通灵"
-- 施法距离
Rng = 0.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9黑暗仆从|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC926被动：|r周期召唤1只黑暗哨兵协助战斗，持续20秒|n|n|Cff999999CD：20s|r"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00S]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (737)tga.blp"
-- 名字
Name = "虚空领主·虚空能量"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "黑暗打击"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r攻击时有|Cffffff8c20%|r几率眩晕敌人1秒"

[A00T]
_parent = "ACce"
-- 影响区域
Area = 250.0
-- 分裂伤害参数
DataA = 1.0
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "召唤物·黑暗之奴·分裂"
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A00U]
_parent = "ANcl"
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (652).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 30.0
-- 施法持续时间
DataA = 0.0
-- 目标类型
DataB = 2
-- 选项
DataC = 3
-- 动作持续时间
DataD = 1.2
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "终极技能·生命汲取"
-- 施法距离
Rng = 1800.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9生命汲取|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC926主动：|r造成|Cffffff8c200%生命值+敏捷x20|r的伤害并在接下来10秒内回复等量生命值|n|n|Cff999999每10000生命值提高5%最大生命值伤害|r"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A00V]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (286)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "终极技能·憎恨"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9终极技能·憎恨lv1|r[|Cffffc92c被动|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C主动：|r提高|Cffffff8c100%|r暴击和致命一击几率，持续|Cffffff8c15|r秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00W]
_parent = "AHad"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (431)tga.blp"
-- 魔法效果
BuffID = "B03E"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 防御奖励
DataA = -10.0
-- 名字
Name = "召唤物·暗影匕首破甲"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "self"

[A00X]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (292).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 120.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "终极技能·亡者大军"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9亡者转换|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = [=[
|CffFFC92C被动：|r每召唤1个单位提高10%生命值，持续10秒|n|Cffffc92c
主动：|r召唤10只亡灵弓箭手，弓箭手每次攻击造成|Cffffff8c敏捷X4|r的伤害，持续10秒]=]
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00Y]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = "attack"
-- 影响区域
Area = 550.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (87).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 60.0
-- 施法持续时间
DataA = 0.0
-- 目标类型
DataB = 2
-- 选项
DataC = 3
-- 动作持续时间
DataD = 1.25
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "终极技能·哀恸箭"
-- 施法距离
Rng = 9999.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9哀恸箭|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C主动：|r对周围敌人造成|Cffffff8c敏捷x60|r的伤害并降低30%攻击力，持续5秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A00Z]
_parent = "Avul"
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "W·无敌的"

[A010]
_parent = "AOae"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (727)tga.blp"
-- 魔法效果
BuffID = "B03F"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = 0.0
-- 攻击速度增加(%)
DataB = 0.0
-- 名字
Name = "·特效·女妖之躯"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 1
-- 种族
race = "unknown"
-- 目标允许
targs = "self"

[A011]
_parent = "Aasl"
-- 影响区域
Area = 1200.0
-- 魔法效果
BuffID = "B00X"
-- 降低移动速度(%)
DataA = -0.5
-- 编辑器后缀
EditorSuffix = "50%"
-- 名字
Name = "J-减速光环"
-- 目标允许
targs = "enemies,ground,nonancient,air"

[A012]
_parent = "Aslo"
-- 魔法效果
BuffID = "B03H"
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 降低移动速度(%)
DataA = 10.0
-- 降低攻击速度(%)
DataB = 10.0
-- 总是自动施放
DataC = 1
-- 持续时间 - 普通
Dur = 3.0
-- 持续时间 - 英雄
HeroDur = 3.0
-- 名字
Name = "召唤物·死寒之拥"
-- 施法距离
Rng = 99999.0

[A013]
_parent = "AIft"
-- 魔法效果
BuffID = "B03I"
-- 附加伤害
DataA = 0.0
-- 持续时间 - 普通
Dur = 2.0
-- 持续时间 - 英雄
HeroDur = 0.1
-- 效果 - 投射物图像
Missileart = ""
-- 名字
Name = "触手减速"
-- 效果 - 目标
TargetArt = ""
-- 效果 - 目标附加点1
Targetattach = ""

[A014]
_parent = "Awan"
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "W·游荡者"

[A015]
_parent = "Aroc"
-- 影响区域
Area = 750.0
-- 目标伤害
DataA = 0.0
-- 最大输出伤害
DataB = 0.0
-- 最大目标数
DataC = 1
-- 持续时间 - 普通
Dur = 0.0
-- 编辑器后缀
EditorSuffix = "默认"
-- 效果 - 射弹自导允许
MissileHoming = 0
-- 效果 - 射弹弧度
Missilearc = 0.0
-- 效果 - 投射物图像
Missileart = "Abilities\\Spells\\Other\\BlackArrow\\BlackArrowMissile.mdl"
-- 效果 - 射弹速度
Missilespeed = 2800
-- 名字
Name = "·多重·黑暗之箭"
-- 需求
Requires = ""
-- 物品技能
item = 1
-- 种族
race = ""
-- 目标允许
targs = "ground,enemies,air"

[A016]
_parent = "Asth"
-- 名字
Name = "·神器·永夜"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A017]
_parent = "Asth"
-- 名字
Name = "·神器·破晓"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A018]
_parent = "Asth"
-- 名字
Name = "·神器·征伐"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A019]
_parent = "Asth"
-- 名字
Name = "·神器·丧钟"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01A]
_parent = "Asth"
-- 名字
Name = "·神器·灾祸"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01B]
_parent = "Asth"
-- 名字
Name = "·神器·死亡"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01C]
_parent = "Asth"
-- 名字
Name = "·天赋·黑暗中的独影"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01D]
_parent = "Asth"
-- 名字
Name = "·天赋·比疯狂更疯狂"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01E]
_parent = "Asth"
-- 名字
Name = "·天赋·欢愉"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01F]
_parent = "Asth"
-- 名字
Name = "·天赋·黑暗游侠兜帽"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01G]
_parent = "Asth"
-- 名字
Name = "·天赋·黑暗游侠大氅"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01H]
_parent = "Asth"
-- 名字
Name = "·天赋·黑暗游侠肩甲"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 种族
race = "nightelf"

[A01I]
_parent = "AUls"
-- 影响区域
Area = 1000.0
-- 魔法施放时间间隔
Cool = 30.0
-- 魔法消耗
Cost = 0
-- 单位释放间隔
DataB = 0.1
-- 每个目标最大蝗虫数量
DataC = 10
-- 生命偷取参数
DataD = 0.0
-- 生命偷取极限
DataE = 999999.0
-- 持续时间 - 普通
Dur = 120.0
-- 持续时间 - 英雄
HeroDur = 120.0
-- 名字
Name = "·怪物·SOUL"
-- 蝗虫单位类型
UnitID = "u000"
-- 英雄技能
hero = 0
-- 种族
race = "human"

[A01J]
_parent = "AOhx"
-- 魔法施放时间间隔
Cool = 20.0
-- 魔法消耗
Cost = 0
-- 持续时间 - 普通
Dur = 3.0
-- 持续时间 - 英雄
HeroDur = 3.0
-- 名字
Name = "·怪物·妖术"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A01K]
_parent = "AUsl"
-- 魔法施放时间间隔
Cool = 5.0
-- 魔法消耗
Cost = 0
-- 持续时间 - 普通
Dur = 3.0
-- 持续时间 - 英雄
HeroDur = 3.0
-- 名字
Name = "·怪物·睡眠"
-- 施法距离
Rng = 9999.0
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A01L]
_parent = "Afr2"
-- 名字
Name = "召唤物·霜之攻击"
-- 种族
race = "human"

[A01M]
_parent = "ANsi"
-- 影响区域
Area = 600.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 魔法施放时间间隔
Cool = 60.0
-- 魔法消耗
Cost = 0
-- 禁止类型
DataA = 3
-- 持续时间 - 普通
Dur = 3.0
-- 持续时间 - 英雄
HeroDur = 3.0
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "终极技能·静默诅咒"
-- 施法距离
Rng = 1600.0
-- 提示工具 - 普通
Tip = "|Cff73FFB9静默诅咒|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C主动:|r对范围敌人造成敏捷X20的伤害并且静默3秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A01N]
_parent = "ACce"
-- 影响区域
Area = 400.0
-- 分裂伤害参数
DataA = 1.0
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "召唤物·死亡之主·分裂"
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A01O]
_parent = "AOre"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 魔法施放时间
Cast = 0.0
-- 魔法施放时间间隔
Cool = 0.0
-- 效果 - 目标点
EffectArt = ""
-- 名字
Name = "召唤物·重生"
-- 英雄技能
hero = 0
-- 种族
race = "human"

[A01P]
_parent = "AAns"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (360).blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 魔法施放时间间隔
Cool = 0.01
-- 黄金消耗
DataA = 0
-- 木材消耗
DataB = 200
-- 热键 - 普通
Hotkey = "F"
-- 名字
Name = "·专精·死亡之力"
-- 提示工具 - 普通
Tip = "死亡之力[|Cffffc92cF|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFFF8C获得死亡力量|r"
-- 种族
race = "human"
-- 等级要求
reqLevel = 1

[A01Q]
_parent = "ANcl"
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbc (39)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "antimagicshell"
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "终极形态·冰霜骨龙萨菲隆"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|CffA338EE穿刺之箭|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r多重之箭几率|Cffffff8c+20%|r|n|Cffffc92c被动：|r攻击有|Cffffff8c25%|r几率对目标造成|Cffffff8c智力X3|r的伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A01R]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·BOSS"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A01S]
_parent = "ACfn"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 范围目标伤害
DataA = 0.0
-- 特定目标伤害
DataB = 0.0
-- 持续时间 - 普通
Dur = 2.0
-- 编辑器后缀
EditorSuffix = ""
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = 2.0
-- 名字
Name = "召唤物·黑冰箭"
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A01T]
_parent = "ACfn"
-- 效果 - 施法动作
Animnames = "spell"
-- 魔法施放时间间隔
Cool = 30.0
-- 魔法消耗
Cost = 0
-- 范围目标伤害
DataA = 0.0
-- 特定目标伤害
DataB = 500.0
-- 持续时间 - 普通
Dur = 1.5
-- 编辑器后缀
EditorSuffix = "X"
-- 持续时间 - 英雄
HeroDur = 1.5
-- 名字
Name = "·怪物·霜冻新星"
-- 种族
race = "human"

[A01U]
_parent = "Apiv"
-- 自动获取攻击目标
DataA = 0
-- 持续时间 - 普通
Dur = -1.0
-- 持续时间 - 英雄
HeroDur = -1.0
-- 名字
Name = "W·停止攻击"

[A01V]
_parent = "AOre"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 魔法施放时间
Cast = 0.0
-- 魔法施放时间间隔
Cool = 3600.0
-- 重生延迟
DataA = 4.0
-- 编辑器后缀
EditorSuffix = "BOSS"
-- 效果 - 目标点
EffectArt = ""
-- 名字
Name = "·怪物·重生"
-- 英雄技能
hero = 0
-- 种族
race = "human"

[A01W]
_parent = "ACac"
-- 影响区域
Area = 1500.0
-- 魔法效果
BuffID = "B02A"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 攻击伤害增加(%)
DataA = -0.3
-- 名字
Name = "召唤物·女妖形态·攻击力"
-- 效果 - 目标
TargetArt = ""
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A01X]
_parent = "Atau"
-- 影响区域
Area = 1000.0
-- 魔法施放时间间隔
Cool = 0.0
-- 名字
Name = "召唤物·嘲讽"
-- 种族
race = "human"

[A01Y]
_parent = "ANcl"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSkeletonArcher.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "absorb"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·皮肤·骸骨射手"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "选择：骸骨射手"
-- 提示工具 - 普通 - 扩展
Ubertip = "攻击间隔-5%|n|n女妖形态变为寒冰骨龙|n每3秒对周围造成敏捷x3的伤害并减少敌人50%攻击和移动速度"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "undead"

[A01Z]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (42)tga.blp"
-- 名字
Name = "召唤物·顺劈斩"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "顺劈斩"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r攻击造成力量X1的范围伤害"

[A020]
_parent = "ANcl"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "absorb"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·皮肤·光明游侠"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "选择：光明游侠"
-- 提示工具 - 普通 - 扩展
Ubertip = "攻击间隔-5%|n|n女妖形态变为死亡之女|n每3秒对周围造成敏捷x3的伤害并减少敌人50%攻击和移动速度"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "undead"

[A021]
_parent = "ANcl"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "absorb"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·皮肤·银月游侠"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "选择：影魔"
-- 提示工具 - 普通 - 扩展
Ubertip = "攻击间隔-5%|n|n女妖形态变为死亡之女|n每3秒对周围造成敏捷x3的伤害并减少敌人50%攻击和移动速度"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "undead"

[A022]
_parent = "ANcl"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "absorb"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·皮肤·凤凰弓"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "选择：影魔"
-- 提示工具 - 普通 - 扩展
Ubertip = "攻击间隔-5%|n|n女妖形态变为死亡之女|n每3秒对周围造成敏捷x3的伤害并减少敌人50%攻击和移动速度"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "undead"

[A023]
_parent = "Acrs"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 失误几率(%)
DataA = 0.5
-- 持续时间 - 普通
Dur = 3.0
-- 编辑器后缀
EditorSuffix = "50%"
-- 持续时间 - 英雄
HeroDur = 3.0
-- 名字
Name = "召唤物·诅咒"
-- 施法距离
Rng = 9999.0
-- 种族
race = "human"

[A024]
_parent = "AHtb"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (79)tga.blp"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 0.0
-- 持续时间 - 普通
Dur = 2.0
-- 持续时间 - 英雄
HeroDur = 2.0
-- 效果 - 投射物图像
Missileart = ""
-- 效果 - 射弹速度
Missilespeed = 0
-- 名字
Name = "召唤物·风暴之锤"
-- 施法距离
Rng = 99999.0
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A025]
_parent = "ANcl"
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "K-空物品"
-- 施法距离
Rng = 700.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = ""
-- 提示工具 - 普通 - 扩展
Ubertip = ""
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 1
-- 种族
race = "other"

[A026]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsubmerge"
-- 持续时间 - 普通
Dur = 0.1
-- 编辑器后缀
EditorSuffix = "1"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·B·1"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "2"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A027]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsubmerge"
-- 编辑器后缀
EditorSuffix = "1"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·C·1"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "2"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A028]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsubmerge"
-- 编辑器后缀
EditorSuffix = "1"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·D·1"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "2"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A029]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsubmerge"
-- 编辑器后缀
EditorSuffix = "1"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·E·1"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "5"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02A]
_parent = "Awrs"
-- 地形变形幅度
DataB = 0.0
-- 地形变形持续时间(毫秒)
DataC = 0
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·践踏 BOSS"
-- 种族
race = "human"

[A02B]
_parent = "ANpi"
-- 影响区域
Area = 2000.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (637)tga.blp"
-- 伤害/间隔时间
DataA = 3000.0
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·献祭"
-- 种族
race = "human"

[A02C]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (175)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "animatedead"
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·诅咒·哀恸箭"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9暗影冲击|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r每攻击|Cffffff8c8|r次释放暗影冲击造成|Cffffff8c敏捷x4|r的伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02D]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "子弹"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\Bullet.mdx"
-- 效果 - 射弹速度
Missilespeed = 1900
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A02E]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (175)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "animatedead"
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·诅咒·仇杀"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9暗影冲击|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r每攻击|Cffffff8c8|r次释放暗影冲击造成|Cffffff8c敏捷x4|r的伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02F]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (695)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceoff"
-- 编辑器后缀
EditorSuffix = "7"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·C·7"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9黑暗之奴|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "8"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02G]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeance"
-- 持续时间 - 普通
Dur = 0.1
-- 编辑器后缀
EditorSuffix = "2"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·B·2"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "3"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02H]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeance"
-- 编辑器后缀
EditorSuffix = "2"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·C·2"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "3"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02I]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeance"
-- 编辑器后缀
EditorSuffix = "2"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·D·2"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "3"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02J]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeance"
-- 编辑器后缀
EditorSuffix = "2"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·E·2"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "3"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02K]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNPossession.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 180.0
-- 施法持续时间
DataA = 0.0
-- 目标类型
DataB = 1
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "终极技能·占据"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9占据|r[|Cffffc92cR|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C主动：|r占据目标灵魂|n|n|Cff999999ESC取消占据效果|r"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,nonhero,air"

[A02Q]
_parent = "AOw2"
-- 效果 - 施法动作
Animnames = "attack,slam"
-- 影响区域
Area = 800.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 效果 - 施法者
CasterArt = "az_crixalis(1)_c5-8.mdl"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 0.0
-- 持续时间 - 普通
Dur = 4.0
-- 编辑器后缀
EditorSuffix = "4"
-- 持续时间 - 英雄
HeroDur = 4.0
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "Z-战争践踏"
-- 提示工具 - 普通
Tip = "战争践踏(|Cffffcc00R|r)"
-- 提示工具 - 普通 - 扩展
Ubertip = "对周围敌人造成2000伤害并眩晕4秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A02S]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·英雄单位"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A02T]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "死寒"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Spells\\Undead\\FrostArmor\\FrostArmorTarget.mdl"
-- 效果 - 目标附加点1
Targetattach = "chest"

[A02V]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (360)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceoff"
-- 编辑器后缀
EditorSuffix = "7"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·D·7"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9虚空之遗|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "8"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A02Z]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstableconcoction"
-- 持续时间 - 普通
Dur = 0.1
-- 编辑器后缀
EditorSuffix = "3"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·B·3"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "4"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A030]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstableconcoction"
-- 编辑器后缀
EditorSuffix = "3"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·C·3"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "4"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A031]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstableconcoction"
-- 编辑器后缀
EditorSuffix = "3"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·D·3"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "4"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A032]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstableconcoction"
-- 编辑器后缀
EditorSuffix = "3"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·E·3"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "4"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A037]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstoneform"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·D·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9复仇之魂|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "1"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A038]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "奖励"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Objects\\InventoryItems\\QuestionMark\\QuestionMark.mdl"
-- 效果 - 目标附加点1
Targetattach = "overhead"

[A039]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsummon"
-- 持续时间 - 普通
Dur = 0.1
-- 编辑器后缀
EditorSuffix = "4"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·B·4"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "5"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A03A]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsummon"
-- 编辑器后缀
EditorSuffix = "4"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·C·4"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "5"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A03B]
_parent = "AEbl"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 0
-- 魔法消耗
Cost = 0
-- 最大范围
DataA = 800.0
-- 最小范围
DataB = 0.0
-- 持续时间 - 普通
Dur = 0.01
-- 编辑器后缀
EditorSuffix = "英雄"
-- 热键 - 普通
Hotkey = "D"
-- 名字
Name = "功能·闪现"
-- 提示工具 - 普通
Tip = "闪现(|Cffffcc00D|r)"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff99E5FF立即移动到指定位置|n|n|r|Cff26ff38TAB查看属性面板|n|n|r|Cff999999CD:10s|r"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "unknown"

[A03C]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsummon"
-- 编辑器后缀
EditorSuffix = "4"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·D·4"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "5"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A03D]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "阳炎护手"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firehands.mdl"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标 - 附加数量
Targetattachcount = 1

[A03F]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsummon"
-- 编辑器后缀
EditorSuffix = "4"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·E·4"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "5"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A03G]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "古神触手"
-- 效果 - 投射物图像
Missileart = "war3mapimported\\shot ii orange.mdx"
-- 效果 - 射弹速度
Missilespeed = 1600
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A03H]
_parent = "AOcr"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 致命一击几率
DataA = 100.0
-- 伤害倍数
DataB = 1.5
-- 编辑器后缀
EditorSuffix = "100% 1.5"
-- 名字
Name = "Z-致命一击"
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A03N]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstoneform"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·A·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "1"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A03O]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsubmerge"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·B·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "2"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A03R]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "复仇之怒"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\HolyAurora.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A03S]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "复仇之怒2"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\war3ake.com - fly_01.mdx"
-- 效果 - 目标附加点1
Targetattach = "chest"

[A03V]
_parent = "Aasl"
-- 影响区域
Area = 500.0
-- 降低移动速度(%)
DataA = -0.4
-- 编辑器后缀
EditorSuffix = "40%"
-- 名字
Name = "J-减速光环"

[A03W]
_parent = "AIct"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 魔法施放时间间隔
Cool = 0.0
-- 持续时间 - 普通
Dur = 60.0
-- 编辑器后缀
EditorSuffix = "正版"
-- 持续时间 - 英雄
HeroDur = 60.0

[A03X]
_parent = "AHtb"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 1.0
-- 持续时间 - 普通
Dur = 2.0
-- 持续时间 - 英雄
HeroDur = 2.0
-- 效果 - 射弹自导允许
MissileHoming = 0
-- 效果 - 投射物图像
Missileart = ""
-- 效果 - 射弹速度
Missilespeed = 0
-- 名字
Name = "召唤物·单晕"
-- 施法距离
Rng = 99999.0
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A040]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unsummon"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·C·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "3"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A042]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unwindwalk"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·D·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "4"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A043]
_parent = "AHad"
-- 影响区域
Area = 999999.0
-- 魔法效果
BuffID = "B024"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 防御奖励
DataA = -30.0
-- 名字
Name = "·光环·腐蚀光环"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 1
-- 种族
race = ""
-- 目标允许
targs = "enemies,ground,air"

[A044]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeance"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·E·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "5"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A045]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceinstant"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·F·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "6"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A046]
_parent = "Aasl"
-- 影响区域
Area = 500.0
-- 魔法效果
BuffID = "B03A"
-- 编辑器后缀
EditorSuffix = "60% 虚空之遗"
-- 名字
Name = "J-减速光环"

[A048]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceoff"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·G·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "7"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A049]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 5.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceon"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "··被动·H·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38解锁一个技能|r"
-- 热键 -关闭
Unhotkey = "8"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A04A]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (291)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceoff"
-- 编辑器后缀
EditorSuffix = "7"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·E·7"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9哀恸箭|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "8"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A04C]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstoneform"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·E·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9女妖哀嚎|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "1"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A04F]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstoneform"
-- 持续时间 - 普通
Dur = 0.1
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·B·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "1"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A04H]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unstoneform"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·C·0"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9哀恸箭|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "1"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A04J]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceinstant"
-- 持续时间 - 普通
Dur = 0.1
-- 编辑器后缀
EditorSuffix = "5"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·B·5"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "6"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A04K]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\181.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 1.0
-- 施法持续时间
DataA = 0.0
-- 目标类型
DataB = 1
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "massteleport"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "功能·给予物品"
-- 施法距离
Rng = 10000.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "给予物品[|Cffffcc19R|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = "将指定物品给予英雄"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "unknown"
-- 目标允许
targs = "item"

[A04L]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "光晕"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\RingOfBright.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A04M]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unwindwalk"
-- 编辑器后缀
EditorSuffix = "6"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "7·被动·B·6"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "7"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A04Z]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "黑暗之箭"
-- 效果 - 投射物图像
Missileart = "Abilities\\Spells\\Other\\BlackArrow\\BlackArrowMissile.mdl"
-- 效果 - 射弹速度
Missilespeed = 2300
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A050]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceinstant"
-- 编辑器后缀
EditorSuffix = "5"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·C·5"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "6"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A051]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unwindwalk"
-- 编辑器后缀
EditorSuffix = "6"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "7·被动·C·6"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "7"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A056]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (510)tga.blp"
-- 编辑器后缀
EditorSuffix = "2"
-- 名字
Name = "召唤物·顺劈斩"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "顺劈斩"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r攻击造成力量X3的范围伤害|n|n|Cff999999英雄射程越远，伤害范围越大|r"

[A05C]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceinstant"
-- 编辑器后缀
EditorSuffix = "5"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·D·5"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "6"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A05E]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unwindwalk"
-- 编辑器后缀
EditorSuffix = "6"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "7·被动·D·6"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "7"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A05L]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "vengeanceinstant"
-- 编辑器后缀
EditorSuffix = "5"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·被动·E·5"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "6"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A05Q]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNSelectHeroOn.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "unwindwalk"
-- 编辑器后缀
EditorSuffix = "6"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "7·被动·E·6"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9未解锁|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38消耗400木材解锁随机天赋|r"
-- 热键 -关闭
Unhotkey = "7"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A05U]
_parent = "AHbh"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 伤害奖励
DataC = 100.0
-- 持续时间 - 普通
Dur = 1.5
-- 编辑器后缀
EditorSuffix = "20% 100"
-- 持续时间 - 英雄
HeroDur = 1.5
-- 名字
Name = "·怪物·重击"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "enemies,ground,air"

[A05V]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "邪能灌注"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Spells\\Human\\ManaFlare\\ManaFlareBase.mdl"
-- 效果 - 目标附加点1
Targetattach = "chest"

[A05X]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "灵魂链接"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Spells\\Human\\ManaFlare\\ManaFlareBoltImpact.mdl"
-- 效果 - 目标附加点1
Targetattach = "chest"

[A06G]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "箭袋"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\Shot II Purple.mdx"
-- 效果 - 射弹速度
Missilespeed = 1600
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A06L]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "破咒师"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\lightaura.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A06Q]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "火"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Environment\\SmallBuildingFire\\SmallBuildingFire2.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A06U]
_parent = "AOre"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 魔法施放时间
Cast = 0.0
-- 魔法施放时间间隔
Cool = 0.0
-- 重生延迟
DataA = 0.0
-- 名字
Name = "W-防消失"
-- 英雄技能
hero = 0

[A06X]
_parent = "Aspb"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (367).blp"
-- 法术列表
DataA = "A022,A020,A01Y,A021,A0UD"
-- 最小法术数量
DataC = 12
-- 最大法术数量
DataD = 12
-- 基础命令ID
DataE = "absorb"
-- 名字
Name = "功能·选择皮肤"
-- 提示工具 - 普通
Tip = "选择皮肤"
-- 提示工具 - 普通 - 扩展
Ubertip = "|Cff26FF38选择一个皮肤，请在游戏开局时选择|r"
-- 物品技能
item = 0
-- 种族
race = "unknown"

[A072]
_parent = "Aasl"
-- 影响区域
Area = 150.0
-- 魔法效果
BuffID = "B031"
-- 编辑器后缀
EditorSuffix = "60% 暗影匕首"
-- 名字
Name = "J-减速光环"

[A074]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (737)tga.blp"
-- 名字
Name = "虚空领主·虚无之触"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "黑暗打击"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r攻击时有|Cffffff8c20%|r几率眩晕敌人1秒"

[A07B]
_parent = "AUau"
-- 影响区域
Area = 1500.0
-- 移动速度增加(%)
DataA = 0.0
-- 生命回复增加
DataB = 0.01
-- 百分比奖励
DataC = 1
-- 名字
Name = "·怪物·回血"
-- 效果 - 目标
TargetArt = "unholyaura.mdx"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "self"

[A07D]
_parent = "AAns"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNStatUp.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 魔法施放时间间隔
Cool = {0.01, 0.01, 0.01, 0.01, 0.01, 0.01}
-- 黄金消耗
DataA = {0, 0, 0, 0, 0, 0}
-- 木材消耗
DataB = {400, 600, 800, 1000, 1200, 1400}
-- 基础命令ID
DataE = {
"neutralspell",
"neutralspell",
"neutralspell",
"neutralspell",
"neutralspell",
"neutralspell",
}
-- 向技能拥有者收费
DataF = {1, 1, 1, 1, 1, 1}
-- 热键 - 普通
Hotkey = "4"
-- 名字
Name = "·专精·晋升"
-- 提示工具 - 普通
Tip = {
"获取天赋[|Cffffc92cF|r]",
"",
"",
"",
"",
"死亡之力",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
"|Cff26FF38获得一个随机天赋|n|n|r|Cff999999所有天赋效果可以叠加|r",
"",
"",
"",
"",
[=[
|CffA338EE每秒敏捷+4|n每秒攻击力+4|n生命值+10000|n暴击伤害+100%|n致命伤害+100%|n攻击速度+100%|n|r|Cffffc92c
“为了希尔瓦娜斯的胜利”|r]=],
}
-- 等级
levels = 6
-- 种族
race = "human"
-- 等级要求
reqLevel = 1

[A07E]
_parent = "AOae"
-- 影响区域
Area = 2000.0
-- 魔法效果
BuffID = "B004"
-- 移动速度增加(%)
DataA = 0.5
-- 攻击速度增加(%)
DataB = 1.0
-- 名字
Name = "虚空领主·吞噬"
-- 效果 - 目标
TargetArt = "war3mapimported\\g5.mdl"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A07F]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (124)tga.blp"
-- 名字
Name = "召唤物·哨兵·急速射击"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "通灵术"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r拥有英雄100%攻击速度"

[A07G]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (396)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 名字
Name = "召唤物·黑暗之奴"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "奴役"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r这是一个召唤物"

[A07H]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (396)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 名字
Name = "·怪物·古神触手"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "奴役"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r这是一个召唤物"

[A07I]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNVampiricAura.blp"
-- 编辑器后缀
EditorSuffix = "2"
-- 名字
Name = "召唤物·吸血光环"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "高级吸血光环"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r提高周围友军50点吸血"

[A07P]
_parent = "AHad"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 防御奖励
DataA = 25.0
-- 名字
Name = "·怪物·十字军光环"
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A085]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "专注光环"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\g2.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A08E]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "秘银弹药"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\brewmastermissile.mdx.mdl"
-- 效果 - 射弹速度
Missilespeed = 1600
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A08F]
_parent = "Aasl"
-- 影响区域
Area = 400.0
-- 魔法效果
BuffID = "B00P"
-- 降低移动速度(%)
DataA = -0.7
-- 编辑器后缀
EditorSuffix = "70% 电"
-- 名字
Name = "J-减速光环"

[A08G]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "符文武器"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\jianzhu5.mdx"
-- 效果 - 目标附加点1
Targetattach = "chest"

[A08S]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (26)tga.blp"
-- 名字
Name = "召唤物·通灵契约"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "通灵契约"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r可以为英雄抵挡一次致命伤害"

[A08T]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (396)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 名字
Name = "召唤物·哨兵"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "奴役"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r这是一个召唤物"

[A08U]
_parent = "AOae"
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (183)tga.blp"
-- 魔法效果
BuffID = "Bplg"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 移动速度增加(%)
DataA = -0.1
-- 攻击速度增加(%)
DataB = -0.3
-- 名字
Name = "召唤物·黑暗之奴·压制"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "压制"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r降低周围敌人|Cffffff8c30%|r攻击速度和|Cffffff8c10%|r移动速度"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A08X]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (90)tga.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 名字
Name = "召唤物·毁灭"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "毁灭"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r天灾巨人领主死亡时会对周围敌人造成|Cffffff8c100%|r生命值的伤害"

[A08Y]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (89)tga.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 名字
Name = "召唤物·余震"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "余震"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r每次攻击造成|Cffffff8c攻击力x2|r的范围物理伤害"

[A090]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (396)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 名字
Name = "·怪物·天灾巨人领主"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "奴役"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r这是一个召唤物"

[A093]
_parent = "Acht"
-- 影响区域
Area = 2000.0
-- 魔法效果
BuffID = "B019"
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 攻击增加(%)
DataA = 0.3
-- 持续时间 - 普通
Dur = 6.0
-- 编辑器后缀
EditorSuffix = ""
-- 持续时间 - 英雄
HeroDur = 6.0
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "召唤物·女妖嚎叫"
-- 种族
race = "human"
-- 目标允许
targs = "ground,enemies,air,organic"

[A094]
_parent = "AHad"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (431)tga.blp"
-- 魔法效果
BuffID = "B03K"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 防御奖励
DataA = -2.0
-- 名字
Name = "召唤物·刺骨破甲"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "self"

[A09I]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "武器 红色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapimported\\0625bladelightblood.mdx"
-- 效果 - 目标附加点2
Targetattach1 = "hand,left"

[A09K]
_parent = "ACbf"
-- 影响区域
Area = 200.0
-- 魔法效果
BuffID = "B028"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 0.0
-- 距离
DataC = 3000.0
-- 最终区域范围
DataD = 200.0
-- 每秒伤害
DataE = 1.0
-- 持续时间 - 普通
Dur = 1.0
-- 持续时间 - 英雄
HeroDur = 1.0
-- 效果 - 投射物图像
Missileart = "az_feiyan.mdl"
-- 效果 - 射弹速度
Missilespeed = 1000
-- 名字
Name = "·马甲·暗影烈焰"
-- 种族
race = "human"
-- 目标允许
targs = "ground,enemies,air"

[A09M]
_parent = "Assp"
-- 单位数量
DataA = 1
-- 单位类型
DataB = "n02G"
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·小蜘蛛"
-- 种族
race = "human"

[A09P]
_parent = "ACdc"
-- 魔法施放时间间隔
Cool = 12.0
-- 魔法消耗
Cost = 0
-- 治疗数值
DataA = 1000.0
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·死亡缠绕"
-- 种族
race = "human"
-- 目标允许
targs = "ground,enemies,air"

[A09Q]
_parent = "Afbk"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (331).blp"
-- 最大损耗魔法-单位
DataA = 10.0
-- 最大损耗魔法-英雄
DataC = 10.0
-- 伤害比率-英雄
DataD = 40.0
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·魔法反馈"

[A09R]
_parent = "AHbh"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 重击几率
DataA = 50.0
-- 伤害奖励
DataC = 300.0
-- 持续时间 - 普通
Dur = 0.1
-- 编辑器后缀
EditorSuffix = "食人魔"
-- 持续时间 - 英雄
HeroDur = 0.1
-- 名字
Name = "·怪物·重击"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "enemies,ground,air"

[A09T]
_parent = "AHbh"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 伤害奖励
DataC = 0.0
-- 持续时间 - 普通
Dur = 1.0
-- 名字
Name = "召唤物·重击"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "enemies,ground,air"

[A09U]
_parent = "AHtb"
-- 魔法施放时间间隔
Cool = 15.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 0.0
-- 持续时间 - 普通
Dur = 2.0
-- 持续时间 - 英雄
HeroDur = 2.0
-- 效果 - 投射物图像
Missileart = "war3mapImported\\stormboltmissile.mdx"
-- 效果 - 射弹速度
Missilespeed = 1200
-- 名字
Name = "·怪物·神圣之锤"
-- 施法距离
Rng = 900.0
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A09Y]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "盾墙"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "az_dun01.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A09Z]
_parent = "AOcr"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 致命一击几率
DataA = 100.0
-- 伤害倍数
DataB = 0.0
-- 不会丢失
DataE = 1
-- 编辑器后缀
EditorSuffix = "100% 0"
-- 名字
Name = "Z-致命一击"
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A0A2]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (175)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 4.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·诅咒"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9暗影冲击|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动:|r释放暗影冲击对前方敌人造成|Cffffff8c智力X4|r的伤害|n|n|Cff999999觉醒：每触发2次暗影冲击就会释放一次群体冲击，造成智力X4的伤害|r"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A0A3]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·进攻单位"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A0A5]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "高级祭司"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "magicseal.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0A8]
_parent = "ANcl"
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbc (39)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "antimagicshell"
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·风行"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|CffA338EE穿刺之箭|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r多重之箭几率|Cffffff8c+20%|r|n|Cffffc92c被动：|r攻击有|Cffffff8c25%|r几率对目标造成|Cffffff8c智力X3|r的伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A0A9]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "白银之手"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "divineseal.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0AC]
_parent = "Aroc"
-- 影响区域
Area = 1200.0
-- 目标伤害
DataA = 0.0
-- 最大输出伤害
DataB = 0.0
-- 最大目标数
DataC = 1
-- 持续时间 - 普通
Dur = 0.0
-- 编辑器后缀
EditorSuffix = "女武神"
-- 效果 - 射弹自导允许
MissileHoming = 0
-- 效果 - 射弹弧度
Missilearc = 0.0
-- 效果 - 投射物图像
Missileart = "war3mapImported\\az2_az_bluedragonpf_missile.mdl"
-- 效果 - 射弹速度
Missilespeed = 2400
-- 名字
Name = "·多重·黑暗之箭"
-- 需求
Requires = ""
-- 物品技能
item = 1
-- 种族
race = ""
-- 目标允许
targs = "ground,enemies,air"

[A0AD]
_parent = "Aroc"
-- 影响区域
Area = 1000.0
-- 目标伤害
DataA = 0.0
-- 最大输出伤害
DataB = 0.0
-- 最大目标数
DataC = 1
-- 持续时间 - 普通
Dur = 0.0
-- 编辑器后缀
EditorSuffix = "哨兵"
-- 效果 - 射弹自导允许
MissileHoming = 0
-- 效果 - 射弹弧度
Missilearc = 0.0
-- 效果 - 投射物图像
Missileart = "Abilities\\Weapons\\SearingArrow\\SearingArrowMissile.mdl"
-- 效果 - 射弹速度
Missilespeed = 2400
-- 名字
Name = "·多重·黑暗之箭"
-- 需求
Requires = ""
-- 物品技能
item = 1
-- 种族
race = ""
-- 目标允许
targs = "ground,enemies,air"

[A0AE]
_parent = "Assp"
-- 单位类型
DataB = "n000"
-- 编辑器后缀
EditorSuffix = "大"
-- 名字
Name = "·怪物·小蜘蛛"
-- 种族
race = "human"

[A0AF]
_parent = "Aroa"
-- 魔法施放时间间隔
Cool = 10.0
-- 魔法消耗
Cost = 0
-- 攻击增加(%)
DataA = 1.0
-- 名字
Name = "·怪物·咆哮"
-- 种族
race = "human"

[A0AG]
_parent = "AOae"

[A0AH]
_parent = "Absk"
-- 魔法施放时间间隔
Cool = 18.0
-- 移动速度增加(%)
DataA = 0.0
-- 攻击速度增加(%)
DataB = 0.0
-- 所受伤害增加(%)
DataC = 0.0
-- 持续时间 - 普通
Dur = 3.0
-- 持续时间 - 英雄
HeroDur = 3.0
-- 热键 - 普通
Hotkey = "Q"
-- 名字
Name = "·怪物·无敌"
-- 提示工具 - 普通
Tip = "恩佐斯之赐"
-- 提示工具 - 普通 - 扩展
Ubertip = "提高攻击速度，持续10秒"
-- 种族
race = "human"

[A0AJ]
_parent = "AIlu"
-- 获得木材
DataA = 100
-- 名字
Name = "兑换木材"

[A0AK]
_parent = "AOae"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (727)tga.blp"
-- 魔法效果
BuffID = "B01V"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = 0.0
-- 攻击速度增加(%)
DataB = 0.0
-- 名字
Name = "·特效·黑剑"
-- 效果 - 目标
TargetArt = "war3mapImported\\aura[z2].mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 1
-- 种族
race = "unknown"
-- 目标允许
targs = "self"

[A0AN]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "黑骑士 武器"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapimported\\0625bladelightred.mdl"
-- 效果 - 目标附加点1
Targetattach = "weapon"

[A0AO]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "龙枪"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapimported\\windwalk fire.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0AP]
_parent = "AOae"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (611)tga.blp"
-- 魔法效果
BuffID = {
"B037",
"B037",
"B037",
"B037",
"B037",
}
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 攻击速度增加(%)
DataB = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 名字
Name = "·光环·碎心光环"
-- 效果 - 目标
TargetArt = "war3mapimported\\g5.mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 5
-- 种族
race = "unknown"

[A0AQ]
_parent = "AUau"
-- 影响区域
Area = 1500.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (292)tga.blp"
-- 移动速度增加(%)
DataA = 0.0
-- 生命回复增加
DataB = 0.0
-- 百分比奖励
DataC = 1
-- 名字
Name = "·怪物·古神之血·回血"
-- 效果 - 目标
TargetArt = "unholyaura.mdx"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "self"

[A0AS]
_parent = "AOre"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 魔法施放时间
Cast = 0.0
-- 魔法施放时间间隔
Cool = 3600.0
-- 重生延迟
DataA = 0.0
-- 名字
Name = "召唤物·腐化的心脏"
-- 英雄技能
hero = 0
-- 种族
race = "human"

[A0AT]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "深渊领主"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "poisonhands.mdl"
-- 效果 - 目标附加点1
Targetattach = "hand"

[A0AV]
_parent = "AIdv"
-- 魔法施放时间间隔
Cool = 0.0
-- 持续时间 - 普通
Dur = 1.0
-- 持续时间 - 英雄
HeroDur = 1.0
-- 名字
Name = "·物品神圣护甲"

[A0AY]
_parent = "AHad"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbc (39)tga.blp"
-- 魔法效果
BuffID = "B03L"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 防御奖励
DataA = -3.0
-- 名字
Name = "召唤物·穿刺之箭破甲"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "self"

[A0AZ]
_parent = "Asth"
-- 名字
Name = "召唤物·高级生物"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "高级生物"

[A0B0]
_parent = "Assp"
-- 单位类型
DataB = "n030"
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·水奴"
-- 种族
race = "human"

[A0B1]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (124)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 名字
Name = "召唤物·哨兵·射击"
-- 需求
Requires = ""
-- 图标 - 学习
ResearchArt = "replaceabletextures\\commandbuttons\\tbl (87)tga.blp"
-- 提示工具 - 普通
Tip = "通灵术"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动:|r攻击|Cffffff8c50%|r几率造成|Cffffff8c敏捷x1.6|r的伤害"
-- 图标 -关闭
Unart = "ReplaceableTextures\\CommandButtons\\BTNTheBlackArrow.blp"

[A0B4]
_parent = "Assp"
-- 单位类型
DataB = "n032"
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·小幽灵"
-- 种族
race = "human"

[A0B5]
_parent = "Assp"
-- 单位类型
DataB = "n033"
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·淤泥战士"
-- 种族
race = "human"

[A0B6]
_parent = "Assp"
-- 单位类型
DataB = "n035"
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·浴母蜘蛛"
-- 种族
race = "human"

[A0BG]
_parent = "ANde"
-- 伤害倍乘(建筑物)
DataB = 1.5
-- 伤害倍乘(英雄)
DataD = 1.5
-- 名字
Name = "召唤物·侵蚀"
-- 提示工具 - 普通
Tip = "侵蚀"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r对|Cffffff8cBOSS|r和建筑造成额外|Cffffff8c50%|r伤害"
-- 英雄技能
hero = 0
-- 种族
race = "human"

[A0BH]
_parent = "ACfn"
-- 魔法施放时间间隔
Cool = 15.0
-- 魔法消耗
Cost = 0
-- 范围目标伤害
DataA = 0.0
-- 特定目标伤害
DataB = 0.0
-- 持续时间 - 普通
Dur = 3.0
-- 编辑器后缀
EditorSuffix = ""
-- 效果 - 目标点
EffectArt = "Abilities\\Spells\\Undead\\Cripple\\CrippleTarget.mdl"
-- 持续时间 - 英雄
HeroDur = 3.0
-- 名字
Name = "·怪物·霜冻新星"
-- 种族
race = "human"

[A0BI]
_parent = "Apig"
-- 影响区域
Area = 2000.0
-- 魔法效果
BuffID = "B039"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 伤害/间隔时间
DataA = 1500.0
-- 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "·怪物·古神之血·群伤"
-- 种族
race = "human"

[A0BM]
_parent = "Absk"
-- 攻击速度增加(%)
DataB = 1.5
-- 所受伤害增加(%)
DataC = 0.0
-- 热键 - 普通
Hotkey = "Q"
-- 名字
Name = "召唤物·狂战士"
-- 提示工具 - 普通
Tip = "狂战士(|cffffcc00Q|r)"
-- 提示工具 - 普通 - 扩展
Ubertip = "提高攻击速度，持续10秒"
-- 种族
race = "human"

[A0BP]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·召唤物"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A0BU]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "技能 赤红"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\sweep_blood_small.mdl"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0BV]
_parent = "AHbh"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 重击几率
DataA = 100.0
-- 伤害奖励
DataC = 0.0
-- 持续时间 - 英雄
HeroDur = 2.0
-- 名字
Name = "召唤物·镇压"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "enemies,ground,air"

[A0BY]
_parent = "ANcl"
-- 效果 - 施法动作
Animnames = ""
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (175)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "animatedead"
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·诅咒"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9暗影冲击|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r每攻击|Cffffff8c8|r次释放暗影冲击造成|Cffffff8c敏捷x4|r的伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A0C0]
_parent = "ANcl"
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbc (39)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "ambush"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = "Q"
-- 名字
Name = "·专精·风行"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|CffA338EE穿刺之箭|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动:|r攻击有几率造成|Cffffff8c敏捷X2|r的伤害并破甲"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A0C1]
_parent = "ANcl"
-- 影响区域
Area = 400.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (695)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 20.0
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 动作持续时间
DataD = 0.0
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "auravampiric"
-- 编辑器后缀
EditorSuffix = "假"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·专精·通灵"
-- 施法距离
Rng = 1500.0
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff73FFB9哨兵|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC926被动：|r每|Cffffff8c10|r秒召唤黑暗弓箭手，造成|Cffffff8c敏捷X2|r的伤害，持续|Cffffff8c10|r秒|n|n|Cffffc926觉醒：|r每|Cffffff8c30|r秒召唤黑暗女武神，持续|Cffffff8c10|r秒。黑暗弓箭手持续时间翻倍"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A0C4]
_parent = "Aasl"
-- 影响区域
Area = 1350.0
-- 魔法效果
BuffID = "B00X"
-- 降低移动速度(%)
DataA = -0.7
-- 编辑器后缀
EditorSuffix = "70% 战吼"
-- 名字
Name = "J-减速光环"
-- 目标允许
targs = "enemies,ground,air"

[A0C9]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "瘟疫之箭"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\undeadmisslegreen.mdl"
-- 效果 - 射弹速度
Missilespeed = 1900
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A0CA]
_parent = "Apoi"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 每秒伤害
DataA = 0.0
-- 移动速度减少(%)
DataB = 0.2
-- 攻击速度减少(%)
DataC = 0.2
-- 持续时间 - 普通
Dur = 1.0
-- 持续时间 - 英雄
HeroDur = 1.0
-- 效果 - 射弹自导允许
MissileHoming = 0
-- 效果 - 投射物图像
Missileart = "war3mapImported\\az2_az_ta01_d2.mdl"
-- 效果 - 射弹速度
Missilespeed = 2800
-- 名字
Name = "召唤物·瘟疫之箭"
-- 种族
race = "human"

[A0CB]
_parent = "AHad"
-- 影响区域
Area = 1000.0
-- 魔法效果
BuffID = "B01A"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 防御奖励
DataA = -100.0
-- 名字
Name = "·怪物·亡灵意志"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "self"

[A0CC]
_parent = "Astn"
-- 魔法施放时间
Cast = 0.0
-- 普通形态单位
DataA = "n00P"
-- 高度调整时间
DataC = 0.0
-- 着陆延迟时间
DataD = 0.0
-- 每秒生命回复
DataE = 200.0
-- 名字
Name = "·怪物·石像鬼形态"
-- 需求
Requires = ""
-- 变化形态单位
UnitID = "u00F"
-- 种族
race = "human"

[A0CE]
_parent = "Asth"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·怪物·防吸血"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""

[A0CF]
_parent = "AIcb"
-- 攻击奖励
DataA = 100
-- 目标防御降低
DataB = 100
-- 持续时间 - 普通
Dur = 30.0
-- 持续时间 - 英雄
HeroDur = 30.0
-- 名字
Name = "·怪物·腐蚀之球"
-- 效果 -特殊
SpecialArt = ""
-- 效果 - 目标
TargetArt = ""
-- 效果 - 目标附加点1
Targetattach = ""
-- 物品技能
item = 0
-- 种族
race = "human"

[A0CG]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "恐惧魔王"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "az_dun01.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0CH]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "哀恸箭"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\az_ts_missile.mdl"
-- 效果 - 射弹速度
Missilespeed = 1900
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A0DT]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "奉献光环"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "divineseal.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0E3]
_parent = "AOae"
-- 影响区域
Area = {9999.0, 9999.0, 9999.0, 9999.0, 9999.0}
-- 魔法效果
BuffID = {
"B038",
"B038",
"B038",
"B038",
"B038",
}
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = {0.1, 0.1, 0.1, 0.1, 0.1}
-- 攻击速度增加(%)
DataB = {0.2, 0.4, 0.6, 0.8, 1.0}
-- 名字
Name = "·怪物·难度"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 等级
levels = 5
-- 种族
race = "human"

[A0EH]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "古神"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "miasma.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0EJ]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "海军上将"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\waterhands.mdx"
-- 效果 - 目标附加点1
Targetattach = "weapon"
-- 效果 - 目标附加点2
Targetattach1 = "hand"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0EY]
_parent = "AOw2"
-- 影响区域
Area = 800.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 效果 - 施法者
CasterArt = "war3mapImported\\xjcsmblbyq.mdl"
-- 魔法施放时间间隔
Cool = 2.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 0.0
-- 持续时间 - 普通
Dur = 4.0
-- 编辑器后缀
EditorSuffix = "3"
-- 持续时间 - 英雄
HeroDur = 4.0
-- 热键 - 普通
Hotkey = "R"
-- 名字
Name = "召唤物·死亡之主"
-- 提示工具 - 普通
Tip = "战争践踏(|Cffffcc00R|r)"
-- 提示工具 - 普通 - 扩展
Ubertip = "对周围敌人造成2000伤害并眩晕4秒"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"

[A0F1]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "光晕 小"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\ringofbright01.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0FH]
_parent = "AOae"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (184)tga.blp"
-- 魔法效果
BuffID = "B01S"
-- 移动速度增加(%)
DataA = 10.0
-- 攻击速度增加(%)
DataB = 1.0
-- 名字
Name = "召唤物·黑暗女武神"
-- 效果 - 目标
TargetArt = "war3mapimported\\g5.mdl"
-- 提示工具 - 普通
Tip = "战场女武神"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r提高所有友军|Cffffff8c100%|r攻击速度，并免疫减速效果"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "ground,nonhero,vulnerable,air,invulnerable,friend,self"

[A0FI]
_parent = "AOae"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNVampiricAura.blp"
-- 魔法效果
BuffID = {
"B01W",
"B01W",
"B01W",
"B01W",
"B01W",
}
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 攻击速度增加(%)
DataB = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 名字
Name = "·光环·吸血光环"
-- 效果 - 目标
TargetArt = "war3mapimported\\g5.mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 5
-- 种族
race = "unknown"

[A0FJ]
_parent = "AOae"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (136)tga.blp"
-- 魔法效果
BuffID = {
"B01T",
"B01T",
"B01T",
"B01T",
"B01T",
}
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 攻击速度增加(%)
DataB = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 名字
Name = "·光环·敏捷光环"
-- 效果 - 目标
TargetArt = "war3mapimported\\g5.mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 5
-- 种族
race = "unknown"

[A0FM]
_parent = "AOae"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (51)tga.blp"
-- 魔法效果
BuffID = {
"B01R",
"B01R",
"B01R",
"B01R",
"B01R",
}
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 攻击速度增加(%)
DataB = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 名字
Name = "·光环·智力光环"
-- 效果 - 目标
TargetArt = "war3mapimported\\g5.mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 5
-- 种族
race = "unknown"

[A0FU]
_parent = "AOae"
-- 影响区域
Area = 1000.0
-- 魔法效果
BuffID = "B03G"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = -0.5
-- 攻击速度增加(%)
DataB = -0.5
-- 名字
Name = "·特效·死寒之拥"
-- 效果 - 目标
TargetArt = "frost build.mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 1
-- 种族
race = "unknown"
-- 目标允许
targs = "enemies,ground,air"

[A0FV]
_parent = "AOae"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (614)tga.blp"
-- 魔法效果
BuffID = {
"B01U",
"B01U",
"B01U",
"B01U",
"B01U",
}
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 移动速度增加(%)
DataA = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 攻击速度增加(%)
DataB = {0.0, 0.0, 0.0, 0.0, 0.0}
-- 名字
Name = "·光环·力量光环"
-- 效果 - 目标
TargetArt = "war3mapimported\\g5.mdl"
-- 英雄技能
hero = 0
-- 物品技能
item = 1
-- 等级
levels = 5
-- 种族
race = "unknown"

[A0GD]
_parent = "AHad"
-- 影响区域
Area = 1000.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (179)tga.blp"
-- 魔法效果
BuffID = "B01Y"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 防御奖励
DataA = 100.0
-- 名字
Name = "召唤物·黑暗庇护"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "黑暗庇护"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r提高周围友军|Cffffff8c100|r护甲"
-- 英雄技能
hero = 0
-- 等级
levels = 1

[A0GW]
_parent = "Aneu"
-- 效果 - 施法者
CasterArt = ""
-- 激活范围
DataA = 9999999.0
-- 选择单位类型
DataB = 3
-- 编辑器后缀
EditorSuffix = "远"
-- 名字
Name = "W·选择英雄"
-- 施法距离
Rng = 600.0
-- 效果 - 目标
TargetArt = ""

[A0HC]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (221)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "berserk",
2 = "berserk",
3 = "berserk",
4 = "berserk",
5 = "berserk",
6 = "berserk",
7 = "berserk",
8 = "berserk",
9 = "berserk",
10 = "berserk",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：护甲"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE强化护甲lv1|r",
2 = "|CffA338EE强化护甲lv2|r",
3 = "|CffA338EE强化护甲lv3|r",
4 = "|CffA338EE强化护甲lv4|r",
5 = "|CffA338EE强化护甲lv5|r",
6 = "|CffA338EE强化护甲lv6|r",
7 = "|CffA338EE强化护甲lv7|r",
8 = "|CffA338EE强化护甲lv8|r",
9 = "|CffA338EE强化护甲lv9|r",
10 = "|CffA338EE强化护甲lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38护甲+15|r",
2 = "|Cff26FF38护甲+15|r",
3 = "|Cff26FF38护甲+15|r",
4 = "|Cff26FF38护甲+15|r",
5 = "|Cff26FF38护甲+15|r",
6 = "|Cff26FF38护甲+15|r",
7 = "|Cff26FF38护甲+15|r",
8 = "|Cff26FF38护甲+15|r",
9 = "|Cff26FF38护甲+15|r",
10 = "|Cff26FF38护甲+15|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0HE]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (8)tga.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "blizzard",
2 = "blizzard",
3 = "blizzard",
4 = "blizzard",
5 = "blizzard",
6 = "blizzard",
7 = "blizzard",
8 = "blizzard",
9 = "blizzard",
10 = "blizzard",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：射程"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE射程lv1|r",
2 = "|CffA338EE射程lv2|r",
3 = "|CffA338EE射程lv3|r",
4 = "|CffA338EE射程lv4|r",
5 = "|CffA338EE射程lv5|r",
6 = "|CffA338EE射程lv6|r",
7 = "|CffA338EE射程lv7|r",
8 = "|CffA338EE射程lv8|r",
9 = "|CffA338EE射程lv9|r",
10 = "|CffA338EE射程lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38射程+20|r",
2 = "|Cff26FF38射程+20|r",
3 = "|Cff26FF38射程+20|r",
4 = "|Cff26FF38射程+20|r",
5 = "|Cff26FF38射程+20|r",
6 = "|Cff26FF38射程+20|r",
7 = "|Cff26FF38射程+20|r",
8 = "|Cff26FF38射程+20|r",
9 = "|Cff26FF38射程+20|r",
10 = "|Cff26FF38射程+20|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0HR]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·关卡BOSS"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A0I8]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbc (1)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "barkskinon",
2 = "barkskinon",
3 = "barkskinon",
4 = "barkskinon",
5 = "barkskinon",
6 = "barkskinon",
7 = "barkskinon",
8 = "barkskinon",
9 = "barkskinon",
10 = "barkskinon",
}
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：技能冷却"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE技能冷却lv1|r",
2 = "|CffA338EE技能冷却lv2|r",
3 = "|CffA338EE技能冷却lv3|r",
4 = "|CffA338EE技能冷却lv4|r",
5 = "|CffA338EE技能冷却lv5|r",
6 = "|CffA338EE技能冷却lv6|r",
7 = "|CffA338EE技能冷却lv7|r",
8 = "|CffA338EE技能冷却lv8|r",
9 = "|CffA338EE技能冷却lv9|r",
10 = "|CffA338EE技能冷却lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
2 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
3 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
4 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
5 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
6 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
7 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
8 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
9 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
10 = "|Cff26FF38技能冷却+1.5%|n|n|r|Cff999999可减少“R”技能和拥有CD的天赋的冷却时间|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0I9]
_parent = "ANcl"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNVampiricAura.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "blight",
2 = "blight",
3 = "blight",
4 = "blight",
5 = "blight",
6 = "blight",
7 = "blight",
8 = "blight",
9 = "blight",
10 = "blight",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：吸血"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE吸血lv1|r",
2 = "|CffA338EE吸血lv2|r",
3 = "|CffA338EE吸血lv3|r",
4 = "|CffA338EE吸血lv4|r",
5 = "|CffA338EE吸血lv5|r",
6 = "|CffA338EE吸血lv6|r",
7 = "|CffA338EE吸血lv7|r",
8 = "|CffA338EE吸血lv8|r",
9 = "|CffA338EE吸血lv9|r",
10 = "|CffA338EE吸血lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38吸血+15|r",
2 = "|Cff26FF38吸血+15|r",
3 = "|Cff26FF38吸血+15|r",
4 = "|Cff26FF38吸血+15|r",
5 = "|Cff26FF38吸血+15|r",
6 = "|Cff26FF38吸血+15|r",
7 = "|Cff26FF38吸血+15|r",
8 = "|Cff26FF38吸血+15|r",
9 = "|Cff26FF38吸血+15|r",
10 = "|Cff26FF38吸血+15|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0IA]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (266)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "blackarrow",
2 = "blackarrow",
3 = "blackarrow",
4 = "blackarrow",
5 = "blackarrow",
6 = "blackarrow",
7 = "blackarrow",
8 = "blackarrow",
9 = "blackarrow",
10 = "blackarrow",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：闪现"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE闪现lv1|r",
2 = "|CffA338EE闪现lv2|r",
3 = "|CffA338EE闪现lv3|r",
4 = "|CffA338EE闪现lv4|r",
5 = "|CffA338EE闪现lv5|r",
6 = "|CffA338EE闪现lv6|r",
7 = "|CffA338EE闪现lv7|r",
8 = "|CffA338EE闪现lv8|r",
9 = "|CffA338EE闪现lv9|r",
10 = "|CffA338EE闪现lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38闪现距离+400|r",
2 = "|Cff26FF38闪现距离+400|r",
3 = "|Cff26FF38闪现距离+400|r",
4 = "|Cff26FF38闪现距离+400|r",
5 = "|Cff26FF38闪现距离+400|r",
6 = "|Cff26FF38闪现距离+400|r",
7 = "|Cff26FF38闪现距离+400|r",
8 = "|Cff26FF38闪现距离+400|r",
9 = "|Cff26FF38闪现距离+400|r",
10 = "|Cff26FF38闪现距离+400|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0IX]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 冰女"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Weapons\\ZigguratFrostMissile\\ZigguratFrostMissile.mdl"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0K6]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (651)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "blackarrowoff",
2 = "blackarrowoff",
3 = "blackarrowoff",
4 = "blackarrowoff",
5 = "blackarrowoff",
6 = "blackarrowoff",
7 = "blackarrowoff",
8 = "blackarrowoff",
9 = "blackarrowoff",
10 = "blackarrowoff",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：生命值"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE生命值lv1|r",
2 = "|CffA338EE生命值lv2|r",
3 = "|CffA338EE生命值lv3|r",
4 = "|CffA338EE生命值lv4|r",
5 = "|CffA338EE生命值lv5|r",
6 = "|CffA338EE生命值lv6|r",
7 = "|CffA338EE生命值lv7|r",
8 = "|CffA338EE生命值lv8|r",
9 = "|CffA338EE生命值lv9|r",
10 = "|CffA338EE生命值lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38生命值+600|r",
2 = "|Cff26FF38生命值+600|r",
3 = "|Cff26FF38生命值+600|r",
4 = "|Cff26FF38生命值+600|r",
5 = "|Cff26FF38生命值+600|r",
6 = "|Cff26FF38生命值+600|r",
7 = "|Cff26FF38生命值+600|r",
8 = "|Cff26FF38生命值+600|r",
9 = "|Cff26FF38生命值+600|r",
10 = "|Cff26FF38生命值+600|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0K7]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (662)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "battlestations",
2 = "battlestations",
3 = "battlestations",
4 = "battlestations",
5 = "battlestations",
6 = "battlestations",
7 = "battlestations",
8 = "battlestations",
9 = "battlestations",
10 = "battlestations",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：敏捷增幅"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE敏捷增幅lv1|r",
2 = "|CffA338EE敏捷增幅lv2|r",
3 = "|CffA338EE敏捷增幅lv3|r",
4 = "|CffA338EE敏捷增幅lv4|r",
5 = "|CffA338EE敏捷增幅lv5|r",
6 = "|CffA338EE敏捷增幅lv6|r",
7 = "|CffA338EE敏捷增幅lv7|r",
8 = "|CffA338EE敏捷增幅lv8|r",
9 = "|CffA338EE敏捷增幅lv9|r",
10 = "|CffA338EE敏捷增幅lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38敏捷+2%|r",
2 = "|Cff26FF38敏捷+2%|r",
3 = "|Cff26FF38敏捷+2%|r",
4 = "|Cff26FF38敏捷+2%|r",
5 = "|Cff26FF38敏捷+2%|r",
6 = "|Cff26FF38敏捷+2%|r",
7 = "|Cff26FF38敏捷+2%|r",
8 = "|Cff26FF38敏捷+2%|r",
9 = "|Cff26FF38敏捷+2%|r",
10 = "|Cff26FF38敏捷+2%|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0KG]
_parent = "ANcl"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNFeedBack.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "bearform",
2 = "bearform",
3 = "bearform",
4 = "bearform",
5 = "bearform",
6 = "bearform",
7 = "bearform",
8 = "bearform",
9 = "bearform",
10 = "bearform",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：会心几率"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE会心几率lv1|r",
2 = "|CffA338EE会心几率lv2|r",
3 = "|CffA338EE会心几率lv3|r",
4 = "|CffA338EE会心几率lv4|r",
5 = "|CffA338EE会心几率lv5|r",
6 = "|CffA338EE会心几率lv6|r",
7 = "|CffA338EE会心几率lv7|r",
8 = "|CffA338EE会心几率lv8|r",
9 = "|CffA338EE会心几率lv9|r",
10 = "|CffA338EE会心几率lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
2 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
3 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
4 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
5 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
6 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
7 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
8 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
9 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
10 = "|Cff26FF38暴击和致命一击几率+1.5%|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0KH]
_parent = "AOae"
-- 影响区域
Area = 2000.0
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNFreezingBreath.blp"
-- 魔法效果
BuffID = "B027"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 移动速度增加(%)
DataA = -0.2
-- 攻击速度增加(%)
DataB = -0.2
-- 名字
Name = "召唤物·冰霜光环"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "|Cff4DA6FF冰霜骨龙·刺骨|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r降低周围敌人|Cffffff8c20%|r攻击和移动速度"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "ground,enemies,air"

[A0KM]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "光晕 小小"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Spells\\Other\\Tornado\\Tornado_Target.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0KW]
_parent = "AOae"
-- 影响区域
Area = 2000.0
-- 魔法效果
BuffID = "B012"
-- 移动速度增加(%)
DataA = -0.4
-- 攻击速度增加(%)
DataB = -0.4
-- 名字
Name = "·怪物·不死瘟疫"
-- 效果 - 目标
TargetArt = "whine.mdx"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A0L0]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 绿色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firebrand shot green.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0L1]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 银色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firebrand shot silver.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0L2]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "龙卷风"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Spells\\Other\\Tornado\\Tornado_Target.mdl"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0L3]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 橙色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firebrand shot orange.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 1

[A0L4]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 粉色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firebrand shot pink.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0L5]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "黑暗游侠 P1"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapimported\\firebrand shot purple.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0L6]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 红色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firebrand shot red.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 1

[A0L7]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 青色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firebrand shot teal.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0L8]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 黄色"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\firebrand shot yellow.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0L9]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·渣渣怪"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A0LB]
_parent = "Assk"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 最小伤害
DataB = 1.0
-- 忽略伤害
DataC = 9999999.0
-- 编辑器后缀
EditorSuffix = "100% 999999999"
-- 名字
Name = "W·皮肤"
-- 需求
Requires = ""
-- 种族
race = "unknown"
-- 目标允许
targs = "enemies,nonhero,allies"

[A0LS]
_parent = "Assk"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 减少伤害几率 (%)
DataA = 12.0
-- 最小伤害
DataB = 1.0
-- 忽略伤害
DataC = 10000000.0
-- 名字
Name = "·怪物·硬化皮肤"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "1"
-- 种族
race = "human"

[A0M3]
_parent = "ACbf"
-- 影响区域
Area = 200.0
-- 魔法效果
BuffID = "B028"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 0.0
-- 距离
DataC = 1000.0
-- 最终区域范围
DataD = 200.0
-- 每秒伤害
DataE = 1.0
-- 持续时间 - 普通
Dur = 1.0
-- 持续时间 - 英雄
HeroDur = 1.0
-- 效果 - 投射物图像
Missileart = "whine.mdx"
-- 效果 - 射弹速度
Missilespeed = 2900
-- 名字
Name = "·马甲·女妖哀嚎"
-- 种族
race = "human"
-- 目标允许
targs = "ground,enemies,air"

[A0M7]
_parent = "AHad"

[A0M8]
_parent = "Aslo"
-- 效果 - 施法动作
Animnames = "attack"
-- 效果 - 施法者
CasterArt = ""
-- 魔法施放时间间隔
Cool = 10.0
-- 魔法消耗
Cost = 0
-- 降低移动速度(%)
DataA = 0.5
-- 降低攻击速度(%)
DataB = 0.0
-- 总是自动施放
DataC = 1
-- 持续时间 - 普通
Dur = 2.0
-- 持续时间 - 英雄
HeroDur = 2.0
-- 名字
Name = "·怪物·减速"
-- 施法距离
Rng = 800.0

[A0M9]
_parent = "AHad"
-- 影响区域
Area = 2000.0
-- 魔法效果
BuffID = "B02B"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 防御奖励
DataA = -100.0
-- 名字
Name = "·怪物·古神之血·破甲"
-- 效果 - 目标
TargetArt = ""
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 目标允许
targs = "enemies,ground,air,hero,organic"

[A0MA]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (518)tga.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 1
-- 名字
Name = "·怪物·自爆"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "雷霆一击[|Cffffc92clv1|r]"
-- 提示工具 - 普通 - 扩展
Ubertip = ""

[A0MC]
_parent = "Aegr"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNGenericSpellImmunity.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 所受穿刺伤害(%)
DataA = 1.0
-- 所受魔法伤害(%)
DataE = 0.15
-- 编辑器后缀
EditorSuffix = "魔抗70%"
-- 名字
Name = "召唤物·远古单位"
-- 提示工具 - 普通
Tip = "|CffFF9326远古单位|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r这是一个BOSS级别召唤物"
-- 种族
race = "human"

[A0MD]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\BTNBreathOfFrost.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 名字
Name = "召唤物·冰霜吐息"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "|Cff4DA6FF冰霜骨龙·吐息|r"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r攻击有几率造成|Cffffff8c敏捷x【3+技能等级x0.1】|r的范围伤害"

[A0MQ]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·普通怪"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = "毒性射击"

[A0MY]
_parent = "Asth"
-- 图标 - 普通
Art = ""
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "召唤物·蝗虫"
-- 需求
Requires = ""
-- 提示工具 - 普通
Tip = ""
-- 提示工具 - 普通 - 扩展
Ubertip = ""

[A0MZ]
_parent = "Aasl"
-- 影响区域
Area = 1000.0
-- 降低移动速度(%)
DataA = -0.9
-- 编辑器后缀
EditorSuffix = "90%"
-- 名字
Name = "J-减速光环"

[A0N1]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "剑"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\jianzhu4.mdx"
-- 效果 - 目标附加点1
Targetattach = "chest"

[A0N8]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "阿木木"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\grandnightelfaura.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0ND]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "势不可挡"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\holystrike.mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0NE]
_parent = "AOre"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (74)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 魔法施放时间
Cast = 0.0
-- 魔法施放时间间隔
Cool = 0.0
-- 重生延迟
DataA = 0.0
-- 效果 - 目标点
EffectArt = ""
-- 名字
Name = "功能·重生"
-- 英雄技能
hero = 0
-- 种族
race = "unknown"

[A0NI]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "手光 火女"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Weapons\\RedDragonBreath\\RedDragonMissile.mdl"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标 - 附加数量
Targetattachcount = 2

[A0NT]
_parent = "AOae"
-- 影响区域
Area = 999999.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (142)tga.blp"
-- 魔法效果
BuffID = "B03B"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 移动速度增加(%)
DataA = 0.0
-- 攻击速度增加(%)
DataB = -0.2
-- 名字
Name = "召唤物·憎恶"
-- 效果 - 目标
TargetArt = "war3mapImported\\DiseaseCloud+.mdl"
-- 提示工具 - 普通
Tip = "瘟疫光环"
-- 提示工具 - 普通 - 扩展
Ubertip = "每|Cffffff8c3|r秒造成|Cffffff8c敏捷X1|r的范围伤害并|Cffff9326嘲讽|r敌人"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "ground,enemies,air"

[A0O1]
_parent = "Asth"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\ac1tgatga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 名字
Name = "召唤物·碾压免疫"
-- 需求
Requires = ""
-- 效果 -特殊
SpecialArt = "darkelfaura.mdx"
-- 提示工具 - 普通
Tip = "灵魂兽"
-- 提示工具 - 普通 - 扩展
Ubertip = "免疫|Cffffff8cBOSS|r的碾压伤害"

[A0PP]
_parent = "Aasl"
-- 影响区域
Area = 2000.0
-- 降低攻击速度(%)
DataB = -0.3
-- 编辑器后缀
EditorSuffix = "冰霜死亡骑士"
-- 名字
Name = "J-减速光环"

[A0PX]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "恩佐斯"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapimported\\aura[z2].mdl"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0Q2]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "黑暗游侠 P2"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "Abilities\\Weapons\\AvengerMissile\\AvengerMissile.mdl"
-- 效果 - 目标附加点1
Targetattach = "hand,left"
-- 效果 - 目标附加点2
Targetattach1 = "hand,right"
-- 效果 - 目标附加点3
Targetattach2 = "chest"
-- 效果 - 目标 - 附加数量
Targetattachcount = 3

[A0QC]
_parent = "AOae"
-- 影响区域
Area = 600.0
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (174)tga.blp"
-- 魔法效果
BuffID = "Bplg"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 移动速度增加(%)
DataA = 0.0
-- 攻击速度增加(%)
DataB = -0.2
-- 编辑器后缀
EditorSuffix = "1"
-- 名字
Name = "召唤物·死亡之主"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "死灵光环"
-- 提示工具 - 普通 - 扩展
Ubertip = "|CffFFC92C被动：|r降低周围敌人|Cffffff8c20%|r攻击速度，每秒造成|Cffffff8c全属性x1|r的范围物理伤害"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "human"
-- 目标允许
targs = "enemies,ground,air"

[A0RH]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "光晕"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\RingOfBright.mdx"
-- 效果 - 目标附加点1
Targetattach = "origin"

[A0RI]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 0
-- 名字
Name = "·单位·建筑"
-- 需求
Requires = ""
-- 效果 - 目标
TargetArt = "az_gjsxh1_r.mdx"
-- 提示工具 - 普通
Tip = "随从单位"

[A0SC]
_parent = "ACbf"
-- 影响区域
Area = 1000.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 5000.0
-- 最大伤害
DataB = 1000000000.0
-- 距离
DataC = 1000.0
-- 最终区域范围
DataD = 300.0
-- 每秒伤害
DataE = 0.0
-- 效果 - 投射物图像
Missileart = "Abilities\\Spells\\Undead\\CarrionSwarm\\CarrionSwarmMissile.mdl"
-- 效果 - 射弹速度
Missilespeed = 1100
-- 名字
Name = "·怪物·腐臭蜂群"
-- 施法距离
Rng = 600.0
-- 种族
race = "human"

[A0SP]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "死灵攻击"
-- 效果 - 投射物图像
Missileart = "necromagusmissile.mdl"
-- 效果 - 射弹速度
Missilespeed = 1800
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A0SQ]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "狂战士"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\CorpseBomb.mdx"
-- 效果 - 射弹速度
Missilespeed = 1600
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A0SV]
_parent = "Asth"
-- 图标 - 普通
Art = "ReplaceableTextures\\CommandButtons\\PASBTNEngineeringUpgrade.blp"
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 名字
Name = "·单位·虚空怪"
-- 需求
Requires = ""
-- 效果 - 目标
TargetArt = "war3mapImported\\twilightincineratespecial.mdx"
-- 提示工具 - 普通
Tip = "毒性射击"

[A0T1]
_parent = "ACce"
-- 影响区域
Area = 0.0
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = -11
-- 分裂伤害参数
DataA = 0.0
-- 编辑器后缀
EditorSuffix = "V60"
-- 效果 - 投射物图像
Missileart = "war3mapImported\\SFMR2.mdl"
-- 效果 - 射弹速度
Missilespeed = 1600
-- 名字
Name = "T-改变投射物"
-- 效果 -特殊
SpecialArt = ""
-- 效果 -特殊附加点
Specialattach = ""
-- 提示工具 - 普通
Tip = "T-改变投射物"
-- 种族
race = "orc"
-- 目标允许
targs = "enemies,ground,air"

[A0T3]
_parent = "Apiv"
-- 名字
Name = "W·永久的隐形"

[A0TX]
_parent = "AItg"
-- 攻击奖励
DataA = 0
-- 编辑器后缀
EditorSuffix = "弓 紫"
-- 名字
Name = "T-特效"
-- 效果 - 目标
TargetArt = "war3mapImported\\Soul Bow Enchantment black.mdx"
-- 效果 - 目标附加点1
Targetattach = "hand,left"

[A0UB]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (137)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 1
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "battleroar",
2 = "battleroar",
3 = "battleroar",
4 = "battleroar",
5 = "battleroar",
6 = "battleroar",
7 = "battleroar",
8 = "battleroar",
9 = "battleroar",
10 = "battleroar",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：回血"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE回血lv1|r",
2 = "|CffA338EE回血lv2|r",
3 = "|CffA338EE回血lv3|r",
4 = "|CffA338EE回血lv4|r",
5 = "|CffA338EE回血lv5|r",
6 = "|CffA338EE回血lv6|r",
7 = "|CffA338EE回血lv7|r",
8 = "|CffA338EE回血lv8|r",
9 = "|CffA338EE回血lv9|r",
10 = "|CffA338EE回血lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff24F235回血+30xlv|r",
2 = "|Cff24F235回血+30xlv|r",
3 = "|Cff24F235回血+30xlv|r",
4 = "|Cff24F235回血+30xlv|r",
5 = "|Cff24F235回血+30xlv|r",
6 = "|Cff24F235回血+30xlv|r",
7 = "|Cff24F235回血+30xlv|r",
8 = "|Cff24F235回血+30xlv|r",
9 = "|Cff24F235回血+30xlv|r",
10 = "|Cff24F235回血+30xlv|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0UC]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (647)tga.blp"
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "barkskinoff",
2 = "barkskinoff",
3 = "barkskinoff",
4 = "barkskinoff",
5 = "barkskinoff",
6 = "barkskinoff",
7 = "barkskinoff",
8 = "barkskinoff",
9 = "barkskinoff",
10 = "barkskinoff",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：攻击速度"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE攻击速度lv1|r",
2 = "|CffA338EE攻击速度lv2|r",
3 = "|CffA338EE攻击速度lv3|r",
4 = "|CffA338EE攻击速度lv4|r",
5 = "|CffA338EE攻击速度lv5|r",
6 = "|CffA338EE攻击速度lv6|r",
7 = "|CffA338EE攻击速度lv7|r",
8 = "|CffA338EE攻击速度lv8|r",
9 = "|CffA338EE攻击速度lv9|r",
10 = "|CffA338EE攻击速度lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
2 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
3 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
4 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
5 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
6 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
7 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
8 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
9 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
10 = "|Cff26FF38攻击速度+10%|n|n|r|Cff999999攻速上限500%|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0UD]
_parent = "ANcl"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = 0.0
-- 选项
DataC = 1
-- 使其他技能无效
DataE = 0
-- 基础命令ID
DataF = "absorb"
-- 效果 - 目标点
EffectArt = ""
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "·皮肤·影魔"
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = "选择：影魔"
-- 提示工具 - 普通 - 扩展
Ubertip = "攻击间隔-5%|n|n女妖形态变为死亡之女|n每3秒对周围造成敏捷x3的伤害并减少敌人50%攻击和移动速度"
-- 英雄技能
hero = 0
-- 等级
levels = 1
-- 种族
race = "undead"

[A0UE]
_parent = "ANcl"
-- 图标 - 普通
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNTrueShot.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 3
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "bloodlustoff",
2 = "bloodlustoff",
3 = "bloodlustoff",
4 = "bloodlustoff",
5 = "bloodlustoff",
6 = "bloodlustoff",
7 = "bloodlustoff",
8 = "bloodlustoff",
9 = "bloodlustoff",
10 = "bloodlustoff",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：攻击间隔"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE攻击间隔lv1|r",
2 = "|CffA338EE攻击间隔lv2|r",
3 = "|CffA338EE攻击间隔lv3|r",
4 = "|CffA338EE攻击间隔lv4|r",
5 = "|CffA338EE攻击间隔lv5|r",
6 = "|CffA338EE攻击间隔lv6|r",
7 = "|CffA338EE攻击间隔lv7|r",
8 = "|CffA338EE攻击间隔lv8|r",
9 = "|CffA338EE攻击间隔lv9|r",
10 = "|CffA338EE攻击间隔lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
2 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
3 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
4 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
5 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
6 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
7 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
8 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
9 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
10 = "|Cff26FF38降低2%攻击间隔|n|n|r|Cff999999攻击间隔上限0.25|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0UF]
_parent = "ANcl"
-- 图标 - 普通
Art = "replaceabletextures\\commandbuttons\\tbl (697)tga.blp"
-- 按钮位置 - 普通 (X)
Buttonpos_1 = 0
-- 按钮位置 - 普通 (Y)
Buttonpos_2 = 2
-- 效果 - 施法者
CasterArt = ""
-- 施法持续时间
DataA = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 选项
DataC = {
1 = 1,
2 = 1,
3 = 1,
4 = 1,
5 = 1,
6 = 1,
7 = 1,
8 = 1,
9 = 1,
10 = 1,
}
-- 动作持续时间
DataD = {
1 = 0.0,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 使其他技能无效
DataE = {
1 = 0,
2 = 0,
3 = 0,
4 = 0,
5 = 0,
6 = 0,
7 = 0,
8 = 0,
9 = 0,
10 = 0,
}
-- 基础命令ID
DataF = {
1 = "bloodlust",
2 = "bloodlust",
3 = "bloodlust",
4 = "bloodlust",
5 = "bloodlust",
6 = "bloodlust",
7 = "bloodlust",
8 = "bloodlust",
9 = "bloodlust",
10 = "bloodlust",
}
-- 持续时间 - 普通
Dur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 效果 - 目标点
EffectArt = ""
-- 持续时间 - 英雄
HeroDur = {
1 = 0.001,
2 = 0.0,
3 = 0.0,
4 = 0.0,
5 = 0.0,
6 = 0.0,
7 = 0.0,
8 = 0.0,
9 = 0.0,
10 = 0.0,
}
-- 热键 - 普通
Hotkey = ""
-- 名字
Name = "科技：攻击增幅"
-- 施法距离
Rng = {
1 = 1000.0,
2 = 500.0,
3 = 500.0,
4 = 500.0,
5 = 500.0,
6 = 500.0,
7 = 500.0,
8 = 500.0,
9 = 500.0,
10 = 500.0,
}
-- 效果 - 目标
TargetArt = ""
-- 提示工具 - 普通
Tip = {
1 = "|CffA338EE攻击增幅lv1|r",
2 = "|CffA338EE攻击增幅lv2|r",
3 = "|CffA338EE攻击增幅lv3|r",
4 = "|CffA338EE攻击增幅lv4|r",
5 = "|CffA338EE攻击增幅lv5|r",
6 = "|CffA338EE攻击增幅lv6|r",
7 = "|CffA338EE攻击增幅lv7|r",
8 = "|CffA338EE攻击增幅lv8|r",
9 = "|CffA338EE攻击增幅lv9|r",
10 = "|CffA338EE攻击增幅lv10|r",
}
-- 提示工具 - 普通 - 扩展
Ubertip = {
1 = "|Cff26FF38攻击力+200|r",
2 = "|Cff26FF38攻击力+200|r",
3 = "|Cff26FF38攻击力+200|r",
4 = "|Cff26FF38攻击力+200|r",
5 = "|Cff26FF38攻击力+200|r",
6 = "|Cff26FF38攻击力+200|r",
7 = "|Cff26FF38攻击力+200|r",
8 = "|Cff26FF38攻击力+200|r",
9 = "|Cff26FF38攻击力+200|r",
10 = "|Cff26FF38攻击力+200|r",
}
-- 英雄技能
hero = 0
-- 等级
levels = 10
-- 种族
race = "human"

[A0UR]
_parent = "ACbf"
-- 魔法效果
BuffID = "B028"
-- 魔法施放时间间隔
Cool = 0.0
-- 魔法消耗
Cost = 0
-- 伤害
DataA = 0.0
-- 距离
DataC = 2000.0
-- 每秒伤害
DataE = 1.0
-- 持续时间 - 普通
Dur = 1.0
-- 持续时间 - 英雄
HeroDur = 1.0
-- 效果 - 投射物图像
Missileart = "Objects\\Spawnmodels\\Undead\\UndeadDissipate\\UndeadDissipate.mdl"
-- 效果 - 射弹速度
Missilespeed = 800
-- 名字
Name = "·怪物·惊骇"
-- 种族
race = "human"
