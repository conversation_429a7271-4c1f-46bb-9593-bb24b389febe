// banner.fdf

// 方形头像背景模板
Frame "BACKDROP" "SquarePortraitBackdropTemplate" {
    // 使用一个通用的按钮背景作为基础，你可以替换成更合适的纹理
    ControlBackdrop "EscMenuButtonImage",

    // 确保混合模式正常工作
    BackdropBlendAll,

    // 不在这里设置 Width, Height, SetAllPoints 或 Anchor，由 JASS 控制
}

// 主背景条模板
Frame "BACKDROP" "BannerBackdropTemplate" {
    // 使用一个通用的背景，你可以替换
    ControlBackdrop "EscMenuButtonImage",
    BackdropBlendAll,

    // 不在这里设置 Width, Height, SetAllPoints 或 Anchor，由 JASS 控制
}

// 文本模板
Frame "TEXT" "BannerTextTemplate" {
    // 设置默认字体，JASS 会覆盖大小
    FrameFont "Fonts\\FRIZQT__.TTF", 0.01, "", // 使用标准的 Friz Quadrata 字体

    // 默认字体颜色 (白色)
    FontColor 1.0 1.0 1.0 1.0,

    // 默认对齐方式 (JASS 会覆盖为居中)
    FontJustificationH JUSTIFYLEFT,
    FontJustificationV JUSTIFYMIDDLE,

    // 不在这里设置 Width, Height, SetAllPoints 或 Anchor，由 JASS 控制
}