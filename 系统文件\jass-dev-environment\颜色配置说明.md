# JASS颜色配置说明

## 🎨 颜色方案

### 正常代码 - 蓝色系
- **基础代码**: `#4A90E2` (蓝色)
- **关键字**: `#5BA7F7` (亮蓝色，加粗)
- **函数名**: `#6BB6FF` (浅蓝色)
- **类型**: `#4A90E2` (蓝色，斜体)
- **字符串/数字**: `#87CEEB` (天蓝色)
- **注释**: `#6A9955` (绿色，斜体)
- **API函数**: `#9CDCFE` (青蓝色，加粗)

### 错误标记 - 红色
- **错误**: `#FF0000` (纯红色)
- **警告**: `#FFA500` (橙色)
- **信息**: `#00BFFF` (深天蓝色)

## 🔧 如何应用配置

1. **自动应用**: 打开JASS文件时会自动使用蓝色主题
2. **手动调整**: 可以在VS Code设置中修改 `editor.tokenColorCustomizations`
3. **重置**: 删除 `.vscode/settings.json` 文件可恢复默认

## 🎯 验证效果

打开 `test-validation.j` 文件，你应该看到：

✅ **正确的代码**: 显示为各种蓝色
❌ **错误的代码**: 有红色波浪线标记
⚠️ **警告**: 有橙色波浪线标记

## 📝 自定义颜色

如果想调整颜色，修改 `.vscode/settings.json` 中的颜色值：

```json
{
  "editor.tokenColorCustomizations": {
    "textMateRules": [
      {
        "scope": "source.jass",
        "settings": {
          "foreground": "#你的颜色代码"
        }
      }
    ]
  }
}
```

## 🚀 AI代码验证流程

1. **AI生成代码** → 粘贴到VS Code
2. **实时检查** → 看是否有红色错误标记
3. **修正错误** → 直到没有红色标记
4. **复制到编辑器** → 确保能正常运行

这样就能确保AI生成的代码在地图编辑器中也能正常工作！
