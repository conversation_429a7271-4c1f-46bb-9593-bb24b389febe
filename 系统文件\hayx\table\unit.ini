[hmtm]
_parent = "hmtm"
-- 允许攻击模式
weapsOn = 1

[hspt]
_parent = "hspt"
-- 模型文件
file = "blood ritual.mdl"

[nbda]
_parent = "nbda"
-- 颜色值(蓝)
blue = 255
-- 颜色值(红)
red = 255

[nskm]
_parent = "nskm"
-- 颜色值(蓝)
blue = 0
-- 模型文件
file = "Units\\Creeps\\BurningArcher\\BurningArcher.mdl"
-- 颜色值(绿)
green = 0
-- 颜色值(红)
red = 0

[H001]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "3"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "orbofcorruption.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2500
-- 名字
Name = "虚空领主"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 普通
abilList = "A00E,A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "voidwalker_outland.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 3.0
-- 类型
movetp = "hover"
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 700
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 275.0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 动画 - 行走速度
walk = 275.0
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H002]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "女妖"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "az_greendragonpf_missile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2800
-- 名字
Name = "女妖形态"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 普通
abilList = "A01W,A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.75
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapImported\\banshee.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 高度
moveHeight = 10.0
-- 类型
movetp = "hover"
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 700
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H003]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\GreenDragonMissile\\GreenDragonMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "影魔"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 普通
abilList = "A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.75
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "Units\\Creeps\\HeroFlameLord\\HeroFlameLord.mdl"
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 850
-- 颜色值(红)
red = 0
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 单位声音设置
unitSound = "HeroFireLord"
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H004]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "4"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "orbofcorruption.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2500
-- 名字
Name = "光明游侠"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 普通
abilList = "A00E,A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 类型
movetp = "hover"
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 700
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 275.0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 动画 - 行走速度
walk = 275.0
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H005]
_parent = "hsor"
-- 名字
Name = "希尔瓦娜斯"
-- 模型文件
file = "war3mapImported\\valeera.mdl"
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H006]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "5"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "orbofcorruption.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2500
-- 名字
Name = "萨菲隆"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 普通
abilList = "A00E,A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapImported\\ok1.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 高度
moveHeight = 100.0
-- 类型
movetp = "hover"
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 700
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 275.0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 动画 - 行走速度
walk = 275.0
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H007]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "6"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "orbofcorruption.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2500
-- 名字
Name = "萨菲隆"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 普通
abilList = "A00E,A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapImported\\necromancerstave.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 类型
movetp = "hover"
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 700
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 275.0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 动画 - 行走速度
walk = 275.0
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H008]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\firebrand shot silver.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "光明游侠"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 普通
abilList = "A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.75
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "[hero]_3.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 0.6
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 850
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 单位声音设置
unitSound = "HeroFireLord"
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H009]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 5
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 5
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\spiritarrow_byepsilon.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "银月游侠"
-- 称谓
Propernames = "游侠新兵"
-- 英雄 - 初始力量
STR = 5
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 普通
abilList = "A06X,A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.2
-- 动画 - 魔法施放点
castpt = 0.0
-- 碰撞体积
collision = 16.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "[hero]_1.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 450
-- 生命回复
regenHP = 0.0
-- 魔法回复
regenMana = 20.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 210.0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 动画 - 行走速度
walk = 210.0
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H00A]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "[jn]_d33.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "凤凰射手"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 普通
abilList = "A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.75
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "zyl_geerd1.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 0.9
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 850
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 单位声音设置
unitSound = "HeroFireLord"
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H00B]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "2"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "az_greendragonpf_missile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1900
-- 名字
Name = "女武神形态"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNBanshee.blp"
-- 普通
abilList = "A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.75
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapimported\\valkyrblack_squished.mdl"
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 180
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 高度
moveHeight = 200.0
-- 类型
movetp = "hover"
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 700
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 275.0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 动画 - 行走速度
walk = 275.0
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H00C]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Spells\\Other\\BlackArrow\\BlackArrowMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "骸骨射手"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 普通
abilList = "A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.75
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapImported\\deadeye.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 850
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 单位声音设置
unitSound = "HeroFireLord"
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H00D]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 55
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 55
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\GreenDragonMissile\\GreenDragonMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "血精灵刺客"
-- 称谓
Propernames = "黑暗游侠"
-- 英雄 - 初始力量
STR = 55
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 普通
abilList = "A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.75
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapImported\\valeera.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 1.0
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 850
-- 生命回复
regenHP = 30.0
-- 魔法回复
regenMana = 10.0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 单位声音设置
unitSound = "HeroFireLord"
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[H00R]
_parent = "hgra"
-- 英雄 - 初始敏捷
AGI = 9999
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNBansheeRanger.blp"
-- 说明
Description = "|CffA338EE女妖之王|r"
-- 生命最大值
HP = 5000000
-- 英雄 - 初始智力
INT = 9999
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1900
-- 名字
Name = "希尔瓦娜斯·风行者"
-- 英雄 - 主要属性
Primary = "AGI"
-- 称谓
Propernames = "|CffA338EE女妖之王|r"
-- 需求
Requires = ""
-- 可研究项目
Researches = ""
-- 英雄 - 初始力量
STR = 9999
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNBansheeRanger.blp"
-- 训练单位
Trains = ""
-- 普通
abilList = "A0KM,Avul,AInv"
-- 攻击 1 - 攻击类型
atkType1 = "chaos"
-- 阴影图像(建筑)
buildingShadow = ""
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 护甲类型
defType = "hero"
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 500000
-- 模型文件
file = "war3mapImported\\BansheeRanger.mdl"
-- 英雄 - 隐藏英雄死亡信息
hideHeroDeathMsg = 1
-- 英雄 - 隐藏小地图英雄显示
hideHeroMinimap = 1
-- 是一个建筑
isbldg = 0
-- 魔法初始数量
mana0 = 200
-- 魔法最大值
manaN = 200
-- 模型缩放
modelScale = 1.5
-- 中立建筑 - 显示小地图标记
nbmmIcon = 1
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 攻击 1 - 攻击范围
rangeN1 = 900
-- 生命回复
regenHP = 1000.0
-- 魔法回复
regenMana = 200.0
-- 生命回复类型
regenType = "always"
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 150.0
-- 阴影图像 - 宽度
shadowW = 150.0
-- 阴影图像 - X轴偏移
shadowX = 75.0
-- 阴影图像 - Y轴偏移
shadowY = 75.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 单位类别
type = ""
-- 建筑地面纹理
uberSplat = ""
-- 阴影图像(单位)
unitShadow = "Shadow"
-- 单位声音设置
unitSound = "DarkRanger"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[H015]
_parent = "Hvwd"
-- 英雄 - 初始敏捷
AGI = 5
-- 英雄 - 每等级提升敏捷
AGIplus = 0.0
-- 图标 - 游戏界面
Art = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 生命最大值
HP = 3000
-- 英雄 - 初始智力
INT = 5
-- 英雄 - 每等级提升智力
INTplus = 0.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Spells\\Other\\BlackArrow\\BlackArrowMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "黑暗游侠"
-- 称谓
Propernames = "游侠新兵"
-- 英雄 - 初始力量
STR = 5
-- 英雄 - 每等级提升力量
STRplus = 0.0
-- 图标 - 计分屏
ScoreScreenIcon = "replaceabletextures\\commandbuttons\\ring (4).blp"
-- 普通
abilList = "A06X,A03B,AInv"
-- 主动攻击范围
acquire = 2000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.2
-- 动画 - 魔法施放点
castpt = 0.0
-- 碰撞体积
collision = 16.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 10.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 10
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapImported\\UDArcher.mdl"
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法最大值
manaN = 100
-- 视野范围(夜晚)
nsight = 1800
-- 单位附加值
points = 1
-- 攻击 1 - 攻击范围
rangeN1 = 450
-- 生命回复
regenHP = 0.0
-- 魔法回复
regenMana = 20.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 210.0
-- 选择缩放
scale = 1.25
-- 缩放投射物
scaleBull = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 基础速度
spd = 522
-- 攻击 1 - 目标允许
targs1 = "debris,enemies,ground,structure,air,item"
-- 队伍颜色
teamColor = 0
-- 转身速度
turnRate = 1.5
-- 动画 - 行走速度
walk = 210.0
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[N01W]
_parent = "ngnw"
-- 英雄 - 初始敏捷
AGI = 1000
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNTichondrius.blp"
-- 生命最大值
HP = 100000000
-- 英雄 - 初始智力
INT = 1000
-- 攻击 1 - 投射物图像
Missileart_1 = ""
-- 攻击 1 - 射弹速率
Missilespeed_1 = 0
-- 名字
Name = "玛尔加尼斯"
-- 英雄 - 主要属性
Primary = "AGI"
-- 称谓
Propernames = "|CffFF0000“血色十字军领袖”|r"
-- 英雄 - 初始力量
STR = 1000
-- 普通
abilList = "A0AH,A02A,A0AQ,A0M9,A0BH,A01R"
-- 主动攻击范围
acquire = 20000.0
-- 攻击 1 - 攻击类型
atkType1 = "chaos"
-- 攻击 1 - 动画回复点
backSw1 = 0.5
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 动画 - 魔法施放回复
castbsw = 0.2
-- 动画 - 魔法施放点
castpt = 0.2
-- 碰撞体积
collision = 1.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.1
-- 基础护甲
def = 200.0
-- 护甲类型
defType = "hero"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.5
-- 模型文件
file = "dreadlord.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 最小速度
minSpd = 350
-- 模型缩放
modelScale = 1.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 175
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 400.0
-- 选择缩放
scale = 5.0
-- 阴影图像 - 高度
shadowH = 500.0
-- 阴影图像 - 宽度
shadowW = 500.0
-- 阴影图像 - X轴偏移
shadowX = 250.0
-- 阴影图像 - Y轴偏移
shadowY = 250.0
-- 基础速度
spd = 400
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,structure,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"
-- 单位声音设置
unitSound = "HeroDreadLord"
-- 动画 - 行走速度
walk = 400.0
-- 攻击 1 - 武器类型
weapTp1 = "instant"

[N076]
_parent = "ninf"
-- 英雄 - 初始敏捷
AGI = 100
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNGarithos.blp"
-- 英雄 - 初始智力
INT = 100
-- 名字
Name = "|CffFF5C26无惧的赞布恩|r"
-- 英雄 - 主要属性
Primary = "STR"
-- 称谓
Propernames = "|CffFF0000大领主|r"
-- 英雄 - 初始力量
STR = 100
-- 普通
abilList = "A0AH,A0HR,A01R,A0A5"
-- 主动攻击范围
acquire = 2500.0
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.56
-- 颜色值(蓝)
blue = 180
-- 碰撞体积
collision = 1.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.1
-- 护甲类型
defType = "hero"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.43
-- 模型文件
file = "units\\human\\HeroPaladinBoss2\\HeroPaladinBoss2.mdl"
-- 颜色值(绿)
green = 180
-- 最小速度
minSpd = 300
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 175
-- 基础速度
spd = 300
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,structure,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器声音
weapType1 = "MetalHeavyBash"

[U02Q]
_parent = "Ucrl"
-- 英雄 - 初始敏捷
AGI = 100
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNGhostMage.blp"
-- 生命最大值
HP = 300000
-- 英雄 - 初始智力
INT = 100
-- 名字
Name = "|Cff99CCFF光耀形态|r"
-- 称谓
Propernames = "|CffFF0000血色高阶祭司|r"
-- 英雄 - 初始力量
STR = 100
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNGhostMage.blp"
-- 普通
abilList = "A0AH,A09U,A01R,A0DT"
-- 主动攻击范围
acquire = 20000.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 碰撞体积
collision = 1.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 基础护甲
def = 150.0
-- 攻击 1 - 基础伤害
dmgplus1 = 1050
-- 模型文件
file = "war3mapImported\\holyruneguardian.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 可作为中立敌对显示
hostilePal = 1
-- 等级
level = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 最小速度
minSpd = 350
-- 模型缩放
modelScale = 2.0
-- 攻击 1 - 攻击范围
rangeN1 = 175
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 200.0
-- 选择缩放
scale = 5.0
-- 阴影图像 - 高度
shadowH = 450.0
-- 阴影图像 - 宽度
shadowW = 450.0
-- 阴影图像 - X轴偏移
shadowX = 225.0
-- 阴影图像 - Y轴偏移
shadowY = 225.0
-- 基础速度
spd = 400
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,structure,air"
-- 单位类别
type = "ancient"
-- 单位声音设置
unitSound = "KelThuzadLich"
-- 动画 - 行走速度
walk = 200.0

[e000]
_parent = "emtg"
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 可作为中立敌对显示
hostilePal = 1
-- 模型缩放
modelScale = 1.25
-- 种族
race = "creeps"
-- 使用科技
upgrades = ""

[e001]
_parent = "emtg"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\creeps\\RockGolem\\RockGolem.mdl"
-- 颜色值(绿)
green = 100
-- 可作为中立敌对显示
hostilePal = 1
-- 模型缩放
modelScale = 2.0
-- 种族
race = "creeps"
-- 单位类别
type = "ancient"
-- 使用科技
upgrades = ""

[e002]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNScatterRockets.blp"
-- 可建造建筑
Builds = ""
-- 攻击 1 - 全伤害范围
Farea1 = 200
-- 生命最大值
HP = 2
-- 攻击 1 - 中伤害范围
Harea1 = 200
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 射弹自导允许
MissileHoming_1 = 1
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1100
-- 名字
Name = "·马甲·憎恨"
-- 攻击 1 - 小伤害范围
Qarea1 = 200
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 300.0
-- 普通
abilList = "A0MY,A00J"
-- 主动攻击范围
acquire = 3000.0
-- 攻击 1 - 攻击类型
atkType1 = "pierce"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.1
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = ".mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 2500
-- 生命回复
regenHP = 0.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 75.0
-- 阴影图像 - Y轴偏移
shadowY = 75.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 1
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ward"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[e003]
_parent = "earc"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.2
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\az_ts_missile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "|Cff999999黑暗哨兵|r"
-- 普通
abilList = "A08T,A0BP,A00Z"
-- 主动攻击范围
acquire = 2000.0
-- 攻击 1 - 动画回复点
backSw1 = 0.58
-- 颜色值(蓝)
blue = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 攻击 1 - 基础伤害
dmgplus1 = 100
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "war3mapImported\\UDArcher.mdl"
-- 颜色值(绿)
green = 0
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 1000
-- 颜色值(红)
red = 0
-- 选择缩放
scale = -1.0
-- 缩放投射物
scaleBull = 0
-- 基础速度
spd = 350
-- 使用科技
upgrades = ""

[e004]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 1
-- 名字
Name = "·万用马甲"
-- 普通
abilList = "A00J,A024"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 碰撞体积
collision = 0.0
-- 死亡时间(秒)
death = 0.1
-- 模型文件
file = ".mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 20.0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 修理时间
reptm = 0
-- 选择缩放
scale = -1.0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e005]
_parent = "earc"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 攻击 1 - 全伤害范围
Farea1 = 200
-- 攻击 1 - 中伤害范围
Harea1 = 200
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.2
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\firebrand shot orange.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "|Cff999999骷髅弓箭手|r"
-- 攻击 1 - 小伤害范围
Qarea1 = 200
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 普通
abilList = "A08T,A0MY,A0L3,A0BP,A00Z"
-- 主动攻击范围
acquire = 2000.0
-- 攻击 1 - 动画回复点
backSw1 = 0.58
-- 颜色值(蓝)
blue = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 基础伤害
dmgplus1 = 100
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.28
-- 模型文件
file = "units\\creeps\\SkeletonArcher\\SkeletonArcher.mdl"
-- 颜色值(绿)
green = 0
-- 模型缩放
modelScale = 1.25
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 1000
-- 颜色值(红)
red = 0
-- 动画 - 跑步速度
run = 180.0
-- 选择缩放
scale = -1.0
-- 基础速度
spd = 400
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 单位声音设置
unitSound = "SkeletonArcher"
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 180.0
-- 攻击 1 - 武器类型
weapTp1 = "msplash"

[e006]
_parent = "earc"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 攻击 1 - 全伤害范围
Farea1 = 40
-- 生命最大值
HP = 5
-- 攻击 1 - 中伤害范围
Harea1 = 40
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.1
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\prismbeam_master.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "|Cff999999虚空之遗|r"
-- 攻击 1 - 小伤害范围
Qarea1 = 40
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 普通
abilList = "A0BP,A07H,A00Z"
-- 主动攻击范围
acquire = 9999.0
-- 攻击 1 - 动画回复点
backSw1 = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 基础伤害
dmgplus1 = 100
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.0
-- 模型文件
file = "war3mapImported\\yoggsarontentaclethin1.mdl"
-- 颜色值(绿)
green = 100
-- 射弹偏移 - Z
launchZ = 300.0
-- 模型缩放
modelScale = 0.5
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 800
-- 生命回复
regenHP = 0.0
-- 选择缩放
scale = -1.0
-- 基础速度
spd = 0
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 使用科技
upgrades = ""

[e007]
_parent = "emtg"
-- 名字
Name = "lv19.娜迦皇家卫士"
-- 需求
Requires = ""
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 模型文件
file = "units\\naga\\NagaMyrmidon\\NagaMyrmidon.mdl"
-- 可作为中立敌对显示
hostilePal = 1
-- 种族
race = "undead"
-- 动画 - 跑步速度
run = 220.0
-- 选择缩放
scale = 2.0
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 220.0

[e008]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 5
-- 名字
Name = "·小助手"
-- 普通
abilList = "Avul,AInv"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 死亡时间(秒)
death = 0.1
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 是一个建筑
isbldg = 1
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e009]
_parent = "emtg"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv19.娜迦皇家教官"
-- 需求
Requires = ""
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "lord shaream.mdl"
-- 可作为中立敌对显示
hostilePal = 1
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 动画 - 跑步速度
run = 220.0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 220.0

[e00O]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 100
-- 名字
Name = "·马甲·哀恸箭"
-- 普通
abilList = "A0C8,A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 碰撞体积
collision = 0.0
-- 模型文件
file = "war3mapImported\\az2_az_ta01_d2.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 1.0
-- 类型
movetp = "fly"
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 选择缩放
scale = 0.1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 1
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 转身速度
turnRate = 3.0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e00S]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 5
-- 名字
Name = "·马甲·暗影烈焰"
-- 普通
abilList = "A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 死亡时间(秒)
death = 2.0
-- 模型文件
file = "az_feiyan.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 100.0
-- 类型
movetp = "fly"
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 522
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 阴影图像(单位)
unitShadow = "ShadowFlyer"
-- 使用科技
upgrades = ""

[e00T]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 5
-- 名字
Name = "·马甲·群体暗影烈焰"
-- 普通
abilList = "A09K,A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 碰撞体积
collision = 0.0
-- 死亡时间(秒)
death = 0.1
-- 模型文件
file = ".mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 选择缩放
scale = 0.1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e00U]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 名字 - 编辑器后缀
EditorSuffix = "大"
-- 生命最大值
HP = 100
-- 名字
Name = "·马甲·哀恸箭"
-- 普通
abilList = "A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 碰撞体积
collision = 0.0
-- 模型文件
file = "deadly plumage hd purple.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 1.0
-- 类型
movetp = "fly"
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 选择缩放
scale = 0.1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 1
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 转身速度
turnRate = 3.0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e00W]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNFarSight.blp"
-- 可建造建筑
Builds = ""
-- 名字 - 编辑器后缀
EditorSuffix = "大"
-- 生命最大值
HP = 2
-- 名字
Name = "·马甲·暗影匕首"
-- 普通
abilList = "A072,A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 模型文件
file = "war3mapImported\\az_ts_missile.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 魔法初始数量
mana0 = 35
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 2.0
-- 高度
moveHeight = 50.0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 魔法回复
regenMana = 5.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e00X]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 5
-- 名字
Name = "·马甲·风行者之箭"
-- 普通
abilList = "A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 高度变化 - 采样范围
elevRad = 0.0
-- 模型文件
file = "war3mapImported\\dekan-bosaidong-weapon.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 最小高度
moveFloor = 128.0
-- 高度
moveHeight = 128.0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 1
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 转身速度
turnRate = 3.0
-- 单位类别
type = ""
-- 阴影图像(单位)
unitShadow = "ShadowFlyer"
-- 使用科技
upgrades = ""

[e00Z]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNFarSight.blp"
-- 可建造建筑
Builds = ""
-- 名字 - 编辑器后缀
EditorSuffix = "干杂活版"
-- 生命最大值
HP = 2
-- 名字
Name = "·万用马甲"
-- 普通
abilList = "A00A,A00J,A024"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 模型文件
file = ".mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 魔法初始数量
mana0 = 35
-- 魔法最大值
manaN = 100
-- 模型缩放
modelScale = 0.1
-- 高度
moveHeight = 50.0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 魔法回复
regenMana = 5.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e010]
_parent = "earc"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "大"
-- 攻击 1 - 全伤害范围
Farea1 = 20
-- 生命最大值
HP = 5
-- 攻击 1 - 中伤害范围
Harea1 = 20
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.1
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\prismbeam_master.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "|Cff999999虚空之遗|r"
-- 攻击 1 - 小伤害范围
Qarea1 = 20
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 普通
abilList = "A0EH,A07H,A00J,A0BP"
-- 主动攻击范围
acquire = 9999.0
-- 攻击 1 - 动画回复点
backSw1 = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 攻击 1 - 基础伤害
dmgplus1 = 100
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.0
-- 模型文件
file = "war3mapImported\\yoggsarontentaclethin1.mdl"
-- 颜色值(绿)
green = 100
-- 射弹偏移 - Z
launchZ = 400.0
-- 模型缩放
modelScale = 0.75
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 1200
-- 生命回复
regenHP = 0.0
-- 基础速度
spd = 0
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 使用科技
upgrades = ""

[e01K]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNScatterRockets.blp"
-- 可建造建筑
Builds = ""
-- 攻击 1 - 全伤害范围
Farea1 = 200
-- 生命最大值
HP = 2
-- 攻击 1 - 中伤害范围
Harea1 = 200
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Spells\\Other\\BlackArrow\\BlackArrowMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "·马甲·黑暗攻击"
-- 攻击 1 - 小伤害范围
Qarea1 = 200
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 300.0
-- 普通
abilList = "A00J"
-- 主动攻击范围
acquire = 3000.0
-- 攻击 1 - 攻击类型
atkType1 = "pierce"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.1
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = "war3mapImported\\az2_az_ta01_d2.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 1.0
-- 类型
movetp = "fly"
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 2500
-- 生命回复
regenHP = 0.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 1
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,nonancient,air"
-- 转身速度
turnRate = 3.0
-- 单位类别
type = "_"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[e01M]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNScatterRockets.blp"
-- 可建造建筑
Builds = ""
-- 攻击 1 - 全伤害范围
Farea1 = 200
-- 生命最大值
HP = 2
-- 攻击 1 - 中伤害范围
Harea1 = 200
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "·马甲·镇压"
-- 攻击 1 - 小伤害范围
Qarea1 = 200
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 300.0
-- 普通
abilList = "A0MY,A00J"
-- 主动攻击范围
acquire = 3000.0
-- 攻击 1 - 攻击类型
atkType1 = "pierce"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 4.0
-- 死亡时间(秒)
death = 0.1
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = ".mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 100.0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 2500
-- 生命回复
regenHP = 0.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 75.0
-- 阴影图像 - Y轴偏移
shadowY = 75.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 1
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,nonancient,air"
-- 单位类别
type = "ward"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[e01P]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\PassiveButtons\\PASBTNScatterRockets.blp"
-- 可建造建筑
Builds = ""
-- 攻击 1 - 全伤害范围
Farea1 = 200
-- 生命最大值
HP = 2
-- 攻击 1 - 中伤害范围
Harea1 = 200
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "·马甲·刺骨"
-- 攻击 1 - 小伤害范围
Qarea1 = 200
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 300.0
-- 普通
abilList = "A0MY,A00J"
-- 主动攻击范围
acquire = 3000.0
-- 攻击 1 - 攻击类型
atkType1 = "pierce"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 2.0
-- 死亡时间(秒)
death = 0.1
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = ".mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 100.0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 2500
-- 生命回复
regenHP = 0.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 75.0
-- 阴影图像 - Y轴偏移
shadowY = 75.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 1
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ward"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[e01R]
_parent = "earc"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\firebrand shot green.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2500
-- 名字
Name = "|Cff999999女妖|r"
-- 普通
abilList = "A08T,A0BP,A00Z"
-- 主动攻击范围
acquire = 9999.0
-- 攻击 1 - 动画回复点
backSw1 = 0.51
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 基础伤害
dmgplus1 = 100
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.56
-- 模型文件
file = "units\\undead\\Banshee\\Banshee.mdl"
-- 高度
moveHeight = 50.0
-- 类型
movetp = "hover"
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 1500
-- 基础速度
spd = 450
-- 使用科技
upgrades = ""

[e01T]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNFarSight.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 2
-- 名字
Name = "·马甲·暗影匕首"
-- 普通
abilList = "A072,A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 模型文件
file = "war3mapImported\\Ephemeral Slash Purple.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 魔法初始数量
mana0 = 35
-- 魔法最大值
manaN = 100
-- 高度
moveHeight = 50.0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 魔法回复
regenMana = 5.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[e01Y]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 5
-- 名字
Name = "·马甲·风行者之箭黑暗"
-- 普通
abilList = "A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 碰撞体积
collision = 0.0
-- 死亡时间(秒)
death = 2.0
-- 模型文件
file = "az_z024.mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 最小高度
moveFloor = 128.0
-- 高度
moveHeight = 128.0
-- 类型
movetp = "fly"
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 522
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 阴影图像(单位)
unitShadow = "ShadowFlyer"
-- 使用科技
upgrades = ""

[e03Q]
_parent = "ewsp"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNCritterChicken.blp"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 5
-- 名字
Name = "·马甲·女妖冲击"
-- 普通
abilList = "A0M3,A00J"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 可以逃跑
canFlee = 0
-- 运输尺寸
cargoSize = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 碰撞体积
collision = 0.0
-- 死亡时间(秒)
death = 0.1
-- 模型文件
file = ".mdl"
-- 队形排列
formation = 0
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 0
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 选择缩放
scale = 0.1
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""

[h000]
_parent = "hgra"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArcaneObservatory.blp"
-- 生命最大值
HP = 1
-- 名字
Name = "|CffFFFF8C闹鬼金矿|r"
-- 可研究项目
Researches = ""
-- 售出物品
Sellitems = "I000,I01A,I03N"
-- 训练单位
Trains = ""
-- 普通
abilList = "Aneu,Avul,Apit"
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "buildings\\undead\\HauntedMine\\HauntedMine.mdl"
-- 模型缩放
modelScale = 0.6
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 选择缩放
scale = 2.0
-- 单位类别
type = "mechanical,neutral"
-- 建筑地面纹理
uberSplat = "USMA"
-- 单位声音设置
unitSound = "CircleOfPower"
-- 使用科技
upgrades = ""

[h00E]
_parent = "hgry"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 可作为中立敌对显示
hostilePal = 1
-- 种族
race = "creeps"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[h00G]
_parent = "Hmkg"
-- 名字
Name = "|Cff4DFFFF福利领取|r"
-- 普通
abilList = "A0UE,A0HC,A0K6,A0IA,A0HE,A0I8,A0I9,A0KG,A0K7,A0UC,A0UF,A0UB,Avul,Apit"
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "foa_portal_01.mdl"
-- 占用人口
fused = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 是一个建筑
isbldg = 1
-- 基础速度
spd = 0
-- 分类 - 特殊
special = 1
-- 允许攻击模式
weapsOn = 0

[h00J]
_parent = "hsor"
-- 名字 - 编辑器后缀
EditorSuffix = "挑战"
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\FrostWyrmMissile\\FrostWyrmMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1650
-- 名字
Name = "巫妖"
-- 普通
abilList = ""
-- 攻击 1 - 攻击类型
atkType1 = "pierce"
-- 攻击 1 - 动画回复点
backSw1 = 0.54
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.46
-- 模型文件
file = "war3mapImported\\SummonerAraj.mdl"
-- 颜色值(绿)
green = 150
-- 模型缩放
modelScale = 2.0
-- 类型
movetp = "foot"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 1000
-- 颜色值(红)
red = 150
-- 选择缩放
scale = 2.0
-- 使用科技
upgrades = ""

[h00K]
_parent = "hsor"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNHeroPaladin.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "挑战"
-- 攻击 1 - 投射物图像
Missileart_1 = ".mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 0
-- 名字
Name = "血色圣骑士"
-- 普通
abilList = ""
-- 攻击 1 - 攻击类型
atkType1 = "pierce"
-- 攻击 1 - 动画回复点
backSw1 = 0.41
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.56
-- 模型文件
file = "war3mapImported\\paladinhorse1.02.mdl"
-- 黄金消耗
goldcost = 0
-- 模型缩放
modelScale = 1.25
-- 类型
movetp = "foot"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 动画 - 跑步速度
run = 380.0
-- 选择缩放
scale = 2.0
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 380.0
-- 攻击 1 - 武器类型
weapTp1 = "normal"

[h00L]
_parent = "hgry"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\PhoenixMissile\\Phoenix_Missile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 名字
Name = "凤凰"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 颜色值(蓝)
blue = 150
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\human\\phoenix\\phoenix.mdl"
-- 颜色值(绿)
green = 150
-- 可作为中立敌对显示
hostilePal = 1
-- 模型缩放
modelScale = 1.5
-- 种族
race = "creeps"
-- 单位类别
type = "ancient"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[h00U]
_parent = "hprt"
-- 生命最大值
HP = 240
-- 名字
Name = "|CffFF5C26心能球|r"
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 200.0
-- 普通
abilList = "A00J"
-- 主动攻击范围
acquire = 500.0
-- 分类 - 战役
campaign = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = "war3mapImported\\wisp.mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 高度
moveHeight = 30.0
-- 类型
movetp = "hover"
-- 中立建筑 - 显示小地图标记
nbmmIcon = 1
-- 攻击 1 - 攻击范围
rangeN1 = 400
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 50.0
-- 阴影图像 - 宽度
shadowW = 50.0
-- 阴影图像 - X轴偏移
shadowX = 25.0
-- 阴影图像 - Y轴偏移
shadowY = 25.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 分类 - 特殊
special = 0
-- 作为目标类型
targType = "air"
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,nonancient,hero"
-- 转身速度
turnRate = 0.1
-- 阴影图像(单位)
unitShadow = "Shadow"
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[h010]
_parent = "hcth"
-- 生命最大值
HP = 6000
-- 名字
Name = "被诅咒的符文商店"
-- 可研究项目
Researches = "R003,R008,R00L,R00I,R005,R00C,R006,R007,R000,R001,R002,R004"
-- 普通
abilList = "Avul"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 30.0
-- 护甲类型
defType = "fort"
-- 攻击 1 - 基础伤害
dmgplus1 = 0
-- 模型文件
file = "buildings\\undead\\Graveyard\\Graveyard.mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 是一个建筑
isbldg = 1
-- 等级
level = 1
-- 模型缩放
modelScale = 0.1
-- 类型
movetp = ""
-- 修理时间
reptm = 0
-- 选择缩放
scale = -1.0
-- 阴影图像 - 高度
shadowH = 0.0
-- 阴影图像 - 宽度
shadowW = 0.0
-- 阴影图像 - X轴偏移
shadowX = 0.0
-- 阴影图像 - Y轴偏移
shadowY = 0.0
-- 基础速度
spd = 0
-- 队伍颜色
teamColor = 11
-- 阴影图像(单位)
unitShadow = ""
-- 使用科技
upgrades = ""
-- 允许攻击模式
weapsOn = 0

[h014]
_parent = "hkni"
-- 循环淡入率
LoopingSoundFadeIn = 0
-- 循环淡出率
LoopingSoundFadeOut = 0
-- 移动
MovementSoundLabel = ""
-- 名字
Name = "|CffFF9326属性强化|r"
-- 需求
Requires = ""
-- 普通
abilList = "A0UE,A0HC,A0K6,A0IA,A0HE,A0I8,A0I9,A0KG,A0K7,A0UC,A0UF,A0UB,Avul"
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 护甲类型
defType = "fort"
-- 攻击 1 - 基础伤害
dmgplus1 = 0
-- 模型文件
file = ".mdl"
-- 占用人口
fused = 0
-- 颜色值(绿)
green = 150
-- 隐藏小地图显示
hideOnMinimap = 1
-- 是一个建筑
isbldg = 1
-- 模型缩放
modelScale = 0.1
-- 类型
movetp = ""
-- 颜色值(红)
red = 150
-- 选择缩放
scale = 1.5
-- 基础速度
spd = 0
-- 队伍颜色
teamColor = 0
-- 阴影图像(单位)
unitShadow = ""
-- 单位声音设置
unitSound = ""
-- 使用科技
upgrades = ""
-- 允许攻击模式
weapsOn = 0

[h016]
_parent = "hprt"
-- 生命最大值
HP = 240
-- 名字
Name = "|CffFF5C26金币大|r"
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 200.0
-- 普通
abilList = "A00J"
-- 主动攻击范围
acquire = 500.0
-- 分类 - 战役
campaign = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = "Objects\\InventoryItems\\PotofGold\\PotofGold.mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 类型
movetp = ""
-- 中立建筑 - 显示小地图标记
nbmmIcon = 1
-- 攻击 1 - 攻击范围
rangeN1 = 400
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 50.0
-- 阴影图像 - 宽度
shadowW = 50.0
-- 阴影图像 - X轴偏移
shadowX = 25.0
-- 阴影图像 - Y轴偏移
shadowY = 25.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 分类 - 特殊
special = 0
-- 作为目标类型
targType = "air"
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,nonancient,hero"
-- 转身速度
turnRate = 0.1
-- 阴影图像(单位)
unitShadow = "Shadow"
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[h01B]
_parent = "hprt"
-- 生命最大值
HP = 240
-- 名字
Name = "|CffFF5C26木材|r"
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 200.0
-- 普通
abilList = "A00J"
-- 主动攻击范围
acquire = 500.0
-- 分类 - 战役
campaign = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = "Objects\\InventoryItems\\BundleofLumber\\BundleofLumber.mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 类型
movetp = ""
-- 中立建筑 - 显示小地图标记
nbmmIcon = 1
-- 攻击 1 - 攻击范围
rangeN1 = 400
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 选择缩放
scale = 3.0
-- 阴影图像 - 高度
shadowH = 50.0
-- 阴影图像 - 宽度
shadowW = 50.0
-- 阴影图像 - X轴偏移
shadowX = 25.0
-- 阴影图像 - Y轴偏移
shadowY = 25.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 分类 - 特殊
special = 0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,nonancient,hero"
-- 转身速度
turnRate = 0.1
-- 阴影图像(单位)
unitShadow = "Shadow"
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[h01C]
_parent = "hpea"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 1000
-- 名字
Name = "随从"
-- 普通
abilList = "A096,A097,A06U,A04I,A00Z,AInv"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 1
-- 碰撞体积
collision = 0.0
-- 模型文件
file = "units\\creeps\\Kobold\\Kobold.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 类型
movetp = "hover"
-- 生命回复
regenHP = 0.0
-- 修理时间
reptm = 0
-- 基础速度
spd = 522
-- 单位声音设置
unitSound = "Kobold"
-- 使用科技
upgrades = ""
-- 允许攻击模式
weapsOn = 0

[h01O]
_parent = "Hpal"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNMagicVault.blp"
-- 名字
Name = "武器商店"
-- 图标 - 计分屏
ScoreScreenIcon = "ReplaceableTextures\\CommandButtons\\BTNMagicVault.blp"
-- 售出物品
Sellitems = [=[
S031,S032,S033,S034,S035
,S036
,S037
,S038
,S039
,I029]=]
-- 普通
abilList = "A0GW,Avul,Apit"
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 100
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 运输尺寸
cargoSize = 0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "war3mapimported\\naaru.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 100
-- 是一个建筑
isbldg = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法初始数量
mana0 = 0
-- 模型缩放
modelScale = 1.3
-- 高度
moveHeight = 175.0
-- 类型
movetp = "hover"
-- 视野范围(夜晚)
nsight = 0
-- 颜色值(红)
red = 100
-- 修理时间
reptm = 0
-- 视野范围(白天)
sight = 0
-- 基础速度
spd = 0
-- 雇佣时间间隔
stockRegen = 0
-- 雇佣开始时间
stockStart = 0
-- 队伍颜色
teamColor = 12
-- 建筑地面纹理
uberSplat = "USMA"
-- 允许攻击模式
weapsOn = 0

[h01Q]
_parent = "hprt"
-- 名字
Name = "|Cff4DD2FF虚空传送门|r"
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 100.0
-- 普通
abilList = "A09Z,A00J"
-- 攻击 1 - 动画回复点
backSw1 = 1.25
-- 颜色值(蓝)
blue = 0
-- 分类 - 战役
campaign = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 500000
-- 攻击 1 - 动画伤害点
dmgpt1 = 2.0
-- 高度变化 - 采样范围
elevRad = 1.0
-- 模型文件
file = "war3mapImported\\xjcsmppbyq.mdl"
-- 颜色值(绿)
green = 0
-- 模型缩放
modelScale = 2.4
-- 攻击 1 - 攻击范围
rangeN1 = 250
-- 颜色值(红)
red = 0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 分类 - 特殊
special = 0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air,hero"
-- 单位类别
type = "ward"
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[h01X]
_parent = "Hpb2"
-- 生命最大值
HP = 1
-- 名字
Name = "结界魔方"
-- 普通
abilList = "A010"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "war3mapImported\\ss61.mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 英雄
heroAbilList = ""
-- 是一个建筑
isbldg = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法初始数量
mana0 = 0
-- 类型
movetp = ""
-- 视野范围(夜晚)
nsight = 1600
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.0
-- 视野范围(白天)
sight = 1600
-- 基础速度
spd = 0
-- 分类 - 特殊
special = 1
-- 最大库存量
stockMax = 0
-- 雇佣时间间隔
stockRegen = 0
-- 队伍颜色
teamColor = 9
-- 允许攻击模式
weapsOn = 0

[h02B]
_parent = "hgra"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArcaneObservatory.blp"
-- 生命最大值
HP = 1
-- 名字
Name = "|CffFFC92C帝国雕像|r"
-- 可研究项目
Researches = "R008,R00I,R006,R007,R005,R00C,R003,R004,R000,R00L,R001,R002"
-- 训练单位
Trains = ""
-- 普通
abilList = ""
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "war3mapImported\\fk_sylvanasstatue.mdl"
-- 模型缩放
modelScale = 1.5
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 选择缩放
scale = 2.0
-- 建筑地面纹理
uberSplat = ""
-- 单位声音设置
unitSound = "CircleOfPower"
-- 使用科技
upgrades = ""

[h02C]
_parent = "hgra"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArcaneObservatory.blp"
-- 生命最大值
HP = 1
-- 名字
Name = "|CffFFFF8C红宝石|r"
-- 需求
Requires = ""
-- 可研究项目
Researches = ""
-- 售出物品
Sellitems = [=[
S001,S002,S003,S004
,S005
,S006
,S007
,S008
,S009
,I0CK]=]
-- 训练单位
Trains = ""
-- 普通
abilList = "A03W,A0GW,Avul,Apit"
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "war3mapImported\\item_orb_vampire.mdl"
-- 模型缩放
modelScale = 2.0
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 选择缩放
scale = 2.0
-- 建筑地面纹理
uberSplat = "USMA"
-- 单位声音设置
unitSound = "CircleOfPower"
-- 使用科技
upgrades = ""

[h02D]
_parent = "hgra"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNTempleOfTheDamned.blp"
-- 生命最大值
HP = 1
-- 名字
Name = "|CffFF5C26通灵学院|r"
-- 需求
Requires = ""
-- 可研究项目
Researches = ""
-- 训练单位
Trains = ""
-- 普通
abilList = "Avul"
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "buildings\\undead\\TempleOfTheDamned\\TempleOfTheDamned.mdl"
-- 模型缩放
modelScale = 0.85
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 选择缩放
scale = 1.75
-- 分类 - 特殊
special = 1
-- 单位类别
type = "mechanical,neutral"
-- 建筑地面纹理
uberSplat = "USMA"
-- 单位声音设置
unitSound = "CircleOfPower"
-- 使用科技
upgrades = ""

[h02G]
_parent = "hpea"
-- 可建造建筑
Builds = ""
-- 生命最大值
HP = 10000000
-- 名字
Name = "木桩"
-- 普通
abilList = "A01R"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 0.11
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 模型缩放
modelScale = 3.0
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 生命回复
regenHP = 1000000000.0
-- 修理时间
reptm = 0
-- 缩放投射物
scaleBull = 0
-- 基础速度
spd = 0
-- 单位类别
type = ""
-- 使用科技
upgrades = ""
-- 允许攻击模式
weapsOn = 0

[h02K]
_parent = "hprt"
-- 生命最大值
HP = 240
-- 名字
Name = "|CffFF5C26银币|r"
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 200.0
-- 普通
abilList = "A00J"
-- 主动攻击范围
acquire = 500.0
-- 分类 - 战役
campaign = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 模型文件
file = "war3mapImported\\SilverCoin.mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 类型
movetp = ""
-- 中立建筑 - 显示小地图标记
nbmmIcon = 1
-- 攻击 1 - 攻击范围
rangeN1 = 400
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 50.0
-- 阴影图像 - 宽度
shadowW = 50.0
-- 阴影图像 - X轴偏移
shadowX = 25.0
-- 阴影图像 - Y轴偏移
shadowY = 25.0
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 分类 - 特殊
special = 0
-- 作为目标类型
targType = "air"
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,nonancient,hero"
-- 转身速度
turnRate = 0.1
-- 阴影图像(单位)
unitShadow = "Shadow"
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[h02X]
_parent = "haro"
-- 名字
Name = "|CffFFFF8C蓝宝石|r"
-- 售出物品
Sellitems = [=[
S021,S022,S023,S024
,S025
,S026
,S027
,S028
,S029
,I001]=]
-- 普通
abilList = "A0GW,Avul,Apit"
-- 阴影图像(建筑)
buildingShadow = ""
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "war3mapimported\\item_orb_intelligence.mdl"
-- 模型缩放
modelScale = 2.0
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 选择缩放
scale = -1.0
-- 队伍颜色
teamColor = 12
-- 建筑地面纹理
uberSplat = "USMA"

[h03A]
_parent = "hwtw"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArcaneObservatory.blp"
-- 生命最大值
HP = 1
-- 名字
Name = "黑心商人"
-- 建筑升级
Upgrade = ""
-- 普通
abilList = "Avul"
-- 颜色值(蓝)
blue = 100
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 护甲类型
defType = "fort"
-- 模型文件
file = "units\\undead\\Acolyte\\Acolyte.mdl"
-- 颜色值(绿)
green = 100
-- 隐藏小地图显示
hideOnMinimap = 1
-- 是一个建筑
isbldg = 0
-- 模型缩放
modelScale = 1.45
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 颜色值(红)
red = 100
-- 选择缩放
scale = -1.0
-- 单位类别
type = ""
-- 建筑地面纹理
uberSplat = ""
-- 使用科技
upgrades = ""

[h03S]
_parent = "haro"
-- 名字
Name = "|CffA338EE埋骨地|r"
-- 售出物品
Sellitems = "I009,I01J,I082,I088,I0GM,I070,I006,I00G"
-- 普通
abilList = "Aneu,Asid,Avul,Asud,Apit"
-- 阴影图像(建筑)
buildingShadow = ""
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "buildings\\undead\\BoneYard\\BoneYard.mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.8
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 选择缩放
scale = -1.0
-- 建筑地面纹理
uberSplat = "USMA"

[n000]
_parent = "ncop"
-- 名字
Name = "|CffA338EE埋骨地|r"
-- 可研究项目
Researches = "R00G,R00F,R00E,R00D,R00B,R00A"
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = ".mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.1
-- 路径纹理
pathTex = ""
-- 种族
race = "human"
-- 使用点击帮助
useClickHelper = 0

[n001]
_parent = "nico"
-- 要求动画名 - 附加动画
Attachmentanimprops = "medium"
-- 名字 - 编辑器后缀
EditorSuffix = "1"
-- 生命最大值
HP = 20000
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\ZigguratMissile\\ZigguratMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1200
-- 名字
Name = "侍女雕像"
-- 普通
abilList = "A00Z,A0I9,A0K7,A0KG,A0I8,A0HE,A0IA,A0K6,A0HC"
-- 主动攻击范围
acquire = 1200.0
-- 要求动画名
animProps = "upgrade,second"
-- 装甲类型
armor = "Stone"
-- 攻击 1 - 攻击类型
atkType1 = "chaos"
-- 攻击 1 - 动画回复点
backSw1 = 0.5
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 攻击 1 - 伤害骰子数量
dice1 = 1
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.5
-- 模型文件
file = "war3mapimported\\hd_grave.mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 模型缩放
modelScale = 2.0
-- 类型
movetp = "foot"
-- 路径纹理
pathTex = ""
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 750
-- 攻击 1 - 伤害骰子面数
sides1 = 1
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "ground,structure,debris,air,item,ward"
-- 队伍颜色
teamColor = 3
-- 建筑地面纹理
uberSplat = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[n002]
_parent = "nwiz"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArthas.blp"
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = ".mdl"
-- 名字
Name = "lv13.血色祭司"
-- 普通
abilList = "A0A3,A09Q"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 颜色值(蓝)
blue = 175
-- 黄金奖励 - 基础值
bountyplus = 20
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 护甲类型
defType = "large"
-- 模型文件
file = "units\\human\\Priest\\Priest.mdl"
-- 颜色值(绿)
green = 175
-- 等级
level = 6
-- 模型缩放
modelScale = 1.25
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 550
-- 选择缩放
scale = 1.2
-- 基础速度
spd = 400
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 攻击 1 - 武器类型
weapTp1 = "instant"

[n004]
_parent = "nkot"
-- 名字 - 编辑器后缀
EditorSuffix = "挑战"
-- 名字
Name = "血色农夫"
-- 普通
abilList = ""
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 255
-- 黄金奖励 - 基础值
bountyplus = 4
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 护甲类型
defType = "large"
-- 模型文件
file = "units\\human\\Peasant\\Peasant.mdl"
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 255
-- 等级
level = 1
-- 木材消耗
lumbercost = 0
-- 模型缩放
modelScale = 1.0
-- 种族
race = "undead"
-- 阴影图像 - 高度
shadowH = 100.0
-- 阴影图像 - 宽度
shadowW = 100.0
-- 阴影图像 - X轴偏移
shadowX = 45.0
-- 阴影图像 - Y轴偏移
shadowY = 45.0
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"

[n005]
_parent = "nwrg"
-- 名字
Name = "猛犸"
-- 普通
abilList = "A0A3"
-- 装甲类型
armor = "Flesh"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "Units\\Creeps\\Mammoth\\Mammoth.mdl"
-- 等级
level = 12
-- 模型缩放
modelScale = 1.5
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 地形设置
tilesets = "*"

[n006]
_parent = "nkot"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNRiderlessHorse.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "挑战"
-- 名字
Name = "血色驮马"
-- 普通
abilList = ""
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 255
-- 黄金奖励 - 基础值
bountyplus = 4
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 护甲类型
defType = "large"
-- 模型文件
file = "units\\critters\\PackHorse\\PackHorse.mdl"
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 255
-- 等级
level = 1
-- 木材消耗
lumbercost = 0
-- 模型缩放
modelScale = 1.0
-- 种族
race = "undead"
-- 动画 - 跑步速度
run = 200.0
-- 阴影图像 - 高度
shadowH = 100.0
-- 阴影图像 - 宽度
shadowW = 100.0
-- 阴影图像 - X轴偏移
shadowX = 45.0
-- 阴影图像 - Y轴偏移
shadowY = 45.0
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 动画 - 行走速度
walk = 200.0

[n007]
_parent = "ninf"
-- 英雄 - 初始敏捷
AGI = 100
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNGarithos.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "挑战"
-- 英雄 - 初始智力
INT = 100
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\BloodElfSpellThiefMISSILE\\BloodElfSpellThiefMISSILE.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1200
-- 名字
Name = "血色破咒师"
-- 英雄 - 主要属性
Primary = "STR"
-- 称谓
Propernames = "|CffFF0000大领主|r"
-- 英雄 - 初始力量
STR = 100
-- 普通
abilList = "A06L,A07P"
-- 主动攻击范围
acquire = 2500.0
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.5
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 1.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.1
-- 护甲类型
defType = "hero"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.5
-- 模型文件
file = "units\\human\\BloodElfSpellThief\\BloodElfSpellThief.mdl"
-- 模型缩放
modelScale = 1.2
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 250
-- 选择缩放
scale = 1.5
-- 基础速度
spd = 300
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,structure,air"
-- 地形设置
tilesets = "*"
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 攻击 1 - 武器声音
weapType1 = ""

[n008]
_parent = "nwgt"
-- 名字 - 编辑器后缀
EditorSuffix = "x"
-- 生命最大值
HP = 1
-- 名字
Name = "|CffB3FF99X1|r"
-- 售出物品
Sellitems = "I02U,I02X,I045,I037,I03G,I01V,I03R,I03T"
-- 普通
abilList = "A0EY,Avul,Apit"
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 210
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 模型文件
file = ".mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 230
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.1
-- 视野范围(夜晚)
nsight = 0
-- 路径纹理
pathTex = ""
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 颜色值(红)
red = 230
-- 修理时间
reptm = 0
-- 选择缩放
scale = -1.0
-- 视野范围(白天)
sight = 0
-- 分类 - 特殊
special = 1
-- 队伍颜色
teamColor = 12
-- 单位类别
type = ""
-- 建筑地面纹理
uberSplat = ""

[n00B]
_parent = "nkot"
-- 名字
Name = "lv1.狗头人奴隶"
-- 普通
abilList = "A0A3"
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 255
-- 黄金奖励 - 基础值
bountyplus = 4
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 护甲类型
defType = "large"
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 255
-- 等级
level = 1
-- 木材消耗
lumbercost = 0
-- 模型缩放
modelScale = 1.0
-- 种族
race = "undead"
-- 基础速度
spd = 250
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"

[n00C]
_parent = "nnwl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 模型文件
file = "units\\creeps\\NerubianQueen\\NerubianQueen.mdl"
-- 颜色值(绿)
green = 255
-- 模型缩放
modelScale = 1.6
-- 地形设置
tilesets = "*"

[n00D]
_parent = "nrvs"
-- 名字
Name = "lv2.血色民兵"
-- 普通
abilList = "A0A3"
-- 装甲类型
armor = "Flesh"
-- 颜色值(蓝)
blue = 180
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 8
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "units\\human\\Militia\\Militia.mdl"
-- 颜色值(绿)
green = 255
-- 等级
level = 1
-- 类型
movetp = "foot"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 100
-- 颜色值(红)
red = 255
-- 基础速度
spd = 260
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = ""

[n00E]
_parent = "nnwq"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\creeps\\SpiderBlue\\SpiderBlue.mdl"
-- 模型缩放
modelScale = 2.0
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n00F]
_parent = "nndr"
-- 生命最大值
HP = 2000000
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1200
-- 名字
Name = "lv18.虚空龙"
-- 普通
abilList = "A0A3"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 颜色值(蓝)
blue = 50
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 5.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 颜色值(绿)
green = 50
-- 等级
level = 15
-- 模型缩放
modelScale = 1.5
-- 类型
movetp = "hover"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 颜色值(红)
red = 150
-- 缩放投射物
scaleBull = 0
-- 基础速度
spd = 450
-- 攻击 1 - 范围影响目标
splashTargs1 = ""
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 转身速度
turnRate = 0.6
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[n00G]
_parent = "nsko"
-- 生命最大值
HP = 6000
-- 名字
Name = "lv3.兽族骷髅"
-- 普通
abilList = "A0A3"
-- 装甲类型
armor = "Flesh"
-- 颜色值(蓝)
blue = 200
-- 黄金奖励 - 基础值
bountyplus = 12
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 颜色值(绿)
green = 200
-- 等级
level = 1
-- 模型缩放
modelScale = 1.0
-- 种族
race = "undead"
-- 颜色值(红)
red = 200
-- 基础速度
spd = 280
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = ""

[n00H]
_parent = "nina"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[n00I]
_parent = "nspd"
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\ChimaeraAcidMissile\\ChimaeraAcidMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 名字
Name = "lv4.小蜘蛛"
-- 普通
abilList = "A0A3,A09M"
-- 黄金奖励 - 基础值
bountyplus = 16
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 护甲类型
defType = "large"
-- 模型文件
file = "units\\creeps\\SpiderBlack\\SpiderBlack.mdl"
-- 颜色值(绿)
green = 255
-- 模型缩放
modelScale = 0.8
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 250
-- 颜色值(红)
red = 255
-- 选择缩放
scale = 1.0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 攻击 1 - 武器声音
weapType1 = ""

[n00J]
_parent = "ncop"
-- 名字
Name = "|CffA338EE通关奖励|r"
-- 普通
abilList = "A00Z,A01D,A01C,A01E"
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = ".mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.1
-- 路径纹理
pathTex = ""
-- 种族
race = "human"

[n00K]
_parent = "ninm"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 名字
Name = "地狱火战车"
-- 普通
abilList = "A0A3"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 颜色值(蓝)
blue = 255
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 颜色值(绿)
green = 255
-- 模型缩放
modelScale = 1.75
-- 地形设置
tilesets = "*"
-- 单位类别
type = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[n00L]
_parent = "nano"
-- 名字
Name = "lv5.强盗"
-- 普通
abilList = "A0A3"
-- 装甲类型
armor = "Flesh"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 20
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 模型文件
file = "units\\creeps\\Bandit\\Bandit.mdl"
-- 等级
level = 1
-- 模型缩放
modelScale = 1.0
-- 种族
race = "undead"
-- 动画 - 跑步速度
run = 200.0
-- 选择缩放
scale = 1.2
-- 阴影图像 - 高度
shadowH = 100.0
-- 阴影图像 - 宽度
shadowW = 100.0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 动画 - 行走速度
walk = 200.0

[n00M]
_parent = "nfor"
-- 生命最大值
HP = 32000
-- 名字
Name = "lv8.无面者"
-- 普通
abilList = "A0A3,A008"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 28
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "Units\\Creeps\\Unbroken\\Unbroken.mdl"
-- 等级
level = 3
-- 模型缩放
modelScale = 0.8
-- 种族
race = "undead"
-- 基础速度
spd = 330
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"

[n00O]
_parent = "nhrq"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型缩放
modelScale = 2.5
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[n00P]
_parent = "nbrg"
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\BansheeMissile\\BansheeMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1100
-- 名字
Name = "lv6.幽灵"
-- 普通
abilList = "A0A3"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 黄金奖励 - 基础值
bountyplus = 24
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "units\\creeps\\BansheeGhost\\BansheeGhost.mdl"
-- 等级
level = 3
-- 模型缩放
modelScale = 1.0
-- 高度
moveHeight = 25.0
-- 类型
movetp = "hover"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 250
-- 选择缩放
scale = 1.2
-- 基础速度
spd = 310
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"

[n00Q]
_parent = "nhrh"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 颜色值(绿)
green = 255
-- 模型缩放
modelScale = 2.0
-- 颜色值(红)
red = 255
-- 地形设置
tilesets = "*"

[n00R]
_parent = "nepl"
-- 名字
Name = "lv10.龙人巫师"
-- 普通
abilList = "A0A3,A01T"
-- 装甲类型
armor = "Flesh"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 40
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "Units\\Creeps\\DragonSpawnBlue\\DragonSpawnBlue.mdl"
-- 颜色值(绿)
green = 255
-- 等级
level = 3
-- 木材奖励 - 骰子数量
lumberbountydice = 1
-- 木材奖励 - 基础值
lumberbountyplus = 2
-- 木材奖励 - 骰子面数
lumberbountysides = 1
-- 模型缩放
modelScale = 1.15
-- 种族
race = "undead"
-- 颜色值(红)
red = 255
-- 基础速度
spd = 360
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"

[n00S]
_parent = "ninf"
-- 普通
abilList = "A02B"
-- 模型缩放
modelScale = 1.5
-- 种族
race = "undead"
-- 阴影图像 - 高度
shadowH = 300.0
-- 阴影图像 - 宽度
shadowW = 300.0
-- 阴影图像 - X轴偏移
shadowX = 150.0
-- 阴影图像 - Y轴偏移
shadowY = 150.0
-- 分类 - 特殊
special = 1

[n00T]
_parent = "ncnk"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 普通
abilList = "SCae"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型缩放
modelScale = 2.5
-- 种族
race = "unknown"
-- 选择缩放
scale = 2.0
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n00U]
_parent = "ngh2"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArthas.blp"
-- 攻击 1 - 投射物图像
Missileart_1 = ""
-- 攻击 1 - 射弹速率
Missilespeed_1 = 0
-- 名字
Name = "lv7.血色战士"
-- 普通
abilList = "A0A3"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 20
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 8.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "war3mapImported\\Scarlet Commander.mdl"
-- 模型缩放
modelScale = 1.0
-- 类型
movetp = "foot"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 100
-- 动画 - 跑步速度
run = 210.0
-- 基础速度
spd = 200
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = ""
-- 动画 - 行走速度
walk = 210.0
-- 攻击 1 - 武器类型
weapTp1 = "normal"

[n00V]
_parent = "ncen"
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 模型缩放
modelScale = 2.2
-- 种族
race = "unknown"
-- 地形设置
tilesets = "*"

[n00W]
_parent = "nsty"
-- 名字
Name = "食人魔战士"
-- 普通
abilList = "A0A3,A09R"
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "units\\creeps\\OgreMagi\\OgreMagi.mdl"
-- 等级
level = 9
-- 模型缩放
modelScale = 1.8
-- 种族
race = "unknown"
-- 选择缩放
scale = 1.8
-- 阴影图像 - 高度
shadowH = 150.0
-- 阴影图像 - 宽度
shadowW = 150.0
-- 阴影图像 - X轴偏移
shadowX = 75.0
-- 阴影图像 - Y轴偏移
shadowY = 75.0
-- 基础速度
spd = 410
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"

[n00X]
_parent = "nwgt"
-- 生命最大值
HP = 1
-- 名字
Name = "|CffB3FF99X2|r"
-- 售出物品
Sellitems = "I00T,I038,I01L,I020,I024,I042,I02D,I02H,I02M,I02P,I02Q,I02T"
-- 普通
abilList = "A00Y,A00N,A00P,A00Q,A00K,A00U,A00X,A00V,Avul,Apit"
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 210
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 模型文件
file = ".mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 230
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.1
-- 视野范围(夜晚)
nsight = 0
-- 路径纹理
pathTex = ""
-- 单位附加值
points = 0
-- 编队优先权
prio = 0
-- 种族
race = "human"
-- 颜色值(红)
red = 230
-- 修理时间
reptm = 0
-- 选择缩放
scale = -1.0
-- 视野范围(白天)
sight = 0
-- 分类 - 特殊
special = 1
-- 队伍颜色
teamColor = 12
-- 单位类别
type = ""
-- 建筑地面纹理
uberSplat = ""

[n00Y]
_parent = "nsty"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "食人魔酋长"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.5
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.5
-- 模型文件
file = "war3mapImported\\cho'gall.mdl"
-- 射弹偏移 - Z
launchZ = 200.0
-- 等级
level = 9
-- 模型缩放
modelScale = 2.5
-- 种族
race = "unknown"
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 选择缩放
scale = 3.0
-- 缩放投射物
scaleBull = 0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 100.0
-- 阴影图像 - Y轴偏移
shadowY = 100.0
-- 基础速度
spd = 400
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器声音
weapType1 = ""

[n00Z]
_parent = "nubk"
-- 名字
Name = "lv16.恩佐斯的奴仆"
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 基础值
bountyplus = 56
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "Units\\Creeps\\FacelessOne\\FacelessOne.mdl"
-- 等级
level = 9
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 选择缩放
scale = 2.0
-- 基础速度
spd = 430
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"

[n010]
_parent = "nbld"
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.15
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\Banditmissile\\Banditmissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 名字
Name = "lv9.土匪"
-- 普通
abilList = "A0A3"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 32
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "units\\creeps\\BanditSpearThrower\\BanditSpearThrower.mdl"
-- 等级
level = 3
-- 模型缩放
modelScale = 1.2
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 550
-- 基础速度
spd = 340
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 攻击 1 - 武器声音
weapType1 = ""

[n011]
_parent = "ngst"
-- 名字
Name = "lv11.丛林漫步者"
-- 普通
abilList = "A0A3"
-- 装甲类型
armor = "Flesh"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 44
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "units\\creeps\\JungleBeast\\JungleBeast.mdl"
-- 模型缩放
modelScale = 1.25
-- 种族
race = "undead"
-- 阴影图像 - 高度
shadowH = 175.0
-- 阴影图像 - 宽度
shadowW = 175.0
-- 阴影图像 - X轴偏移
shadowX = 80.0
-- 阴影图像 - Y轴偏移
shadowY = 80.0
-- 基础速度
spd = 370
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 允许攻击模式
weapsOn = 1

[n012]
_parent = "nstw"
-- 普通
abilList = "A0A3,Alit"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 模型缩放
modelScale = 1.2
-- 地形设置
tilesets = "*"
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[n013]
_parent = "nstw"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 普通
abilList = "Alit"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\creeps\\ThunderLizardVizier\\ThunderLizardVizier.mdl"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[n014]
_parent = "nadr"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = "A0A3"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 颜色值(绿)
green = 255
-- 模型缩放
modelScale = 1.5
-- 地形设置
tilesets = "*"
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[n015]
_parent = "nsty"
-- 名字
Name = "lv15.食人魔战士"
-- 普通
abilList = "A0A3,A09R"
-- 黄金奖励 - 基础值
bountyplus = 52
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 模型文件
file = "units\\creeps\\Ogre\\Ogre.mdl"
-- 等级
level = 9
-- 模型缩放
modelScale = 1.8
-- 种族
race = "undead"
-- 选择缩放
scale = 1.8
-- 阴影图像 - 高度
shadowH = 150.0
-- 阴影图像 - 宽度
shadowW = 150.0
-- 阴影图像 - X轴偏移
shadowX = 75.0
-- 阴影图像 - Y轴偏移
shadowY = 75.0
-- 基础速度
spd = 410
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"

[n018]
_parent = "nfgt"
-- 生命最大值
HP = 10
-- 名字
Name = "蝗虫群"
-- 普通
abilList = "A01K,Awan,A00J"
-- 主动攻击范围
acquire = 200.0
-- 攻击 1 - 攻击类型
atkType1 = "pierce"
-- 攻击 1 - 动画回复点
backSw1 = 0.0
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.1
-- 死亡时间(秒)
death = 0.1
-- 基础护甲
def = 20.0
-- 护甲类型
defType = "hero"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.0
-- 模型文件
file = "war3mapImported\\DiseaseCloud+.mdl"
-- 可作为中立敌对显示
hostilePal = 1
-- 高度
moveHeight = 100.0
-- 类型
movetp = "fly"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 100
-- 生命回复
regenHP = 0.0
-- 选择缩放
scale = -1.0
-- 阴影图像 - 高度
shadowH = 100.0
-- 阴影图像 - 宽度
shadowW = 100.0
-- 阴影图像 - X轴偏移
shadowX = 50.0
-- 阴影图像 - Y轴偏移
shadowY = 50.0
-- 基础速度
spd = 150
-- 作为目标类型
targType = "air"
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air,hero"
-- 地形设置
tilesets = "*"
-- 转身速度
turnRate = 3.0
-- 单位类别
type = ""
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 攻击 1 - 武器声音
weapType1 = ""

[n019]
_parent = "nrzg"
-- 名字
Name = "野猪人"
-- 普通
abilList = "A0A3"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 等级
level = 9
-- 选择缩放
scale = 1.8
-- 缩放投射物
scaleBull = 0
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"

[n01B]
_parent = "nwiz"
-- 攻击 1 - 射弹弧度
Missilearc_1 = 0.0
-- 攻击 1 - 投射物图像
Missileart_1 = ".mdl"
-- 名字
Name = "lv14.异教徒"
-- 普通
abilList = "A0A3"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 颜色值(蓝)
blue = 200
-- 黄金奖励 - 基础值
bountyplus = 48
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 护甲类型
defType = "large"
-- 模型文件
file = "units\\undead\\Acolyte\\Acolyte.mdl"
-- 颜色值(绿)
green = 100
-- 等级
level = 6
-- 模型缩放
modelScale = 1.75
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 颜色值(红)
red = 100
-- 基础速度
spd = 400
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 攻击 1 - 武器类型
weapTp1 = "normal"
-- 攻击 1 - 武器声音
weapType1 = "WoodHeavyBash"

[n01D]
_parent = "nkot"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv1.狗头人奴隶主"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 255
-- 黄金奖励 - 基础值
bountyplus = 4
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\creeps\\KoboldGeomancer\\KoboldGeomancer.mdl"
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 255
-- 木材消耗
lumbercost = 0
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 选择缩放
scale = 2.0
-- 基础速度
spd = 250
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n01E]
_parent = "nrvs"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv2.民兵队长"
-- 普通
abilList = "A07B"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 8
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\human\\TheCaptain\\TheCaptain.mdl"
-- 颜色值(绿)
green = 255
-- 模型缩放
modelScale = 1.5
-- 类型
movetp = "foot"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 颜色值(红)
red = 255
-- 选择缩放
scale = 1.5
-- 阴影图像 - 高度
shadowH = 150.0
-- 阴影图像 - 宽度
shadowW = 150.0
-- 阴影图像 - X轴偏移
shadowX = 75.0
-- 阴影图像 - Y轴偏移
shadowY = 75.0
-- 基础速度
spd = 250
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n01G]
_parent = "nspd"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\ChimaeraAcidMissile\\ChimaeraAcidMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 名字
Name = "lv4.小蜘蛛"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 基础值
bountyplus = 16
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 2.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\creeps\\SpiderBlack\\SpiderBlack.mdl"
-- 颜色值(绿)
green = 255
-- 模型缩放
modelScale = 1.5
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 550
-- 颜色值(红)
red = 255
-- 基础速度
spd = 250
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器类型
weapTp1 = "missile"

[n01H]
_parent = "nsko"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv3.兽族骷髅"
-- 普通
abilList = "A07B"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 基础值
bountyplus = 12
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "war3mapImported\\returned.mdl"
-- 模型缩放
modelScale = 1.5
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 选择缩放
scale = 1.5
-- 基础速度
spd = 300
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n01I]
_parent = "nano"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv5.屠夫"
-- 普通
abilList = "A07B"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 颜色值(蓝)
blue = 200
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 20
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\undead\\Abomination\\Abomination.mdl"
-- 颜色值(绿)
green = 200
-- 模型缩放
modelScale = 1.75
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 颜色值(红)
red = 200
-- 选择缩放
scale = 1.75
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"

[n01J]
_parent = "nbrg"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\BansheeMissile\\BansheeMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1100
-- 名字
Name = "lv6.幽灵"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 颜色值(蓝)
blue = 255
-- 黄金奖励 - 基础值
bountyplus = 24
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 2.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\undead\\Banshee\\Banshee.mdl"
-- 颜色值(绿)
green = 175
-- 模型缩放
modelScale = 1.35
-- 高度
moveHeight = 50.0
-- 类型
movetp = "hover"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 600
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 转身速度
turnRate = 0.6
-- 单位类别
type = "ancient"

[n01K]
_parent = "nbld"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv9.强盗领主"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 32
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 等级
level = 3
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 100.0
-- 阴影图像 - Y轴偏移
shadowY = 100.0
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"

[n01L]
_parent = "nepl"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv10.龙人首领"
-- 普通
abilList = "A07B"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 40
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "Units\\Creeps\\DragonSpawnPurple\\DragonSpawnPurple.mdl"
-- 颜色值(绿)
green = 255
-- 等级
level = 3
-- 模型缩放
modelScale = 1.75
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 颜色值(红)
red = 255
-- 选择缩放
scale = 2.0
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"

[n01M]
_parent = "nfor"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv8.无面者将军"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 28
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "war3mapImported\\generalvezax.mdl"
-- 等级
level = 3
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 选择缩放
scale = 3.0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 100.0
-- 阴影图像 - Y轴偏移
shadowY = 100.0
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"

[n01O]
_parent = "ngst"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv11.森林之王"
-- 普通
abilList = "A07B"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 44
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\creeps\\Sasquatch\\Sasquatch.mdl"
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"
-- 允许攻击模式
weapsOn = 1

[n01Q]
_parent = "nwiz"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\DemonHunterMissile\\DemonHunterMissile.mdl"
-- 名字
Name = "lv14.邪恶巫师"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.6
-- 黄金奖励 - 基础值
bountyplus = 100
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.5
-- 模型文件
file = "units\\demon\\EredarWarlockPurple\\EredarWarlockPurple.mdl"
-- 等级
level = 6
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 550
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 160.0
-- 阴影图像 - 宽度
shadowW = 160.0
-- 阴影图像 - X轴偏移
shadowX = 80.0
-- 阴影图像 - Y轴偏移
shadowY = 80.0
-- 基础速度
spd = 300
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"

[n01T]
_parent = "nrzg"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "野猪人萨满"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 允许睡眠
canSleep = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\orc\\spiritwalker\\spiritwalker.mdl"
-- 模型缩放
modelScale = 2.0
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 100.0
-- 阴影图像 - Y轴偏移
shadowY = 100.0
-- 基础速度
spd = 350
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n01U]
_parent = "nsty"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv15.食人魔酋长"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.5
-- 黄金奖励 - 基础值
bountyplus = 100
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.5
-- 模型文件
file = "units\\creeps\\OgreOneHeadedArmored\\OgreOneHeadedArmored.mdl"
-- 射弹偏移 - Z
launchZ = 200.0
-- 等级
level = 9
-- 模型缩放
modelScale = 3.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 选择缩放
scale = 3.0
-- 缩放投射物
scaleBull = 0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 100.0
-- 阴影图像 - Y轴偏移
shadowY = 100.0
-- 基础速度
spd = 400
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器声音
weapType1 = ""

[n023]
_parent = "nchr"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNVoidWalker.blp"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv16.黑暗之血"
-- 普通
abilList = "A0M9,A07B"
-- 主动攻击范围
acquire = 2500.0
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\creeps\\SludgeMonster\\SludgeMonster.mdl"
-- 等级
level = 4
-- 模型缩放
modelScale = 3.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 选择缩放
scale = 4.0
-- 基础速度
spd = 250
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 单位类别
type = "ancient"
-- 使用科技
upgrades = ""

[n028]
_parent = "nmyr"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 从属等价物
DependencyOr = ""
-- 生命最大值
HP = 200000
-- 攻击 1 - 投射物图像
Missileart_1 = "tx_147.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2400
-- 名字
Name = "|Cff999999恐惧魔王之影|r"
-- 普通
abilList = "A07I,A000,A07G,A0CG,A0BP,A00Z"
-- 主动攻击范围
acquire = 1000.0
-- 攻击 1 - 攻击类型
atkType1 = "chaos"
-- 攻击 1 - 动画回复点
backSw1 = 0.55
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 0
-- 黄金奖励 - 骰子数量
bountydice = 0
-- 黄金奖励 - 基础值
bountyplus = 0
-- 黄金奖励 - 骰子面数
bountysides = 0
-- 分类 - 战役
campaign = 0
-- 动画 - 魔法施放回复
castbsw = 0.0
-- 动画 - 魔法施放点
castpt = 0.1
-- 攻击 1 - 攻击间隔
cool1 = 0.5
-- 基础护甲
def = 20.0
-- 护甲类型
defType = "hero"
-- 攻击 1 - 基础伤害
dmgplus1 = 200
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.55
-- 模型文件
file = "dreadlord.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 0
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 类型
movetp = "foot"
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 1200
-- 颜色值(红)
red = 0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 2.0
-- 基础速度
spd = 300
-- 攻击 1 - 目标允许
targs1 = "debris,ground,structure,air,ward,item"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 攻击 1 - 武器声音
weapType1 = ""

[n02C]
_parent = "nnzg"
-- 名字
Name = "|Cff7373FFX3|r"
-- 售出物品
Sellitems = "I03W,I014,I02Z,I01V,I016,I03S,I035,I03V,I03Y"
-- 普通
abilList = "Avul,Apit"
-- 颜色值(蓝)
blue = 100
-- 阴影图像(建筑)
buildingShadow = ""
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = ".mdl"
-- 颜色值(绿)
green = 100
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.1
-- 路径纹理
pathTex = ""
-- 种族
race = "human"
-- 建筑地面纹理
uberSplat = ""

[n02E]
_parent = "ncpn"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArthas.blp"
-- 可建造建筑
Builds = ""
-- 名字
Name = "lv19.血色黑暗骑士"
-- 普通
abilList = "A0A3,A00I"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 攻击 1 - 动画回复点
backSw1 = 0.41
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 20
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 分类 - 战役
campaign = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.56
-- 模型文件
file = "war3mapImported\\skeletalknight.mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 0
-- 等级
level = 12
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 颜色值(红)
red = 0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 380.0
-- 选择缩放
scale = 1.5
-- 基础速度
spd = 450
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 单位类别
type = ""
-- 动画 - 行走速度
walk = 380.0
-- 攻击 1 - 武器声音
weapType1 = "MetalHeavySlice"
-- 允许攻击模式
weapsOn = 1

[n02G]
_parent = "nspd"
-- 生命最大值
HP = 300
-- 名字
Name = "小小蜘蛛"
-- 主动攻击范围
acquire = 20000.0
-- 黄金奖励 - 基础值
bountyplus = 2
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 24.0
-- 模型文件
file = "units\\creeps\\SpiderBlack\\SpiderBlack.mdl"
-- 颜色值(绿)
green = 255
-- 木材奖励 - 基础值
lumberbountyplus = 1
-- 种族
race = "undead"
-- 颜色值(红)
red = 255
-- 选择缩放
scale = 1.0
-- 分类 - 特殊
special = 1
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"

[n02H]
_parent = "nwld"
-- 名字
Name = "lv12.恐怖之狼"
-- 普通
abilList = "A0A3,A0AF"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 44
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 等级
level = 9
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 100
-- 基础速度
spd = 380
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"

[n02R]
_parent = "nrvs"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv17.黑龙领主"
-- 普通
abilList = "A07B"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 颜色值(蓝)
blue = 180
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 100
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 10.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "war3mapImported\\dragonspawnpurpleoverlord.mdl"
-- 颜色值(绿)
green = 255
-- 等级
level = 12
-- 模型缩放
modelScale = 1.75
-- 类型
movetp = "foot"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 颜色值(红)
red = 255
-- 选择缩放
scale = 4.0
-- 缩放投射物
scaleBull = 0
-- 阴影图像 - 高度
shadowH = 300.0
-- 阴影图像 - 宽度
shadowW = 300.0
-- 阴影图像 - X轴偏移
shadowX = 150.0
-- 阴影图像 - Y轴偏移
shadowY = 150.0
-- 基础速度
spd = 450
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n02S]
_parent = "nwld"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "lv12.恐怖之狼"
-- 普通
abilList = "A07B,A0AF"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 48
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "units\\orc\\Spiritwolf\\Spiritwolf.mdl"
-- 等级
level = 9
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 选择缩放
scale = 2.0
-- 基础速度
spd = 450
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 单位类别
type = "ancient"

[n02U]
_parent = "nndr"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1200
-- 名字
Name = "lv18.变异虚空龙"
-- 普通
abilList = "A07B"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 100
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 12.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 等级
level = 15
-- 模型缩放
modelScale = 2.25
-- 类型
movetp = "hover"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 150
-- 缩放投射物
scaleBull = 0
-- 基础速度
spd = 450
-- 攻击 1 - 范围影响目标
splashTargs1 = ""
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 转身速度
turnRate = 0.6
-- 单位类别
type = "ancient"
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[n02Y]
_parent = "nwrg"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 名字
Name = "猛犸人"
-- 普通
abilList = "A07B"
-- 装甲类型
armor = "Flesh"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 18.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 模型文件
file = "Units\\Creeps\\Magnataur\\Magnataur.mdl"
-- 等级
level = 12
-- 模型缩放
modelScale = 2.5
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 基础速度
spd = 450
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 地形设置
tilesets = "*"
-- 单位类别
type = "ancient"

[n02Z]
_parent = "nbwm"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 名字
Name = "lv17.黑龙"
-- 普通
abilList = "A0A3"
-- 攻击 1 - 攻击类型
atkType1 = "normal"
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 60
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 碰撞体积
collision = 5.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 死亡时间(秒)
death = 0.5
-- 等级
level = 12
-- 模型缩放
modelScale = 1.5
-- 类型
movetp = "hover"
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 450
-- 缩放投射物
scaleBull = 0
-- 基础速度
spd = 440
-- 攻击 1 - 目标允许
targs1 = "ground,enemies,air"
-- 转身速度
turnRate = 0.6
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[n031]
_parent = "ncop"
-- 名字
Name = "|CffA338EE装备|r"
-- 普通
abilList = "Avul,A017,A019,A01B,A01A,A018,A016"
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = ".mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.1
-- 路径纹理
pathTex = ""
-- 种族
race = "human"
-- 使用点击帮助
useClickHelper = 0

[n039]
_parent = "ncpn"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNArcaneObservatory.blp"
-- 可建造建筑
Builds = ""
-- 攻击 1 - 投射物图像
Missileart_1 = "Abilities\\Weapons\\FireBallMissile\\FireBallMissile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 名字
Name = "|CffA338EE莱斯·霜语|r"
-- 普通
abilList = "A0BH"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.54
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 10
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 分类 - 战役
campaign = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.5
-- 护甲类型
defType = "hero"
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.46
-- 模型文件
file = "war3mapImported\\necromancerstave.mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 射弹偏移 - Z
launchZ = 150.0
-- 等级
level = 3
-- 模型缩放
modelScale = 2.0
-- 种族
race = "undead"
-- 攻击 1 - 攻击范围
rangeN1 = 750
-- 修理时间
reptm = 0
-- 选择缩放
scale = 2.0
-- 基础速度
spd = 180
-- 单位类别
type = ""
-- 使用科技
upgrades = "_"
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 攻击 1 - 武器声音
weapType1 = ""
-- 允许攻击模式
weapsOn = 1

[n04D]
_parent = "ngme"
-- 生命最大值
HP = 1000
-- 名字
Name = "|CffFFFF8C黑铁宝箱|r"
-- 售出物品
Sellitems = ""
-- 普通
abilList = ""
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 阴影图像(建筑)
buildingShadow = ""
-- 死亡时间(秒)
death = 0.1
-- 护甲类型
defType = "none"
-- 模型文件
file = "war3mapImported\\UDArcher.mdl"
-- 是一个建筑
isbldg = 0
-- 模型缩放
modelScale = 0.5
-- 路径纹理
pathTex = ""
-- 种族
race = "human"
-- 选择缩放
scale = -1.0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 100.0
-- 阴影图像 - Y轴偏移
shadowY = 100.0
-- 作为目标类型
targType = "ground"
-- 单位类别
type = "_"
-- 建筑地面纹理
uberSplat = ""
-- 阴影图像(单位)
unitShadow = "Shadow"

[n057]
_parent = "ncop"
-- 名字
Name = "|CffA338EE商城|r"
-- 普通
abilList = "Avul,A05R,A02Y,A0BZ,A0BW,A086,A06H,A08Z,A091"
-- 分类 - 战役
campaign = 0
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = ".mdl"
-- 隐藏小地图显示
hideOnMinimap = 1
-- 模型缩放
modelScale = 0.1
-- 路径纹理
pathTex = ""
-- 种族
race = "human"

[o000]
_parent = "osw3"
-- 名字 - 编辑器后缀
EditorSuffix = ""
-- 名字
Name = "|Cff999999暗影猎犬|r"
-- 普通
abilList = "A08T,A0MY,A0L3,A0BP,A00Z"
-- 种族
race = "human"
-- 分类 - 特殊
special = 0

[u000]
_parent = "ufro"
-- 名字 - 编辑器后缀
EditorSuffix = "BOSS"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 1600
-- 普通
abilList = ""
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 64
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 护甲类型
defType = "divine"
-- 可作为中立敌对显示
hostilePal = 1
-- 模型缩放
modelScale = 2.0
-- 高度
moveHeight = 400.0
-- 种族
race = "creeps"
-- 攻击 1 - 攻击范围
rangeN1 = 550
-- 单位类别
type = "ancient"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 允许攻击模式
weapsOn = 1

[u001]
_parent = "ugho"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 攻击 1 - 全伤害范围
Farea1 = 200
-- 生命最大值
HP = 4
-- 攻击 1 - 中伤害范围
Harea1 = 200
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2100
-- 名字
Name = "|Cff999999黑暗仆从|r"
-- 攻击 1 - 小伤害范围
Qarea1 = 200
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 普通
abilList = "A0L6,A0BP,A00T"
-- 主动攻击范围
acquire = 2000.0
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 100
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 30
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 基础护甲
def = 100.0
-- 护甲类型
defType = "hero"
-- 防御升级奖励
defUp = 10.0
-- 模型文件
file = "units\\undead\\Skeleton\\Skeleton.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 100
-- 可作为中立敌对显示
hostilePal = 1
-- 等级
level = 1
-- 模型缩放
modelScale = 1.45
-- 类型
movetp = ""
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 颜色值(红)
red = 100
-- 生命回复
regenHP = 1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 选择缩放
scale = -1.0
-- 基础速度
spd = 400
-- 攻击 1 - 范围影响目标
splashTargs1 = "ground,enemies,air"
-- 作为目标类型
targType = "air"
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,structure,air"
-- 队伍颜色
teamColor = 12
-- 地形设置
tilesets = "I"
-- 单位类别
type = ""
-- 单位声音设置
unitSound = "Sylvanus"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器声音
weapType1 = "MetalHeavySlice"
-- 允许攻击模式
weapsOn = 1

[u003]
_parent = "ugho"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNSkeletonWarrior.blp"
-- 生命最大值
HP = 1200
-- 名字
Name = "|Cff999999死亡之主|r"
-- 普通
abilList = "A01N,A0QC,A000,A0BP,A0EY"
-- 主动攻击范围
acquire = 2000.0
-- 装甲类型
armor = "Metal"
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.55
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 30
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 动画 - 魔法施放回复
castbsw = 2.0
-- 碰撞体积
collision = 1.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 基础护甲
def = 2000.0
-- 护甲类型
defType = "hero"
-- 防御升级奖励
defUp = 10.0
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.55
-- 模型文件
file = "boneabomination_bc.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 0
-- 可作为中立敌对显示
hostilePal = 1
-- 等级
level = 1
-- 模型缩放
modelScale = 2.0
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 200
-- 颜色值(红)
red = 0
-- 生命回复
regenHP = 0.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 240.0
-- 选择缩放
scale = -1.0
-- 阴影图像 - 高度
shadowH = 200.0
-- 阴影图像 - 宽度
shadowW = 200.0
-- 阴影图像 - X轴偏移
shadowX = 100.0
-- 阴影图像 - Y轴偏移
shadowY = 100.0
-- 基础速度
spd = 400
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,structure,air"
-- 队伍颜色
teamColor = 12
-- 地形设置
tilesets = "I"
-- 单位声音设置
unitSound = "Abomination"
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 240.0
-- 攻击 1 - 武器声音
weapType1 = "MetalHeavySlice"
-- 允许攻击模式
weapsOn = 1

[u004]
_parent = "ugho"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 生命最大值
HP = 100
-- 名字
Name = "|Cff999999憎恶|r"
-- 攻击 1 - 攻击范围缓冲
RngBuff1 = 300.0
-- 普通
abilList = "A08T,A0BP,A00Z"
-- 主动攻击范围
acquire = 2000.0
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 1.17
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 30
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 40.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 基础护甲
def = 300.0
-- 护甲类型
defType = "hero"
-- 防御升级奖励
defUp = 10.0
-- 攻击 1 - 基础伤害
dmgplus1 = 100000
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.5
-- 模型文件
file = "units\\creeps\\Zombie\\Zombie.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 可作为中立敌对显示
hostilePal = 1
-- 等级
level = 1
-- 模型缩放
modelScale = 1.5
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 250
-- 生命回复
regenHP = 0.0
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 210.0
-- 选择缩放
scale = 2.0
-- 阴影图像 - 高度
shadowH = 300.0
-- 阴影图像 - 宽度
shadowW = 300.0
-- 阴影图像 - X轴偏移
shadowX = 150.0
-- 阴影图像 - Y轴偏移
shadowY = 150.0
-- 基础速度
spd = 300
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,structure,air"
-- 队伍颜色
teamColor = 12
-- 地形设置
tilesets = "I"
-- 单位类别
type = "_"
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 210.0
-- 允许攻击模式
weapsOn = 1

[u005]
_parent = "ugho"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 攻击 1 - 全伤害范围
Farea1 = 200
-- 生命最大值
HP = 4
-- 攻击 1 - 中伤害范围
Harea1 = 200
-- 攻击 1 - 中伤害参数
Hfact1 = 1.0
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\az2_az_bluedragonpf_missile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2600
-- 名字
Name = "|Cff999999死亡之女|r"
-- 攻击 1 - 小伤害范围
Qarea1 = 200
-- 攻击 1 - 小伤害参数
Qfact1 = 1.0
-- 普通
abilList = "A0AC,A08T,A0BP,A00Z"
-- 主动攻击范围
acquire = 2500.0
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 建造时间
bldtm = 0
-- 颜色值(蓝)
blue = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 30
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 8.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 基础护甲
def = 100.0
-- 护甲类型
defType = "hero"
-- 防御升级奖励
defUp = 10.0
-- 模型文件
file = "war3mapImported\\valkyr.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 0
-- 可作为中立敌对显示
hostilePal = 1
-- 等级
level = 1
-- 高度
moveHeight = 400.0
-- 类型
movetp = "fly"
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 1200
-- 颜色值(红)
red = 0
-- 生命回复
regenHP = 1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 400.0
-- 选择缩放
scale = -1.0
-- 基础速度
spd = 1
-- 攻击 1 - 范围影响目标
splashTargs1 = "enemies,ground,air"
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,structure,air"
-- 队伍颜色
teamColor = 12
-- 地形设置
tilesets = "I"
-- 单位类别
type = ""
-- 单位声音设置
unitSound = "Assassin"
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 400.0
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 攻击 1 - 武器声音
weapType1 = ""
-- 允许攻击模式
weapsOn = 1

[u00C]
_parent = "ugho"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 生命最大值
HP = 4
-- 攻击 1 - 投射物图像
Missileart_1 = "war3mapImported\\az2_az_bluedragonpf_missile.mdl"
-- 攻击 1 - 射弹速率
Missilespeed_1 = 2400
-- 名字
Name = "|Cff999999黑暗女武神|r"
-- 普通
abilList = "A0AC,A08T,A0BP,A00Z"
-- 主动攻击范围
acquire = 2500.0
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 30
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 8.0
-- 攻击 1 - 攻击间隔
cool1 = 1.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 基础护甲
def = 100.0
-- 护甲类型
defType = "hero"
-- 防御升级奖励
defUp = 10.0
-- 模型文件
file = "war3mapimported\\valkyrblack_squished.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 可作为中立敌对显示
hostilePal = 1
-- 等级
level = 1
-- 高度
moveHeight = 400.0
-- 类型
movetp = "fly"
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 2000
-- 生命回复
regenHP = 1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 动画 - 跑步速度
run = 300.0
-- 选择缩放
scale = -1.0
-- 基础速度
spd = 0
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,structure,air"
-- 队伍颜色
teamColor = 12
-- 地形设置
tilesets = "I"
-- 单位类别
type = ""
-- 单位声音设置
unitSound = "Assassin"
-- 使用科技
upgrades = ""
-- 动画 - 行走速度
walk = 300.0
-- 攻击 1 - 武器类型
weapTp1 = "missile"
-- 攻击 1 - 武器声音
weapType1 = ""
-- 允许攻击模式
weapsOn = 1

[u00D]
_parent = "ugho"
-- 图标 - 游戏界面
Art = "ReplaceableTextures\\CommandButtons\\BTNAnimateDead.blp"
-- 生命最大值
HP = 10
-- 名字
Name = "·马甲·穿刺箭阵"
-- 普通
abilList = "A0MY,A00J"
-- 主动攻击范围
acquire = 2000.0
-- 攻击 1 - 攻击类型
atkType1 = "hero"
-- 攻击 1 - 动画回复点
backSw1 = 0.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 基础值
bountyplus = 30
-- 黄金奖励 - 骰子面数
bountysides = 1
-- 可以逃跑
canFlee = 0
-- 碰撞体积
collision = 0.0
-- 攻击 1 - 攻击间隔
cool1 = 0.25
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.1
-- 基础护甲
def = 30.0
-- 护甲类型
defType = "hero"
-- 防御升级奖励
defUp = 10.0
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.0
-- 模型文件
file = "az_lcdark_w1.mdl"
-- 占用人口
fused = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 可作为中立敌对显示
hostilePal = 1
-- 等级
level = 1
-- 模型缩放
modelScale = 3.0
-- 高度
moveHeight = -300.0
-- 类型
movetp = "fly"
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 600
-- 生命回复
regenHP = -1.0
-- 生命回复类型
regenType = "always"
-- 修理时间
reptm = 0
-- 基础速度
spd = 1
-- 攻击 1 - 目标允许
targs1 = "enemies,ground,structure,air"
-- 队伍颜色
teamColor = 12
-- 地形设置
tilesets = "I"
-- 单位类别
type = "ward"
-- 阴影图像(单位)
unitShadow = ""
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "instant"
-- 允许攻击模式
weapsOn = 1

[u00G]
_parent = "unpl"
-- 生命最大值
HP = 1000000
-- 名字
Name = "|CffFF5C26时间装置|r"
-- 可研究项目
Researches = ""
-- 售出物品
Sellitems = "I046,I043,I008,I01I"
-- 训练单位
Trains = ""
-- 建筑升级
Upgrade = ""
-- 普通
abilList = "A0GW,Avul,Apit"
-- 阴影图像(建筑)
buildingShadow = ""
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "foa_portal_01.mdl"
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 种族
race = "human"
-- 选择缩放
scale = 4.0
-- 队伍颜色
teamColor = 4
-- 单位类别
type = "mechanical,neutral"
-- 建筑地面纹理
uberSplat = "USMA"
-- 使用科技
upgrades = ""

[u01O]
_parent = "uzg1"
-- 生命最大值
HP = 1000000
-- 攻击 1 - 投射物图像
Missileart_1 = ""
-- 名字
Name = "萨隆邪铁栅栏"
-- 需求
Requires = ""
-- 售出物品
Sellitems = "I09R"
-- 普通
abilList = "A0UL,A0US,A0UT,A0UU,A000,A002,A003,A004,A0UQ,A02S,A05Z,AInv"
-- 主动攻击范围
acquire = 1200.0
-- 攻击 1 - 动画回复点
backSw1 = 0.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 骰子面数
bountysides = 1
-- AI放置范围
buffRadius = 0.0
-- 阴影图像(建筑)
buildingShadow = "ShadowHouse"
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.01
-- 攻击 1 - 基础伤害
dmgplus1 = 1
-- 攻击 1 - 动画伤害点
dmgpt1 = 0.0
-- 模型文件
file = "war3mapImported\\ConcreteBarrierCrossSection.mdl"
-- 提供人口
fmade = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 300
-- 修理时间
reptm = 0
-- 放置不允许
requirePlace = ""
-- 选择缩放
scale = 3.0
-- 阴影图像 - 高度
shadowH = 100.0
-- 阴影图像 - 宽度
shadowW = 300.0
-- 阴影图像 - X轴偏移
shadowX = 50.0
-- 阴影图像 - Y轴偏移
shadowY = 50.0
-- 分类 - 特殊
special = 1
-- 作为目标类型
targType = "ground,air"
-- 队伍颜色
teamColor = 0
-- 单位类别
type = "_"
-- 建筑地面纹理
uberSplat = ""
-- 单位声音设置
unitSound = "ArcaneVault"
-- 使用科技
upgrades = ""
-- 攻击 1 - 武器类型
weapTp1 = "instant"

[u024]
_parent = "uzg1"
-- 生命最大值
HP = 6
-- 名字
Name = "被诅咒的符文商店"
-- 需求
Requires = ""
-- 普通
abilList = "Avul"
-- 主动攻击范围
acquire = 1200.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 骰子面数
bountysides = 1
-- AI放置范围
buffRadius = 0.0
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.01
-- 攻击 1 - 基础伤害
dmgplus1 = 0
-- 模型文件
file = "buildings\\undead\\Graveyard\\Graveyard.mdl"
-- 提供人口
fmade = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 颜色值(绿)
green = 200
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 模型缩放
modelScale = 0.75
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 800
-- 颜色值(红)
red = 200
-- 修理时间
reptm = 0
-- 放置不允许
requirePlace = ""
-- 选择缩放
scale = 1.5
-- 队伍颜色
teamColor = 9
-- 单位类别
type = "mechanical"
-- 单位声音设置
unitSound = "ArcaneVault"
-- 使用科技
upgrades = ""
-- 允许攻击模式
weapsOn = 0

[u028]
_parent = "uzg1"
-- 生命最大值
HP = 6
-- 名字
Name = "|CffFFFF8C黄宝石|r"
-- 需求
Requires = ""
-- 售出物品
Sellitems = [=[
S011,S012,S013,S014
,S015
,S016
,S017
,S018
,S019
,I02K]=]
-- 普通
abilList = "A0GW,Avul,Apit"
-- 主动攻击范围
acquire = 1200.0
-- 建造时间
bldtm = 0
-- 黄金奖励 - 骰子数量
bountydice = 1
-- 黄金奖励 - 骰子面数
bountysides = 1
-- AI放置范围
buffRadius = 0.0
-- 阴影图像(建筑)
buildingShadow = ""
-- 碰撞体积
collision = 0.0
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 死亡时间(秒)
death = 0.01
-- 攻击 1 - 基础伤害
dmgplus1 = 0
-- 模型文件
file = "war3mapImported\\item_orb_defense.mdl"
-- 提供人口
fmade = 0
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 模型缩放
modelScale = 2.0
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 种族
race = "human"
-- 攻击 1 - 攻击范围
rangeN1 = 800
-- 修理时间
reptm = 0
-- 放置不允许
requirePlace = ""
-- 选择缩放
scale = 1.5
-- 队伍颜色
teamColor = 0
-- 单位类别
type = "mechanical"
-- 单位声音设置
unitSound = "ArcaneVault"
-- 使用科技
upgrades = ""
-- 允许攻击模式
weapsOn = 0

[u034]
_parent = "ugol"
-- 名字 - 编辑器后缀
EditorSuffix = "1"
-- 生命最大值
HP = 20000
-- 名字
Name = "|CffFFFF8C高级商店|r"
-- 售出物品
Sellitems = "I02E,I03R"
-- 普通
abilList = "Aneu,Asid,Avul,Asud,Apit"
-- 主动攻击范围
acquire = 1200.0
-- 建造时间
bldtm = 0
-- AI放置类型
buffType = ""
-- 队伍颜色 - 允许自定义
customTeamColor = 1
-- 模型文件
file = "war3mapimported\\banhammerpedestal.mdl"
-- 修理黄金消耗
goldRep = 0
-- 黄金消耗
goldcost = 0
-- 隐藏小地图显示
hideOnMinimap = 1
-- 修理木材消耗
lumberRep = 0
-- 木材消耗
lumbercost = 0
-- 魔法初始数量
mana0 = 20
-- 魔法最大值
manaN = 1000
-- 路径纹理
pathTex = ""
-- 放置要求
preventPlace = ""
-- 种族
race = "human"
-- 魔法回复
regenMana = 1000.0
-- 修理时间
reptm = 0
-- 选择缩放
scale = 1.0
-- 分类 - 特殊
special = 1
-- 队伍颜色
teamColor = 12
-- 单位类别
type = "mechanical"
-- 建筑地面纹理
uberSplat = "USMA"
-- 使用科技
upgrades = ""
