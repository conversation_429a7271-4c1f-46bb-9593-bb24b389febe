# JASS开发环境搭建指南

## 项目概述
- **项目路径**: `e:\WORLD EDIT\hayx\map\`
- **主要文件**: `war3map.j` (编译后的JASS代码)
- **开发工具**: YDWE (You Dao World Editor)
- **编程方式**: GUI + 自定义JASS

## 你的编程风格特点

### 1. 变量命名规范
```jass
// 全局变量使用udg_前缀
integer udg_SL = 0                    // 数量
integer udg_SJ/*时间*/ = 0            // 时间，使用中文注释
unit array udg_YX                     // 英雄数组
unit array udg_zz/*助手*/             // 助手单位数组
integer array udg_CS/*通关次数*/      // 通关次数数组
```

### 2. YDWE API使用模式
```jass
// 局部变量管理
YDLocalInitialize()                   // 初始化局部变量系统
call YDLocal1Set(unit, "a", GetAttacker())  // 设置局部变量
call YDLocal1Get(unit, "a")          // 获取局部变量
call YDLocal1Release()               // 释放局部变量

// 用户数据存储
call YDUserDataSet(unit, unit, "属性名", real, value)
call YDUserDataGet(unit, unit, "属性名", real)

// DZ框架UI系统
call DzCreateFrame("框架名", 父框架, 0)
call DzFrameSetSize(框架, 宽度, 高度)
call DzFrameSetPoint(框架, 锚点, 相对框架, 相对锚点, x偏移, y偏移)
```

### 3. 触发器结构模式
```jass
// 标准触发器结构
function Trig_技能名Conditions takes nothing returns boolean
    // 触发条件
    return (条件1) and (条件2) and (条件3)
endfunction

function Trig_技能名Actions takes nothing returns nothing
    YDLocalInitialize()
    // 技能逻辑
    call YDLocal1Release()
endfunction

function InitTrig_技能名 takes nothing returns nothing
    set gg_trg_技能名 = CreateTrigger()
    call TriggerRegisterPlayerUnitEventSimple(gg_trg_技能名, Player(0), EVENT_PLAYER_UNIT_ATTACKED)
    call TriggerAddCondition(gg_trg_技能名, Condition(function Trig_技能名Conditions))
    call TriggerAddAction(gg_trg_技能名, function Trig_技能名Actions)
endfunction
```

## 关键系统分析

### 1. 暗影冲击技能系统 (anyingchongji)
**触发条件**:
- 16%概率触发
- 攻击者必须有"暗影冲击"属性 >= 1.0
- 目标必须是敌人且不是建筑
- 攻击者必须是英雄

**技能效果**:
- 创建飞行特效 "az_feiyan.mdl"
- 沿直线移动1280距离，每0.015秒移动28距离
- 范围200伤害检测
- 2级以上触发额外9方向冰霜攻击

### 2. UI界面系统
- 使用DZ框架创建自定义界面
- 坐标系统: (像素值 / 1280.00) 进行标准化
- 支持按钮回调、纹理设置、文本显示

### 3. 数据存储系统
- 使用YDUserData系统存储单位属性
- 支持boolean、real、integer、string等类型
- 可以按player、unit、item等分类存储

## 开发环境设置

### 1. 推荐的开发流程
1. **在GUI中设计基本逻辑** - 使用YDWE的可视化编辑器
2. **导出J代码进行分析** - 查看编译后的JASS代码
3. **使用模板创建新技能** - 基于现有技能模式
4. **在YDWE中测试功能** - 实时测试和调试
5. **记录和文档化** - 维护代码文档

### 2. 调试技巧
```jass
// 调试输出
call DisplayTimedTextToPlayer(GetLocalPlayer(), 0, 0, 5.00, "调试信息: " + I2S(变量值))

// 单位选择调试
call SelectUnitForPlayerSingle(目标单位, GetLocalPlayer())

// 特效调试
call AddSpecialEffectTarget("特效路径", 目标单位, "挂接点")

// 条件调试
if (条件) then
    call DisplayTimedTextToPlayer(GetLocalPlayer(), 0, 0, 3.00, "条件满足")
else
    call DisplayTimedTextToPlayer(GetLocalPlayer(), 0, 0, 3.00, "条件不满足")
endif
```

### 3. 常用YDWE函数参考
```jass
// 时间相关
call YDWETimerDestroyEffect(持续时间, 特效)  // 定时销毁特效
call TimerStart(计时器, 间隔, 是否重复, 回调函数)

// 角度和距离
call YDWEAngleBetweenUnits(单位1, 单位2)    // 计算两单位间角度
call PolarProjectionBJ(起点, 距离, 角度)     // 极坐标投影

// 特效操作
call EXEffectMatRotateZ(特效, 角度)         // 旋转特效Z轴
call YDWESetEffectLoc(特效, 位置)           // 设置特效位置
```

## 技能开发模板

### 基础攻击触发技能模板
```jass
function Trig_新技能Conditions takes nothing returns boolean
    return ((GetRandomReal(1.00, 100.00) <= 触发概率)) and
           ((YDUserDataGet(unit, GetAttacker(), "技能名", real) >= 1.00)) and
           ((IsUnitEnemy(GetTriggerUnit(), GetOwningPlayer(GetAttacker())) == true)) and
           ((IsUnitType(GetTriggerUnit(), UNIT_TYPE_STRUCTURE) == false)) and
           ((IsUnitType(GetAttacker(), UNIT_TYPE_HERO) == true))
endfunction

function Trig_新技能Actions takes nothing returns nothing
    YDLocalInitialize()
    call YDLocal1Set(unit, "attacker", GetAttacker())
    call YDLocal1Set(unit, "target", GetTriggerUnit())

    // 在这里添加技能效果

    call YDLocal1Release()
endfunction

function InitTrig_新技能 takes nothing returns nothing
    set gg_trg_新技能 = CreateTrigger()
    call TriggerRegisterPlayerUnitEventSimple(gg_trg_新技能, Player(5), EVENT_PLAYER_UNIT_ATTACKED)
    // 为所有玩家注册事件...
    call TriggerAddCondition(gg_trg_新技能, Condition(function Trig_新技能Conditions))
    call TriggerAddAction(gg_trg_新技能, function Trig_新技能Actions)
endfunction
```