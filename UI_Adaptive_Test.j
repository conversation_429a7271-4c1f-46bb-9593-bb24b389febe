//===========================================================================
// UI自适应系统测试和集成文件
//===========================================================================

globals
    trigger gg_trg_UIAdaptiveTest = null
    trigger gg_trg_ToggleUI = null
    trigger gg_trg_ResolutionDetect = null
    
    // 测试用变量
    boolean TestMode = true
    integer TestCounter = 0
endglobals

//===========================================================================
// 分辨率检测和自动适配
//===========================================================================
function AutoDetectAndAdapt takes nothing returns nothing
    local real width = DzGetClientWidth()
    local real height = DzGetClientHeight()
    local real ratio = width / height
    local string resolutionInfo
    
    // 构建分辨率信息字符串
    set resolutionInfo = "分辨率: " + I2S(R2I(width)) + "x" + I2S(R2I(height)) + " (比例: " + R2S(ratio) + ")"
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB[检测] " + resolutionInfo + "|r")
    
    // 根据比例给出建议
    if ratio > 1.8 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[建议] 超宽屏检测到，将使用黑边适配模式|r")
    elseif ratio > 1.6 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[建议] 宽屏检测到，将使用标准适配模式|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB[建议] 标准比例，使用原生适配|r")
    endif
    
    // 自动初始化UI适配
    call InitAdaptiveUI()
endfunction

//===========================================================================
// 键盘快捷键处理
//===========================================================================
function HandleKeyboardShortcuts takes nothing returns nothing
    local string pressedKey = DzGetTriggerKey()
    
    if pressedKey == "OSKEY_F1" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700[快捷键] F1 - 切换自适应UI|r")
        call ToggleAdaptiveUI()
    elseif pressedKey == "OSKEY_F2" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700[快捷键] F2 - 重新检测分辨率|r")
        call AutoDetectAndAdapt()
    elseif pressedKey == "OSKEY_F3" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700[快捷键] F3 - 显示帮助信息|r")
        call ShowHelpInfo()
    endif
endfunction

//===========================================================================
// 显示帮助信息
//===========================================================================
function ShowHelpInfo takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== UI自适应系统帮助 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700F1|r - 切换自适应UI显示/隐藏")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700F2|r - 重新检测屏幕分辨率")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700F3|r - 显示此帮助信息")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700ESC|r - 切换UI显示")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB当前状态: " + (IsUIAdapted ? "已适配" : "未适配") + "|r")
endfunction

//===========================================================================
// 性能测试函数
//===========================================================================
function PerformanceTest takes nothing returns nothing
    local real startTime = GetGameTimeOfDay()
    local integer i = 0
    local real endTime
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[性能] 开始UI性能测试...|r")
    
    // 模拟UI操作
    loop
        exitwhen i >= 100
        call DzFrameShow(AdaptiveContainer, true)
        call DzFrameShow(AdaptiveContainer, false)
        set i = i + 1
    endloop
    
    call DzFrameShow(AdaptiveContainer, true)
    set endTime = GetGameTimeOfDay()
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[性能] 测试完成，耗时: " + R2S(endTime - startTime) + "秒|r")
endfunction

//===========================================================================
// 兼容性测试
//===========================================================================
function CompatibilityTest takes nothing returns nothing
    local boolean dzApiAvailable = false
    local boolean japiAvailable = false
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 兼容性检测 ===|r")
    
    // 检测DzAPI
    if DzGetGameUI() != 0 then
        set dzApiAvailable = true
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ DzAPI 可用|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ DzAPI 不可用|r")
    endif
    
    // 检测基础功能
    if dzApiAvailable then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 基础UI功能正常|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 可以开始UI适配|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ 缺少必要API，无法进行UI适配|r")
        return
    endif
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 兼容性检测完成 ===|r")
endfunction

//===========================================================================
// 错误处理和恢复
//===========================================================================
function HandleUIError takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] UI系统出现问题，尝试恢复...|r")
    
    // 重置UI状态
    set IsUIAdapted = false
    
    // 尝试重新初始化
    call TriggerSleepAction(1.0)
    call InitAdaptiveUI()
    
    if IsUIAdapted then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[恢复] UI系统已成功恢复|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] UI系统恢复失败，请重启地图|r")
    endif
endfunction

//===========================================================================
// 触发器初始化函数
//===========================================================================
function InitTrig_UIAdaptiveTest takes nothing returns nothing
    // 创建主测试触发器
    set gg_trg_UIAdaptiveTest = CreateTrigger()
    call TriggerRegisterPlayerChatEvent(gg_trg_UIAdaptiveTest, Player(0), "-ui", true)
    call TriggerAddAction(gg_trg_UIAdaptiveTest, function AutoDetectAndAdapt)
    
    // 创建切换UI触发器
    set gg_trg_ToggleUI = CreateTrigger()
    call TriggerRegisterPlayerChatEvent(gg_trg_ToggleUI, Player(0), "-toggle", true)
    call TriggerAddAction(gg_trg_ToggleUI, function ToggleAdaptiveUI)
    
    // 创建分辨率检测触发器
    set gg_trg_ResolutionDetect = CreateTrigger()
    call TriggerRegisterPlayerChatEvent(gg_trg_ResolutionDetect, Player(0), "-detect", true)
    call TriggerAddAction(gg_trg_ResolutionDetect, function AutoDetectAndAdapt)
    
    // 注册键盘事件
    call DzTriggerRegisterKeyEvent(gg_trg_ToggleUI, "OSKEY_F1", 1, false)
    call DzTriggerRegisterKeyEvent(gg_trg_ResolutionDetect, "OSKEY_F2", 1, false)
    call TriggerAddAction(gg_trg_ToggleUI, function HandleKeyboardShortcuts)
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== UI自适应系统已加载 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -ui 开始自适应|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -toggle 切换显示|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -detect 重新检测|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700按 F1/F2/F3 使用快捷键|r")
endfunction

//===========================================================================
// 自动启动函数（可选）
//===========================================================================
function AutoStartAdaptiveUI takes nothing returns nothing
    call TriggerSleepAction(2.0)  // 等待游戏完全加载
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB[自动] 开始自动适配UI...|r")
    
    // 执行兼容性检测
    call CompatibilityTest()
    
    // 自动检测并适配
    call AutoDetectAndAdapt()
    
    // 显示帮助信息
    call ShowHelpInfo()
endfunction

//===========================================================================
// 主函数调用
//===========================================================================
function main takes nothing returns nothing
    call InitTrig_UIAdaptiveTest()
    
    // 如果需要自动启动，取消下面的注释
    // call AutoStartAdaptiveUI()
endfunction
