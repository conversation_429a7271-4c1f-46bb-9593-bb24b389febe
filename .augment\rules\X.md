---
type: "manual"
---

<PERSON><PERSON><PERSON> (Keep It Simple, Stupid)
Encourages <PERSON> to write straightforward, uncomplicated solutions

Avoids over-engineering and unnecessary complexity

Results in more readable and maintainable code

YAGNI (You Aren't Gonna Need It)
Prevents <PERSON> from adding speculative features

Focuses on implementing only what's currently needed

Reduces code bloat and maintenance overhead

SOLID Principles
Single Responsibility Principle

Open-Closed Principle

Liskov Substitution Principle

Interface Segregation Principle

Dependency Inversion Principle