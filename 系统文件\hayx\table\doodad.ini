[AObo]
_parent = "AObo"
-- 模型文件
file = "war3mapImported\\dragonspawnpurpleoverlord.mdl"

[AObr]
_parent = "AObr"
-- 模型文件
file = "war3mapImported\\virtualunitbirth_byepsilon.mdl"

[AOgs]
_parent = "AOgs"
-- 模型文件
file = "az_crixalis(1)_c5-8.mdl"
-- 路径纹理
pathTex = ""

[ASwt]
_parent = "ASwt"
-- 默认比例
defScale = 0.6
-- 路径纹理
pathTex = ""

[BOct]
_parent = "BOct"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "Doodads\\Northrend\\Structures\\AncientZiggurat\\AncientZiggurat1.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 0.1
-- 颜色值(绿)
vertG = {200, 200, 200}
-- 颜色值(红)
vertR = {150, 150, 150}

[BOth]
_parent = "BOth"
-- 模型文件
file = "az_aurarune3_4.mdl"

[BRfs]
_parent = "BRfs"
-- 颜色值(蓝)
vertB = {150, 150}
-- 颜色值(绿)
vertG = {150, 150}

[CObo]
_parent = "CObo"
-- 模型文件
file = "war3mapimported\\darklightningnova.mdl"

[CPms]
_parent = "CPms"
-- 最大比例
maxScale = 5.0

[CSra]
_parent = "CSra"
-- 最大比例
maxScale = 3.0

[DOab]
_parent = "DOab"
-- 模型文件
file = "war3mapImported\\war3ake.com - bookshelf.mdl"
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 200
-- 颜色值(红)
vertR = 200

[DOas]
_parent = "DOas"
-- 模型文件
file = "war3mapImported\\shadowymissileofevildoomv2.mdl"
-- 路径纹理
pathTex = ""

[DObk]
_parent = "DObk"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapimported\\ephemeral cut purple.mdl"
-- 最小比例
minScale = 1.0
-- 悬崖上可放置
onCliffs = 1

[DObw]
_parent = "DObw"
-- 模型文件
file = "war3mapImported\\war3ake.com - bookshelf.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 200
-- 颜色值(红)
vertR = 200

[DOcr]
_parent = "DOcr"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "Abilities\\Spells\\Human\\MassTeleport\\MassTeleportTo.mdl"

[DOim]
_parent = "DOim"
-- 模型文件
file = "icetornado.mdl"

[DOjp]
_parent = "DOjp"
-- 模型文件
file = "sacredshield.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[DOlc]
_parent = "DOlc"
-- 颜色值(蓝)
vertB = {0, 0, 0, 0}

[DOsv]
_parent = "DOsv"
-- 模型文件
file = "war3mapImported\\firecrackerarrow.mdl"
-- 颜色值(蓝)
vertB = {200, 200, 200, 200}
-- 颜色值(绿)
vertG = {150, 150, 150, 150}
-- 颜色值(红)
vertR = {100, 100, 100, 100}

[DOsw]
_parent = "DOsw"
-- 模型文件
file = "war3mapImported\\az_potm(1)_r2.mdl"

[DOtt]
_parent = "DOtt"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[DSah]
_parent = "DSah"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[DSp0]
_parent = "DSp0"
-- 模型文件
file = "war3mapImported\\ss50.mdl"

[GRst]
_parent = "GRst"
-- 颜色值(蓝)
vertB = {0, 0, 0, 0}
-- 颜色值(绿)
vertG = {0, 0, 0, 0}
-- 颜色值(红)
vertR = {0, 0, 0, 0}

[GSah]
_parent = "GSah"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[GSp0]
_parent = "GSp0"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[GSp9]
_parent = "GSp9"
-- X轴最大旋转角度(度数)
maxPitch = -90.0
-- Y轴最大旋转角度(度数)
maxRoll = -90.0
-- 路径纹理
pathTex = ""

[IOch]
_parent = "IOch"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "buildings\\other\\CircleOfPower\\CircleOfPower.mdl"
-- 路径纹理
pathTex = ""

[IOic]
_parent = "IOic"
-- 默认比例
defScale = 1.2
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.0
-- 颜色值(绿)
vertG = 230
-- 颜色值(红)
vertR = 230

[IOob]
_parent = "IOob"
-- 最大比例
maxScale = 2.0

[IOpr]
_parent = "IOpr"
-- 默认比例
defScale = 1.75
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[IOsl]
_parent = "IOsl"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\Spell Marker Blue.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[IOsm]
_parent = "IOsm"
-- 模型文件
file = "war3mapImported\\DiseaseCloud+.mdl"

[IOss]
_parent = "IOss"
-- 模型文件
file = "az_lina(2)_t2_blast.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 颜色值(绿)
vertG = 200
-- 颜色值(红)
vertR = 150

[IOst]
_parent = "IOst"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[IRgc]
_parent = "IRgc"
-- 路径纹理
pathTex = ""

[IRrk]
_parent = "IRrk"
-- 最大比例
maxScale = 3.0

[ISrb]
_parent = "ISrb"
-- 颜色值(绿)
vertG = {230, 230, 230}
-- 颜色值(红)
vertR = {230, 230, 230}

[ISsr]
_parent = "ISsr"
-- 默认比例
defScale = 1.5
-- 路径纹理
pathTex = ""

[JOgr]
_parent = "JOgr"
-- 默认比例
defScale = 1.0

[KOdr]
_parent = "KOdr"
-- 默认比例
defScale = 6.0
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {0, 0, 0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0, 0, 0}

[LObz]
_parent = "LObz"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[LOcb]
_parent = "LOcb"
-- 模型文件
file = "Abilities\\Weapons\\WardenMissile\\WardenMissile.mdl"

[LOgr]
_parent = "LOgr"
-- 模型文件
file = "Doodads\\Cityscape\\Props\\CityGrave\\CityGrave3.mdl"

[LOhp]
_parent = "LOhp"
-- 模型文件
file = "az-ziwu-yumao.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[LOlp]
_parent = "LOlp"
-- 默认比例
defScale = 0.75
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[LOsk]
_parent = "LOsk"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[LOsm]
_parent = "LOsm"
-- 默认比例
defScale = 4.0

[LOwp]
_parent = "LOwp"
-- 模型文件
file = "war3mapImported\\ArcaneBurst.mdl"

[LOwr]
_parent = "LOwr"
-- 模型文件
file = "xuanfeng2.mdl"

[LOxx]
_parent = "LOxx"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "az_crixalis(1)_c5-8.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[LRrk]
_parent = "LRrk"
-- 默认比例
defScale = 0.75
-- 最大比例
maxScale = 4.0

[LSba]
_parent = "LSba"
-- 默认比例
defScale = 1.5
-- 路径纹理
pathTex = ""

[LSsb]
_parent = "LSsb"
-- 默认比例
defScale = 1.5
-- 路径纹理
pathTex = ""

[LSsf]
_parent = "LSsf"
-- 默认比例
defScale = 1.25

[LSsi]
_parent = "LSsi"
-- 默认比例
defScale = 1.25
-- 路径纹理
pathTex = ""

[LSst]
_parent = "LSst"
-- 默认比例
defScale = 2.0

[LSwm]
_parent = "LSwm"
-- 默认比例
defScale = 2.0
-- 路径纹理
pathTex = ""

[LWw0]
_parent = "LWw0"
-- X轴最大旋转角度(度数)
maxPitch = -10.0
-- Y轴最大旋转角度(度数)
maxRoll = -30.0
-- 最大比例
maxScale = 10.0
-- 最小比例
minScale = 1.0
-- 颜色值(蓝)
vertB = 200
-- 颜色值(红)
vertR = 150

[NOal]
_parent = "NOal"
-- 模型文件
file = "war3mapImported\\CampaignArchwayHalf.mdl"
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 125
-- 颜色值(绿)
vertG = 100
-- 颜色值(红)
vertR = 100

[NObk]
_parent = "NObk"
-- 模型文件
file = "war3mapImported\\captain lordaeron_hq_tc_unit.mdl"

[NObo]
_parent = "NObo"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.0

[NOfl]
_parent = "NOfl"
-- 模型文件
file = "war3mapImported\\ss50.mdl"
-- 路径纹理
pathTex = ""

[NOfp]
_parent = "NOfp"
-- 默认比例
defScale = 0.7

[NOok]
_parent = "NOok"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 1.0
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""

[NOtb]
_parent = "NOtb"
-- 默认比例
defScale = 1.5

[NRic]
_parent = "NRic"
-- 路径纹理
pathTex = ""

[NRwr]
_parent = "NRwr"
-- 默认比例
defScale = 3.0
-- 模型文件
file = "war3mapImported\\ice shard.mdl"
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {150, 150, 150, 150, 150, 150, 150, 150}
-- 颜色值(绿)
vertG = {150, 150, 150, 150, 150, 150, 150, 150}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150, 150, 150, 150}

[NSct]
_parent = "NSct"
-- 默认比例
defScale = 1.75
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 180
-- 颜色值(红)
vertR = 160

[NWsd]
_parent = "NWsd"
-- 默认比例
defScale = 2.0

[NWsp]
_parent = "NWsp"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapimported\\vrykuldrakkarshipptimized.mdl"
-- 路径纹理
pathTex = ""

[OOgr]
_parent = "OOgr"
-- 默认比例
defScale = 1.2
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[OOsk]
_parent = "OOsk"
-- 模型文件
file = "war3mapImported\\twilightincineratespecial.mdl"

[OPop]
_parent = "OPop"
-- 模型文件
file = "Objects\\Spawnmodels\\Demon\\InfernalMeteor\\InfernalMeteor.mdl"

[OZsp]
_parent = "OZsp"
-- 最小比例
minScale = 0.5

[VOal]
_parent = "VOal"
-- 模型文件
file = "war3mapImported\\xjcsmppbyq.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[VOas]
_parent = "VOas"
-- 模型文件
file = "az_z024.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[VOfs]
_parent = "VOfs"
-- 路径纹理
pathTex = ""

[VSvb]
_parent = "VSvb"
-- 默认比例
defScale = 0.75
-- 最大比例
maxScale = 1.75
-- 最小比例
minScale = 1.0
-- 颜色值(蓝)
vertB = {220, 220, 220}
-- 颜色值(绿)
vertG = {220, 220, 220}

[XOcl]
_parent = "XOcl"
-- 默认比例
defScale = 1.5
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 240
-- 颜色值(绿)
vertG = 180
-- 颜色值(红)
vertR = 180

[XOcs]
_parent = "XOcs"
-- 最大比例
maxScale = 3.0

[XOmr]
_parent = "XOmr"
-- 可通行
walkable = 1

[YObb]
_parent = "YObb"
-- 默认比例
defScale = 2.0

[YObg]
_parent = "YObg"
-- 模型文件
file = "war3mapimported\\radiance crimson.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0

[YOec]
_parent = "YOec"
-- 默认比例
defScale = 10.0
-- 模型文件
file = "war3mapImported\\az_tx1_1.mdl"

[YOgr]
_parent = "YOgr"
-- 模型文件
file = "Doodads\\Cityscape\\Props\\CityGrave\\CityGrave1.mdl"

[YOks]
_parent = "YOks"
-- 默认比例
defScale = 1.0
-- 模型文件
file = "war3mapImported\\SummonerAraj.mdl"
-- X轴最大旋转角度(度数)
maxPitch = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[YOlp]
_parent = "YOlp"
-- 默认比例
defScale = 0.75

[YOob]
_parent = "YOob"
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[YOsa]
_parent = "YOsa"
-- 模型文件
file = "heal green.mdl"
-- X轴最大旋转角度(度数)
maxPitch = -270.0
-- Y轴最大旋转角度(度数)
maxRoll = -180.0

[YOth]
_parent = "YOth"
-- 模型文件
file = "war3mapImported\\AlternateBansheeQueen.mdl"

[YOwa]
_parent = "YOwa"
-- 模型文件
file = "war3mapimported\\arcaneburst.mdl"

[YPfs]
_parent = "YPfs"
-- 最小比例
minScale = 1.2

[YS00]
_parent = "YS00"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS01]
_parent = "YS01"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS02]
_parent = "YS02"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS03]
_parent = "YS03"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS04]
_parent = "YS04"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS05]
_parent = "YS05"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS06]
_parent = "YS06"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS07]
_parent = "YS07"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS08]
_parent = "YS08"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS09]
_parent = "YS09"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS10]
_parent = "YS10"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS11]
_parent = "YS11"
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""

[YS12]
_parent = "YS12"
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 200

[YS13]
_parent = "YS13"
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 200

[YS14]
_parent = "YS14"
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 200

[YS15]
_parent = "YS15"
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 200

[YSaw]
_parent = "YSaw"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[YSca]
_parent = "YSca"
-- 路径纹理
pathTex = ""

[YSll]
_parent = "YSll"
-- 颜色值(蓝)
vertB = 230
-- 颜色值(绿)
vertG = 230

[YSw2]
_parent = "YSw2"
-- 颜色值(蓝)
vertB = 100
-- 颜色值(绿)
vertG = 70
-- 颜色值(红)
vertR = 0

[ZOba]
_parent = "ZOba"
-- 模型文件
file = "war3mapimported\\divineshieldtarget.mdl"
-- 路径纹理
pathTex = ""

[ZObz]
_parent = "ZObz"
-- 模型文件
file = "Doodads\\Ruins\\Props\\Brazier\\Brazier0.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[ZOdt]
_parent = "ZOdt"
-- 默认比例
defScale = 1.0

[ZOfo]
_parent = "ZOfo"
-- 路径纹理
pathTex = ""

[ZOls]
_parent = "ZOls"
-- 模型文件
file = "whine.mdl"

[ZOob]
_parent = "ZOob"
-- 模型文件
file = "doodads\\cinematic\\IcecrownObelisk\\IcecrownObelisk.mdl"

[ZOrb]
_parent = "ZOrb"
-- 默认比例
defScale = 1.0
-- 模型文件
file = "blood ritual.mdl"
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[ZOrc]
_parent = "ZOrc"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\[missile]whiteelfspellthiefmissile.mdl"

[ZOrp]
_parent = "ZOrp"
-- 模型文件
file = "war3mapImported\\CampaignArchwayHalf.mdl"
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {150, 150, 150, 150, 150}
-- 颜色值(绿)
vertG = {120, 120, 120, 120, 120}
-- 颜色值(红)
vertR = {120, 120, 120, 120, 120}

[ZOst]
_parent = "ZOst"
-- 模型文件
file = "war3mapimported\\divineshieldtarget.mdl"

[ZOvr]
_parent = "ZOvr"
-- 模型文件
file = "war3mapImported\\firebrand shot yellow.mdl"

[ZPsh]
_parent = "ZPsh"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "war3mapImported\\jx3_zs_cao9.mdl"

[ZWca]
_parent = "ZWca"
-- 默认比例
defScale = 2.0

[ZZys]
_parent = "ZZys"
-- 默认比例
defScale = 2.0

[D000]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川1"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\HD_SkullonStick3.mdl"
-- 路径纹理
pathTex = ""

[D001]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川3"
-- 模型文件
file = "war3mapImported\\HD_HayCart.mdl"
-- 路径纹理
pathTex = ""

[D002]
_parent = "VOal"
-- 模型文件
file = "b zhuangshi.mdl"
-- 最大比例
maxScale = 7.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D003]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川"
-- 模型文件
file = "war3mapImported\\HD_CityGrave1.mdl"
-- 路径纹理
pathTex = ""

[D004]
_parent = "YOsa"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\xiaocao.mdl"
-- 颜色值(蓝)
vertB = 0
-- 颜色值(绿)
vertG = 200

[D005]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川2"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\HD_HayCartbroken.mdl"
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {150, 150, 150, 150, 150}
-- 颜色值(绿)
vertG = {150, 150, 150, 150, 150}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150}

[D006]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川4"
-- 模型文件
file = "war3mapimported\\hd_grave.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[D007]
_parent = "ZOrp"
-- 名字
Name = "龙骨"
-- 默认比例
defScale = 8.0
-- 模型文件
file = "war3mapImported\\longguhuangye.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {100, 100, 100, 100, 100}
-- 颜色值(绿)
vertG = {100, 100, 100, 100, 100}
-- 颜色值(红)
vertR = {100, 100, 100, 100, 100}

[D008]
_parent = "ZOrp"
-- 名字
Name = "窗户光照"
-- 模型文件
file = "war3mapImported\\[ake]war3ake.com - 0586022696199289020768386.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {0, 255, 255, 255, 255}

[D009]
_parent = "IOst"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[D00A]
_parent = "ZOrp"
-- 名字
Name = "霜"
-- 模型文件
file = "war3mapImported\\[spectacle][nature]fog.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 0.2
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D00B]
_parent = "ZOrp"
-- 名字
Name = "冰洞2"
-- 默认比例
defScale = 2.5
-- 模型文件
file = "forsaken_hall.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D00C]
_parent = "ZOrp"
-- 名字
Name = "冰洞1"
-- 默认比例
defScale = 1.25
-- 模型文件
file = "war3mapImported\\G2_weiqiang3.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""

[D00D]
_parent = "ZOrp"
-- 名字
Name = "冰洞3"
-- 默认比例
defScale = 5.0
-- 模型文件
file = "war3mapImported\\IronForgeExterior07.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D00E]
_parent = "ZOrp"
-- 名字
Name = "冰洞4"
-- 默认比例
defScale = 5.0
-- 模型文件
file = "war3mapImported\\IronForgeExterior06.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D00F]
_parent = "ZOrp"
-- 名字
Name = "冰洞5"
-- 默认比例
defScale = 5.0
-- 模型文件
file = "war3mapImported\\IronForgeExterior05.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D00G]
_parent = "ZOrp"
-- 名字
Name = "冰洞6"
-- 默认比例
defScale = 5.0
-- 模型文件
file = "war3mapImported\\IronForgeExterior04.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D00H]
_parent = "ZOrp"
-- 名字
Name = "冰洞7"
-- 默认比例
defScale = 5.0
-- 模型文件
file = "war3mapImported\\IronForgeExterior03.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D00I]
_parent = "ZOrp"
-- 名字
Name = "冰洞8"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapimported\\hd_lanternpostx.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D00J]
_parent = "ZOrp"
-- 名字
Name = "冰洞9"
-- 模型文件
file = "war3mapImported\\HD_TreasurePile0.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {150, 150, 150, 150, 150}
-- 颜色值(绿)
vertG = {150, 150, 150, 150, 150}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150}

[D00K]
_parent = "ZOrp"
-- 名字
Name = "冰洞10"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "forsaken_flighttower.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D00L]
_parent = "IOsm"
-- 默认比例
defScale = 0.8
-- 模型文件
file = "war3mapImported\\web2.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0

[D00M]
_parent = "ZOrp"
-- 名字
Name = "树"
-- 模型文件
file = "war3mapImported\\tree_AshenTree2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = "PathTextures\\4x4Default.tga"
-- 小地图 - 显示
showInMM = 0

[D00N]
_parent = "ZOrp"
-- 名字
Name = "光  绿色 浅"
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 30.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D00O]
_parent = "ZOrp"
-- 名字
Name = "霜 小"
-- 模型文件
file = "war3mapImported\\[spectacle][nature]fog.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D00P]
_parent = "NRic"
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = {
1 = 200,
2 = 200,
3 = 200,
4 = 200,
5 = 200,
6 = 200,
7 = 200,
8 = 200,
9 = 200,
10 = 200,
}
-- 颜色值(红)
vertR = {
1 = 200,
2 = 200,
3 = 200,
4 = 200,
5 = 200,
6 = 200,
7 = 200,
8 = 200,
9 = 200,
10 = 200,
}

[D00Q]
_parent = "ZOrp"
-- 名字
Name = "飞艇"
-- 模型文件
file = "war3mapImported\\DiseaseCloud+.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D00R]
_parent = "IRrk"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 0.5
-- 小地图 - 显示
showInMM = 0

[D00S]
_parent = "IRgc"
-- 固定角度
fixedRot = -1.0
-- 最大比例
maxScale = 0.8
-- 路径纹理
pathTex = "none"
-- 小地图 - 显示
showInMM = 0

[D00T]
_parent = "ZOrp"
-- 名字
Name = "光  蓝色 浅"
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 8.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {150, 150, 150, 150, 150}
-- 颜色值(绿)
vertG = {75, 75, 75, 75, 75}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D00U]
_parent = "IRrk"
-- 名字
Name = "岩石斜"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 2.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D00V]
_parent = "YOsa"
-- 模型文件
file = "war3mapImported\\Skyfire Gunship.mdl"
-- 最大比例
maxScale = 3.0

[D00W]
_parent = "ZOrp"
-- 名字
Name = "天灾旗帜"
-- 模型文件
file = "war3mapImported\\xjcsmblbyq.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D00X]
_parent = "ZOrp"
-- 名字
Name = "天灾柱子"
-- 模型文件
file = "war3mapImported\\zhizi.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 3.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {210, 210, 210, 210, 210}
-- 颜色值(红)
vertR = {210, 210, 210, 210, 210}

[D00Y]
_parent = "ZOrp"
-- 名字
Name = "烟"
-- 模型文件
file = "war3mapImported\\[spectacle][nature]fog.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 0.2
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D00Z]
_parent = "ZOrp"
-- 名字
Name = "光  白色 浅"
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 1000.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {50, 50, 50, 50, 50}
-- 颜色值(绿)
vertG = {50, 50, 50, 50, 50}
-- 颜色值(红)
vertR = {50, 50, 50, 50, 50}

[D010]
_parent = "ZOrp"
-- 名字
Name = "光  白色 浅"
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 20.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {100, 100, 100, 100, 100}
-- 颜色值(绿)
vertG = {100, 100, 100, 100, 100}
-- 颜色值(红)
vertR = {100, 100, 100, 100, 100}

[D011]
_parent = "ZOrp"
-- 名字
Name = "烟"
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {40, 40, 40, 40, 40}
-- 颜色值(绿)
vertG = {40, 40, 40, 40, 40}
-- 颜色值(红)
vertR = {40, 40, 40, 40, 40}

[D012]
_parent = "IRrk"
-- 名字
Name = "岩石斜"
-- 默认比例
defScale = 0.5
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D013]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川5"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\HD_EmptyCrates2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.6
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {175, 175, 175, 175, 175}
-- 颜色值(绿)
vertG = {175, 150, 150, 150, 150}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150}

[D014]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川1"
-- 默认比例
defScale = 0.8
-- 模型文件
file = "war3mapImported\\HD_Ruins_Shrub0.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = {150, 150, 150, 150, 150}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150}

[D015]
_parent = "IRcy"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {200, 200, 200, 200, 200, 200, 200, 200, 200}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150, 150, 150, 150, 150}

[D016]
_parent = "ZOrp"
-- 名字
Name = "树NEW"
-- 模型文件
file = "war3mapImported\\tree_ashentree2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D017]
_parent = "ZOrp"
-- 名字
Name = "树NEW1"
-- 默认比例
defScale = 5.0
-- 模型文件
file = "crystalsongbush02.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D018]
_parent = "ZOrp"
-- 名字
Name = "树NEW2"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "crystalsongbush03.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D019]
_parent = "ZOrp"
-- 名字
Name = "树NEW3"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "silvermoontree03.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 0.5
-- 小地图 - 显示
showInMM = 0

[D01A]
_parent = "ZOrp"
-- 名字
Name = "树NEW4"
-- 默认比例
defScale = 0.25
-- 模型文件
file = "silvermoontree09.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D01B]
_parent = "ZOrp"
-- 名字
Name = "树NEW5"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\SilvermoonTree08.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D01C]
_parent = "ZOrp"
-- 名字
Name = "树NEW6"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "crystalsongtree02.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D01D]
_parent = "ZOrp"
-- 名字
Name = "光  蓝色 浅"
-- 默认比例
defScale = 8.0
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {120, 120, 120, 120, 120}
-- 颜色值(绿)
vertG = {80, 80, 80, 80, 80}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D01E]
_parent = "ZOrp"
-- 名字
Name = "光  蓝色 浅"
-- 默认比例
defScale = 1.8
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = {60, 60, 60, 60, 60}
-- 颜色值(红)
vertR = {60, 60, 60, 60, 60}

[D01F]
_parent = "ZOrp"
-- 名字
Name = "光  橙色 浅"
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 10.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {0, 0, 0, 0, 0}

[D01G]
_parent = "VOal"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\ss67.mdl"
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D01H]
_parent = "VOal"
-- 模型文件
file = "Abilities\\Spells\\Undead\\Impale\\ImpaleMissTarget.mdl"
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D01I]
_parent = "VOal"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\ss62.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D01J]
_parent = "VOal"
-- 名字
Name = "斜放的栅栏"
-- 默认比例
defScale = 0.5
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D01K]
_parent = "VOal"
-- 名字
Name = "浮空城"
-- 模型文件
file = "war3mapImported\\scourgewall.mdl"
-- 最大比例
maxScale = 10.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D01L]
_parent = "ZOrp"
-- 名字
Name = "光晕"
-- 默认比例
defScale = 1.15
-- 模型文件
file = "war3mapImported\\g2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {200, 200, 200, 200, 200}

[D01M]
_parent = "IOsm"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "forsaken_hall.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[D01N]
_parent = "IOsm"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "forsaken_flighttower.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[D01O]
_parent = "VOfl"
-- 名字
Name = "蜘蛛网"
-- 模型文件
file = "war3mapImported\\web2.mdl"
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D01P]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川6"
-- 模型文件
file = "war3mapImported\\HD_CityGrave1.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D01Q]
_parent = "YOks"
-- 名字
Name = "称号1"
-- 默认比例
defScale = 1.0
-- 模型文件
file = "war3mapImported\\ss50.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 有阴影
shadow = 0
-- 小地图 - 显示
showInMM = 0

[D01R]
_parent = "ZOrp"
-- 名字
Name = "光  紫色 浅"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = {0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150}

[D01S]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川3-3"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "war3mapImported\\HD_Hay1.mdl"
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {200, 200, 200, 200, 200}
-- 颜色值(绿)
vertG = {200, 200, 200, 200, 200}
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D01T]
_parent = "ZOrp"
-- 名字
Name = "天灾旗帜"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "panzi.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D01U]
_parent = "VOfl"
-- 名字
Name = "道标"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\firefly.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 0
-- 颜色值(绿)
vertG = 100

[D01V]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川7"
-- 模型文件
file = "war3mapImported\\HD_caibao001.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {150, 150, 150, 150, 150}
-- 颜色值(绿)
vertG = {150, 150, 150, 150, 150}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150}

[D01W]
_parent = "IOst"
-- 名字
Name = "天灾火盆"
-- 模型文件
file = "war3mapImported\\scourgebrazier.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.1
-- 路径纹理
pathTex = ""

[D01X]
_parent = "LOsm"
-- 默认比例
defScale = 3.0
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D01Y]
_parent = "ZOrp"
-- 名字
Name = "烟"
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {60, 60, 60, 60, 60}
-- 颜色值(绿)
vertG = {40, 40, 40, 40, 40}
-- 颜色值(红)
vertR = {40, 40, 40, 40, 40}

[D01Z]
_parent = "VOfl"
-- 模型文件
file = "city_lowwall_tallendcap.mdl"
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D020]
_parent = "VOfl"
-- 模型文件
file = "war3mapImported\\ND_Nerubian_Tower01.mdl"
-- 最大比例
maxScale = 3.0
-- 路径纹理
pathTex = ""
-- 选择圈大小
selSize = 110.0
-- 小地图 - 显示
showInMM = 0

[D021]
_parent = "ZOrp"
-- 名字
Name = "树NEW7"
-- 默认比例
defScale = 0.25
-- 模型文件
file = "war3mapImported\\El.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0
-- 颜色值(红)
vertR = {100, 100, 100, 100, 100}

[D022]
_parent = "ZOrp"
-- 名字
Name = "树NEW8"
-- 默认比例
defScale = 0.25
-- 模型文件
file = "war3mapImported\\El.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D023]
_parent = "ZOrp"
-- 名字
Name = "树NEW9"
-- 模型文件
file = "war3mapImported\\northrendtree1.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D024]
_parent = "ZOrp"
-- 名字
Name = "树NEW10"
-- 默认比例
defScale = 0.4
-- 模型文件
file = "war3mapImported\\El.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D025]
_parent = "VOfl"
-- 名字
Name = "浮空城塔"
-- 模型文件
file = "war3mapImported\\scourgewalltower.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D026]
_parent = "ZOrp"
-- 名字
Name = "烟 蓝"
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {50, 50, 50, 50, 50}
-- 颜色值(绿)
vertG = {30, 30, 30, 30, 30}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D027]
_parent = "VOfl"
-- 模型文件
file = "Doodads\\Northrend\\Structures\\AncientZiggurat\\AncientZiggurat1.mdl"
-- 最大比例
maxScale = 9.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = 230
-- 颜色值(红)
vertR = 200

[D028]
_parent = "IOst"
-- 名字
Name = "天灾火盆"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\scourgebrazier.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(红)
vertR = 0

[D029]
_parent = "VOfl"
-- 名字
Name = "箱子 小"
-- 模型文件
file = "war3mapImported\\crate.mdl"
-- 最大比例
maxScale = 1.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = 180
-- 颜色值(绿)
vertG = 180
-- 颜色值(红)
vertR = 180

[D02A]
_parent = "VOfl"
-- 名字
Name = "选择英雄·光晕"
-- 默认比例
defScale = 3.0
-- 模型文件
file = "war3mapImported\\az_potm(1)_r2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D02B]
_parent = "VOfl"
-- 名字
Name = "灵魂之塔3"
-- 默认比例
defScale = 2.75
-- 模型文件
file = "war3mapImported\\[ake]qz.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D02C]
_parent = "VOal"
-- 模型文件
file = "war3mapImported\\ss61.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D02D]
_parent = "OZsp"
-- 最大比例
maxScale = 4.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 120
-- 颜色值(绿)
vertG = 120

[D02E]
_parent = "ZOrp"
-- 名字
Name = "光  白色 浅"
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 4.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {100, 100, 100, 100, 100}
-- 颜色值(绿)
vertG = {100, 100, 100, 100, 100}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D02F]
_parent = "DOsv"
-- 名字
Name = "塔"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\AnimatedGrass.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D02G]
_parent = "ZOrp"
-- 名字
Name = "树NEW11"
-- 默认比例
defScale = 0.25
-- 模型文件
file = "war3mapImported\\El.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {220, 220, 220, 220, 220}
-- 颜色值(红)
vertR = {100, 100, 100, 100, 100}

[D02H]
_parent = "BOct"
-- 模型文件
file = "war3mapImported\\fk_sylvanasstatue.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.0
-- 颜色值(蓝)
vertB = {200, 200, 200}
-- 颜色值(绿)
vertG = {200, 200, 200}
-- 颜色值(红)
vertR = {200, 200, 200}

[D02I]
_parent = "ZOrp"
-- 名字
Name = "光  橙色 浅"
-- 默认比例
defScale = 8.0
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 10.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {100, 100, 100, 100, 100}

[D02J]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川0"
-- 模型文件
file = "war3mapImported\\HD_StoneWall03.mdl"
-- 路径纹理
pathTex = ""

[D02K]
_parent = "ZOrp"
-- 名字
Name = "烟 橙色"
-- 默认比例
defScale = 0.1
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {50, 50, 50, 50, 50}
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D02L]
_parent = "NOfl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D02M]
_parent = "VOal"
-- 模型文件
file = "rl chengqiang nsd old1.mdl"
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D02N]
_parent = "VOal"
-- 模型文件
file = "az_cmpink_f_originmage.mdl"
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D02O]
_parent = "VOal"
-- 模型文件
file = "war3mapImported\\ss48.mdl"
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D02P]
_parent = "IOsm"
-- 模型文件
file = "war3mapImported\\scarletbannercleanu.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[D02Q]
_parent = "IOsm"
-- 模型文件
file = "war3mapImported\\scarletbannerlong.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[D02R]
_parent = "ZOrp"
-- 名字
Name = "烟 绿色"
-- 默认比例
defScale = 0.1
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {110, 110, 110, 110, 110}

[D02S]
_parent = "ZOrp"
-- 名字
Name = "烟 橙"
-- 默认比例
defScale = 0.1
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {60, 60, 60, 60, 60}
-- 颜色值(红)
vertR = {150, 150, 150, 150, 150}

[D02T]
_parent = "OZsp"
-- 模型文件
file = "buildings\\other\\DarkPortal\\DarkPortal.mdl"
-- 最大比例
maxScale = 3.0
-- 路径纹理
pathTex = ""

[D02U]
_parent = "ZOrp"
-- 名字
Name = "烟 橙色"
-- 默认比例
defScale = 0.35
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D02V]
_parent = "VOal"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\ss62.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D02W]
_parent = "VOfl"
-- 名字
Name = "长篱笆 大"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\moriapillar.mdl"
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 110
-- 颜色值(红)
vertR = 80

[D02X]
_parent = "VOfl"
-- 名字
Name = "石头 大"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapimported\\ceiling rays.mdl"
-- 最大比例
maxScale = 0.8
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = 75
-- 颜色值(绿)
vertG = 75
-- 颜色值(红)
vertR = 75

[D02Y]
_parent = "ZOrp"
-- 名字
Name = "天灾柱子 大"
-- 默认比例
defScale = 1.25
-- 模型文件
file = "war3mapImported\\DraeneiAltar7.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {210, 210, 210, 210, 210}
-- 颜色值(红)
vertR = {210, 210, 210, 210, 210}

[D02Z]
_parent = "ZOrp"
-- 名字
Name = "烟 绿色"
-- 默认比例
defScale = 0.25
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {60, 60, 60, 60, 60}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D030]
_parent = "ZOrp"
-- 名字
Name = "烟 绿色"
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {60, 60, 60, 60, 60}
-- 颜色值(绿)
vertG = {0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D031]
_parent = "ZOrp"
-- 名字
Name = "烟 绿色"
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {40, 40, 40, 40, 40}
-- 颜色值(红)
vertR = {60, 60, 60, 60, 60}

[D032]
_parent = "VOfl"
-- 名字
Name = "长篱笆 大"
-- 模型文件
file = "war3mapImported\\altaroffrost.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D033]
_parent = "ZOrp"
-- 名字
Name = "烟 青色"
-- 默认比例
defScale = 0.2
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {120, 120, 120, 120, 120}
-- 颜色值(红)
vertR = {40, 40, 40, 40, 40}

[D034]
_parent = "ZOrp"
-- 名字
Name = "烟 红色"
-- 默认比例
defScale = 0.2
-- 模型文件
file = "war3mapimported\\[ake]war3ake.com - 7605427555117899530586757.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {0, 0, 0, 0, 0}
-- 颜色值(绿)
vertG = {0, 0, 0, 0, 0}
-- 颜色值(红)
vertR = {100, 100, 100, 100, 100}

[D035]
_parent = "VOfl"
-- 名字
Name = "天灾城柱子"
-- 模型文件
file = "war3mapImported\\zhuzi20.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D036]
_parent = "VOfl"
-- 名字
Name = "天灾城墙"
-- 模型文件
file = "war3mapimported\\hd_curbstone0.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D037]
_parent = "VOfl"
-- 名字
Name = "天灾城柱子"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\Dwarf_Pillar.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = 200
-- 颜色值(绿)
vertG = 200
-- 颜色值(红)
vertR = 200

[D038]
_parent = "VOfl"
-- 名字
Name = "天灾城柱子"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\moriapillar.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = 150
-- 颜色值(红)
vertR = 60

[D039]
_parent = "ZOrp"
-- 名字
Name = "树NEW12"
-- 默认比例
defScale = 0.25
-- 模型文件
file = "Doodads\\Terrain\\LordaeronTree\\LordaeronTree0S.mdl"
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {220, 220, 220, 220, 220}

[D03A]
_parent = "ZOrp"
-- 名字
Name = "先锋军旗帜"
-- 默认比例
defScale = 1.25
-- 模型文件
file = "war3mapImported\\Scarlet_Banner_Stand.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 小地图 - 显示
showInMM = 0

[D03B]
_parent = "ZOrp"
-- 名字
Name = "烟new"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\cloud-blend.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {240, 240, 240, 240, 240}
-- 颜色值(红)
vertR = {240, 240, 240, 240, 240}

[D03C]
_parent = "ZOrp"
-- 名字
Name = "烟new1"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "war3mapImported\\cloud-blend.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {50, 50, 50, 50, 50}
-- 颜色值(绿)
vertG = {120, 120, 120, 120, 120}
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D03D]
_parent = "ZOrp"
-- 名字
Name = "烟new2"
-- 模型文件
file = "war3mapImported\\cloud-blend.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = {200, 200, 200, 200, 200}
-- 颜色值(红)
vertR = {200, 200, 200, 200, 200}

[D03E]
_parent = "ZOrp"
-- 名字
Name = "烟new3"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "war3mapImported\\cloud-blend.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {200, 200, 200, 200, 200}

[D03F]
_parent = "ZOrp"
-- 名字
Name = "烟new4"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "war3mapImported\\cloud-blend.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D03G]
_parent = "ZOrp"
-- 名字
Name = "先锋军旗帜"
-- 模型文件
file = "war3mapimported\\water2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {150, 150, 150, 150, 150}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D03H]
_parent = "LRrk"
-- 默认比例
defScale = 0.45
-- 颜色值(红)
vertR = {
1 = 150,
2 = 150,
3 = 150,
4 = 150,
5 = 150,
6 = 150,
7 = 150,
8 = 150,
9 = 150,
10 = 150,
}

[D03I]
_parent = "VOfl"
-- 名字
Name = "长篱笆 大"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "war3mapImported\\zhuangshiwu004.mdl"
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D03J]
_parent = "NRic"
-- 默认比例
defScale = 3.0
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = {
1 = 200,
2 = 200,
3 = 200,
4 = 200,
5 = 200,
6 = 200,
7 = 200,
8 = 200,
9 = 200,
10 = 200,
}
-- 颜色值(红)
vertR = {
1 = 200,
2 = 200,
3 = 200,
4 = 200,
5 = 200,
6 = 200,
7 = 200,
8 = 200,
9 = 200,
10 = 200,
}

[D03K]
_parent = "IRrk"
-- 名字
Name = "岩石斜"
-- 默认比例
defScale = 3.0
-- 模型文件
file = "xuanfeng2.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.5
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D03L]
_parent = "YOks"
-- 名字
Name = "天灾城柱底"
-- 默认比例
defScale = 3.0
-- 模型文件
file = "war3mapImported\\moriawell.mdl"
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = 150
-- 颜色值(红)
vertR = 100

[D03M]
_parent = "VOfl"
-- 名字
Name = "天灾城墙1"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\HD_LanternPostx.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D03N]
_parent = "ZOrp"
-- 名字
Name = "光  蓝色 浅"
-- 模型文件
file = "war3mapImported\\[spectacle][light]Glow_white.mdl"
-- 最大比例
maxScale = 1000.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {30, 30, 30, 30, 30}
-- 颜色值(绿)
vertG = {20, 20, 20, 20, 20}
-- 颜色值(红)
vertR = {20, 20, 20, 20, 20}

[D03O]
_parent = "IOst"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\scourgebrazier.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D03P]
_parent = "ZOrp"
-- 名字
Name = "天灾旗帜"
-- 模型文件
file = "zhuzi2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D03Q]
_parent = "ZOrp"
-- 名字
Name = "冰冠冰川8"
-- 模型文件
file = "war3mapImported\\HD_brazier.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D03R]
_parent = "ZOrp"
-- 名字
Name = "烟new0"
-- 默认比例
defScale = 0.8
-- 模型文件
file = "war3mapImported\\cloud-blend.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(绿)
vertG = {240, 240, 240, 240, 240}
-- 颜色值(红)
vertR = {240, 240, 240, 240, 240}

[D03S]
_parent = "VOfl"
-- 名字
Name = "泰坦墙"
-- 模型文件
file = "war3mapImported\\HD_Grave.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D03T]
_parent = "VOfl"
-- 名字
Name = "泰坦墙"
-- 模型文件
file = "war3mapImported\\[ake]war3ake.com - 0207194794849369616067742.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = 235
-- 颜色值(绿)
vertG = 235
-- 颜色值(红)
vertR = 235

[D03U]
_parent = "YOks"
-- 默认比例
defScale = 1.25
-- 模型文件
file = "war3mapImported\\ss47.mdl"
-- 路径纹理
pathTex = ""

[D03V]
_parent = "VOfl"
-- 名字
Name = "圣光"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\firefly.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = 50
-- 颜色值(红)
vertR = 100

[D03W]
_parent = "VOal"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\ss62.mdl"
-- X轴最大旋转角度(度数)
maxPitch = -14.0
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D03X]
_parent = "YOks"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\fk_sylvanasstatue.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 230
-- 颜色值(绿)
vertG = 230
-- 颜色值(红)
vertR = 240

[D03Y]
_parent = "YOks"
-- 名字
Name = "刺"
-- 默认比例
defScale = 2.0
-- 模型文件
file = "war3mapImported\\hd_curbstone0.mdl"
-- 路径纹理
pathTex = ""

[D03Z]
_parent = "VOfs"
-- 默认比例
defScale = 1.5
-- 路径纹理
pathTex = ""

[D040]
_parent = "ZOrp"
-- 名字
Name = "窗户光照"
-- 默认比例
defScale = 5.0
-- 模型文件
file = "war3mapImported\\Window Rays 1.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = {150, 150, 150, 150, 150}
-- 颜色值(绿)
vertG = {125, 125, 125, 125, 125}
-- 颜色值(红)
vertR = {0, 0, 0, 0, 0}

[D041]
_parent = "YOks"
-- 名字
Name = "墙"
-- 默认比例
defScale = 1.0
-- 模型文件
file = "war3mapImported\\citylowwall0.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D042]
_parent = "VOfl"
-- 名字
Name = "火苗"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\firefly.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 100
-- 颜色值(绿)
vertG = 200

[D043]
_parent = "YOlp"
-- 模型文件
file = "war3mapImported\\hfmlamppost.mdl"

[D044]
_parent = "ZOrp"
-- 名字
Name = "窗户"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "war3mapImported\\windowglass 05.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D045]
_parent = "YOlp"
-- 名字
Name = "挂灯笼的柱子2"
-- 模型文件
file = "war3mapImported\\shadowtrap.mdl"

[D046]
_parent = "DOas"
-- 默认比例
defScale = 1.75
-- 模型文件
file = "war3mapimported\\hd_grave.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D047]
_parent = "DOas"
-- 名字
Name = "锁链"
-- 默认比例
defScale = 0.2
-- 模型文件
file = "sunwell.mdl"
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 130
-- 颜色值(绿)
vertG = 235
-- 颜色值(红)
vertR = 235

[D048]
_parent = "DOas"
-- 模型文件
file = "war3mapImported\\hd_stairs.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D049]
_parent = "VOal"
-- 名字
Name = "围栏1"
-- 模型文件
file = "war3mapImported\\ss50.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D04A]
_parent = "VSvb"
-- 名字
Name = "建筑1"
-- 默认比例
defScale = 0.75
-- 最小比例
minScale = 0.75

[D04B]
_parent = "VSvb"
-- 名字
Name = "建筑2"
-- 模型文件
file = "villagebuilding1.mdl"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.0

[D04C]
_parent = "VOal"
-- 名字
Name = "围栏2"
-- 模型文件
file = "war3mapImported\\HD_CityGrave1.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = 230
-- 颜色值(绿)
vertG = 230

[D04D]
_parent = "VOal"
-- 名字
Name = "围栏3"
-- 默认比例
defScale = 1.25
-- 模型文件
file = "village_fencelongangled.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0
-- 颜色值(蓝)
vertB = 230
-- 颜色值(绿)
vertG = 230

[D04E]
_parent = "VOal"
-- 名字
Name = "围栏4"
-- 默认比例
defScale = 1.25
-- 模型文件
file = "city_lowwall_tallendcap.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D04F]
_parent = "VOal"
-- 名字
Name = "围栏4"
-- 默认比例
defScale = 25.0
-- 模型文件
file = "war3mapimported\\westfallchurch.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 选择圈大小
selSize = 1.0
-- 小地图 - 显示
showInMM = 0

[D04G]
_parent = "VOal"
-- 名字
Name = "围栏4"
-- 模型文件
file = "stone_fence_gate.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D04H]
_parent = "VOal"
-- 名字
Name = "围栏4"
-- 模型文件
file = "stone_fence_gate.mdl"
-- X轴最大旋转角度(度数)
maxPitch = -30.0
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[D04I]
_parent = "GRst"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "war3mapImported\\spirecreep3.mdl"
-- X轴最大旋转角度(度数)
maxPitch = 20.0
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D04J]
_parent = "ZZys"
-- 默认比例
defScale = 2.0

[D04K]
_parent = "GRst"
-- 名字
Name = "石笋1"
-- 默认比例
defScale = 0.5
-- 模型文件
file = "war3mapImported\\spirecreep1.mdl"
-- 最大比例
maxScale = 5.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D04L]
_parent = "GRst"
-- 名字
Name = "石笋2"
-- 默认比例
defScale = 0.25
-- 模型文件
file = "war3mapImported\\spirecreep2.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""

[D04M]
_parent = "GRst"
-- 名字
Name = "石笋4"
-- 模型文件
file = "war3mapImported\\spirecreep4.mdl"
-- X轴最大旋转角度(度数)
maxPitch = -150.0
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 0.5
-- 路径纹理
pathTex = ""

[D04N]
_parent = "JSax"
-- 路径纹理
pathTex = ""

[D04O]
_parent = "LOhp"
-- 名字
Name = "光环 红"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "war3mapimported\\spell marker green.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 0
-- 颜色值(绿)
vertG = 0

[D04P]
_parent = "LOhp"
-- 名字
Name = "光环 蓝"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "war3mapImported\\anheiguang.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = 0
-- 颜色值(红)
vertR = 0

[D04Q]
_parent = "LOhp"
-- 名字
Name = "光环 青"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "war3mapImported\\anheiguang.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(红)
vertR = 0

[D04R]
_parent = "LOhp"
-- 名字
Name = "光环 紫"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "war3mapImported\\anheiguang.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(绿)
vertG = 0

[D04S]
_parent = "LOhp"
-- 名字
Name = "光环 黄"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "war3mapImported\\anheiguang.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 0

[D04T]
_parent = "LOhp"
-- 名字
Name = "光环 绿"
-- 默认比例
defScale = 4.0
-- 模型文件
file = "war3mapImported\\anheiguang.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 颜色值(蓝)
vertB = 0
-- 颜色值(红)
vertR = 0

[D04U]
_parent = "ZOba"
-- 名字
Name = "特效·电影"
-- 模型文件
file = "war3mapimported\\divineshieldtarget.mdl"
-- 路径纹理
pathTex = ""

[D04V]
_parent = "ZOba"
-- 名字
Name = "特效·电影"
-- 模型文件
file = "war3mapImported\\az2_[slfs]d2-2.mdl"
-- 路径纹理
pathTex = ""

[D04W]
_parent = "ZOba"
-- 名字
Name = "特效·电影"
-- 模型文件
file = "war3mapimported\\arcaneshield.mdl"
-- 路径纹理
pathTex = ""

[D04X]
_parent = "YOks"
-- 默认比例
defScale = 1.5
-- 模型文件
file = "b ta.mdl"
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
