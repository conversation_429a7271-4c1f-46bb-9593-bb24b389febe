//===========================================================================
// 黑暗箭雨技能 - 仿暴风雪效果
//===========================================================================

// 全局变量声明
globals
    trigger gg_trg_DarkArrowRain = null
endglobals

// 单支箭的下落定时器回调
function Trig_DarkArrowFallTimer takes nothing returns nothing
    local timer ydl_timer = GetExpiredTimer()
    local effect ydl_arrow = YDLocalGet(ydl_timer, effect, "arrow")
    local real ydl_startTime = YDLocalGet(ydl_timer, real, "startTime")
    local real ydl_currentTime = YDLocalGet(ydl_timer, real, "currentTime")
    local real ydl_targetX = YDLocalGet(ydl_timer, real, "targetX")
    local real ydl_targetY = YDLocalGet(ydl_timer, real, "targetY")
    local real ydl_progress
    local real ydl_currentZ
    local effect ydl_effect
    
    // 更新时间（降低更新频率，让箭矢下落更清晰）
    set ydl_currentTime = ydl_currentTime + 0.05
    call YDLocalSet(ydl_timer, real, "currentTime", ydl_currentTime)
    
    // 计算下落进度 (0.3秒完成下落)
    set ydl_progress = (ydl_currentTime - ydl_startTime) / 0.3
    
    if ydl_progress >= 1.0 then
        // 箭矢到达地面
        set ydl_currentZ = 50.0
        call EXSetEffectZ(ydl_arrow, ydl_currentZ)
        
        // 创建撞击特效
        set ydl_effect = AddSpecialEffect("Abilities\\Spells\\Other\\Stampede\\StampedeMissileDeath.mdl", ydl_targetX, ydl_targetY)
        call DestroyEffect(ydl_effect)
        
        // 销毁箭矢和定时器
        call DestroyEffect(ydl_arrow)
        call YDLocal3Release()
        call DestroyTimer(ydl_timer)
    else
        // 更新箭矢高度 (从2000到50的抛物线)
        set ydl_currentZ = 2000.0 - (1950.0 * ydl_progress * ydl_progress)  // 抛物线下落
        call EXSetEffectZ(ydl_arrow, ydl_currentZ)
    endif
    
    // 清理局部变量
    set ydl_timer = null
    set ydl_arrow = null
    set ydl_effect = null
endfunction

// 伤害定时器回调 - 每0.3秒造成一次伤害
function Trig_DarkArrowDamageTimer takes nothing returns nothing
    local timer ydl_timer = GetExpiredTimer()
    local unit ydl_caster = YDLocalGet(ydl_timer, unit, "caster")
    local real ydl_centerX = YDLocalGet(ydl_timer, real, "centerX")
    local real ydl_centerY = YDLocalGet(ydl_timer, real, "centerY")
    local real ydl_damage = YDLocalGet(ydl_timer, real, "damage")
    local integer ydl_damageCount = YDLocalGet(ydl_timer, integer, "damageCount")
    local group ydl_group
    local unit ydl_unit
    local effect ydl_effect
    
    // 减少剩余伤害次数
    set ydl_damageCount = ydl_damageCount - 1
    call YDLocalSet(ydl_timer, integer, "damageCount", ydl_damageCount)
    
    // 创建伤害区域特效（黑暗能量波动）
    set ydl_effect = AddSpecialEffect("Abilities\\Spells\\Other\\BlackArrow\\BlackArrowMissile.mdl", ydl_centerX, ydl_centerY)
    call DestroyEffect(ydl_effect)
    
    // 对范围内敌人造成伤害
    set ydl_group = CreateGroup()
    call GroupEnumUnitsInRange(ydl_group, ydl_centerX, ydl_centerY, 300.0, Filter(null))
    loop
        set ydl_unit = FirstOfGroup(ydl_group)
        exitwhen ydl_unit == null
        call GroupRemoveUnit(ydl_group, ydl_unit)
        
        // 检查是否为敌人且存活
        if IsUnitEnemy(ydl_unit, GetOwningPlayer(ydl_caster)) and GetUnitState(ydl_unit, UNIT_STATE_LIFE) > 0.405 and not IsUnitType(ydl_unit, UNIT_TYPE_STRUCTURE) then
            call UnitDamageTarget(ydl_caster, ydl_unit, ydl_damage, true, false, ATTACK_TYPE_CHAOS, DAMAGE_TYPE_MAGIC, WEAPON_TYPE_WHOKNOWS)
            // 单体受伤特效
            set ydl_effect = AddSpecialEffect("Abilities\\Spells\\Other\\Stampede\\StampedeMissileDeath.mdl", GetUnitX(ydl_unit), GetUnitY(ydl_unit))
            call DestroyEffect(ydl_effect)
        endif
    endloop
    
    // 检查是否结束
    if ydl_damageCount <= 0 then
        // 清理定时器数据并销毁
        call YDLocal3Release()
        call DestroyTimer(ydl_timer)
    endif
    
    // 清理局部变量
    call DestroyGroup(ydl_group)
    set ydl_group = null
    set ydl_unit = null
    set ydl_timer = null
    set ydl_caster = null
    set ydl_effect = null
endfunction

// 创建单支箭矢
function CreateDarkArrow takes real centerX, real centerY, real delay returns nothing
    local timer ydl_arrowTimer
    local effect ydl_arrow
    local real ydl_randomX
    local real ydl_randomY
    local real ydl_distance
    local real ydl_angle
    
    // 使用暴风雪算法生成随机位置
    set ydl_distance = GetRandomReal(0.0, 300.0)
    set ydl_angle = GetRandomReal(0.0, 6.28318)  // 0到2π
    set ydl_randomX = centerX + ydl_distance * Cos(ydl_angle)
    set ydl_randomY = centerY + ydl_distance * Sin(ydl_angle)
    
    // 创建朝下的黑暗之箭特效（完美解决方案：几何体直接朝下）
    set ydl_arrow = AddSpecialEffect("war3mapImported\\BlackArrowMissile_Down.mdx", ydl_randomX, ydl_randomY)
    call EXSetEffectZ(ydl_arrow, 2000.0)  // 设置初始高度

    // 无需任何旋转函数！箭矢默认就朝下，无旋转动画！
    // 完美解决了旋转动画问题
    
    // 创建箭矢下落定时器
    set ydl_arrowTimer = CreateTimer()
    call YDLocalSet(ydl_arrowTimer, effect, "arrow", ydl_arrow)
    call YDLocalSet(ydl_arrowTimer, real, "startTime", delay)
    call YDLocalSet(ydl_arrowTimer, real, "currentTime", 0.0)
    call YDLocalSet(ydl_arrowTimer, real, "targetX", ydl_randomX)
    call YDLocalSet(ydl_arrowTimer, real, "targetY", ydl_randomY)
    call TimerStart(ydl_arrowTimer, 0.05, true, function Trig_DarkArrowFallTimer)
    
    // 清理局部变量
    set ydl_arrowTimer = null
    set ydl_arrow = null
endfunction

// 主技能函数
function Trig_DarkArrowRainActions takes nothing returns nothing
    local timer ydl_damageTimer
    local unit ydl_caster
    local real ydl_targetX
    local real ydl_targetY
    local real ydl_heroInt
    local real ydl_skillLevel
    local real ydl_skillDamage
    local real ydl_baseDamage
    local real ydl_finalDamage
    local integer ydl_i
    local real ydl_arrowDelay
    local effect ydl_effect
    
    // 获取攻击信息
    set ydl_caster = GetAttacker()  // 攻击者
    set ydl_targetX = GetUnitX(GetTriggerUnit())  // 被攻击单位的位置
    set ydl_targetY = GetUnitY(GetTriggerUnit())
    
    // 显示调试信息
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "单位被攻击触发黑暗箭雨")
    
    // 计算伤害
    if IsUnitType(ydl_caster, UNIT_TYPE_HERO) then
        set ydl_heroInt = I2R(GetHeroInt(ydl_caster, true))
        set ydl_skillLevel = 1.0
        set ydl_skillDamage = 0.0
    else
        set ydl_heroInt = 10.0
        set ydl_skillLevel = 1.0
        set ydl_skillDamage = 0.0
    endif
    
    set ydl_baseDamage = ydl_heroInt * 2.0 * ydl_skillLevel
    set ydl_finalDamage = ydl_baseDamage * (1.0 + ydl_skillDamage)
    
    // 不需要预警特效，直接开始箭雨
    
    // 创建16支箭矢，分批次生成让效果更清晰
    set ydl_i = 0
    loop
        exitwhen ydl_i >= 16
        set ydl_arrowDelay = GetRandomReal(0.0, 1.0)  // 在1.0秒内随机延迟，更集中
        call CreateDarkArrow(ydl_targetX, ydl_targetY, ydl_arrowDelay)
        set ydl_i = ydl_i + 1
    endloop
    
    // 创建伤害定时器 (每0.3秒造成一次伤害，共5次)
    set ydl_damageTimer = CreateTimer()
    call YDLocalSet(ydl_damageTimer, unit, "caster", ydl_caster)
    call YDLocalSet(ydl_damageTimer, real, "centerX", ydl_targetX)
    call YDLocalSet(ydl_damageTimer, real, "centerY", ydl_targetY)
    call YDLocalSet(ydl_damageTimer, real, "damage", ydl_finalDamage)
    call YDLocalSet(ydl_damageTimer, integer, "damageCount", 5)  // 1.5秒 / 0.3秒 = 5次
    call TimerStart(ydl_damageTimer, 0.3, true, function Trig_DarkArrowDamageTimer)
    
    // 清理局部变量
    set ydl_damageTimer = null
    set ydl_caster = null
    set ydl_effect = null
endfunction

function InitTrig_DarkArrowRain takes nothing returns nothing
    set gg_trg_DarkArrowRain = CreateTrigger()
    call TriggerRegisterAnyUnitEventBJ(gg_trg_DarkArrowRain, EVENT_PLAYER_UNIT_ATTACKED)
    call TriggerAddAction(gg_trg_DarkArrowRain, function Trig_DarkArrowRainActions)
endfunction

//===========================================================================
// 使用说明：
// 1. 将此代码复制到地图编辑器的触发器中
// 2. 在主初始化函数中调用 InitTrig_DarkArrowRain()
// 3. 任意单位被攻击时会在被攻击单位位置创建黑暗箭雨效果
// 4. 16支黑暗之箭从2000高度在1.5秒内随机落下
// 5. 每0.3秒对300码范围内敌人造成一次伤害
//===========================================================================
