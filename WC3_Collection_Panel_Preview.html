<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>魔兽争霸3 收藏面板 - 优化效果预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #1a1a2e, #16213e);
            font-family: 'Courier New', monospace;
            color: #fff;
            overflow-x: auto;
        }

        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #4a5568;
        }

        .title {
            text-align: center;
            color: #ffd700;
            font-size: 24px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .comparison {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .panel-container {
            flex: 1;
            min-width: 400px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #666;
        }

        .panel-title {
            text-align: center;
            color: #87ceeb;
            font-size: 18px;
            margin-bottom: 15px;
        }

        /* 当前版本样式 */
        .current-panel {
            width: 320px;
            height: 240px;
            background: linear-gradient(135deg, #2d3748, #4a5568);
            border: 2px solid #718096;
            border-radius: 8px;
            position: relative;
            margin: 0 auto;
        }

        /* 优化版本样式 */
        .optimized-panel {
            width: 320px;
            height: 240px;
            background: linear-gradient(135deg, #2d1b69, #553c9a);
            border: 3px solid #9f7aea;
            border-radius: 12px;
            position: relative;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(159, 122, 234, 0.3);
        }

        .panel-header {
            text-align: center;
            padding: 8px;
            font-size: 16px;
            font-weight: bold;
        }

        .current-panel .panel-header {
            color: #e2e8f0;
            background: rgba(0, 0, 0, 0.2);
        }

        .optimized-panel .panel-header {
            color: #ffd700;
            background: linear-gradient(90deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.1));
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .collection-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            padding: 15px;
            width: 160px;
            float: left;
        }

        .collection-slot {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .current-panel .collection-slot {
            background: #4a5568;
            border: 1px solid #718096;
            color: #cbd5e0;
        }

        .current-panel .collection-slot:hover {
            background: #5a6578;
            border-color: #a0aec0;
        }

        .optimized-panel .collection-slot {
            background: linear-gradient(135deg, #553c9a, #7c3aed);
            border: 2px solid #9f7aea;
            color: #ffd700;
            box-shadow: inset 0 0 5px rgba(255, 215, 0, 0.2);
        }

        .optimized-panel .collection-slot:hover {
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            border-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
            transform: scale(1.1);
        }

        .detail-panel {
            float: right;
            width: 120px;
            height: 140px;
            margin: 15px 10px;
            padding: 10px;
            border-radius: 6px;
        }

        .current-panel .detail-panel {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid #4a5568;
            color: #a0aec0;
        }

        .optimized-panel .detail-panel {
            background: linear-gradient(135deg, rgba(159, 122, 234, 0.2), rgba(124, 58, 237, 0.3));
            border: 2px solid #9f7aea;
            color: #e2e8f0;
            box-shadow: inset 0 0 10px rgba(159, 122, 234, 0.1);
        }

        .detail-title {
            font-size: 14px;
            margin-bottom: 8px;
            text-align: center;
        }

        .current-panel .detail-title {
            color: #cbd5e0;
        }

        .optimized-panel .detail-title {
            color: #87ceeb;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
        }

        .detail-text {
            font-size: 11px;
            line-height: 1.3;
        }

        .toggle-button {
            position: absolute;
            bottom: 10px;
            left: 10px;
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
        }

        .current-panel .toggle-button {
            background: #4a5568;
            border: 1px solid #718096;
            color: #cbd5e0;
        }

        .optimized-panel .toggle-button {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border: 2px solid #f59e0b;
            color: #1a202c;
            font-weight: bold;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
        }

        .features-list {
            margin-top: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
        }

        .features-title {
            color: #ffd700;
            font-size: 20px;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-item {
            margin: 10px 0;
            padding: 8px;
            background: rgba(159, 122, 234, 0.1);
            border-left: 4px solid #9f7aea;
            border-radius: 4px;
        }

        .feature-label {
            color: #87ceeb;
            font-weight: bold;
        }

        .feature-desc {
            color: #e2e8f0;
            margin-left: 10px;
        }

        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }

        @media (max-width: 768px) {
            .comparison {
                flex-direction: column;
            }
            .panel-container {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1 class="title">🎮 魔兽争霸3 收藏面板美术优化效果预览</h1>
        
        <div class="comparison">
            <!-- 当前版本 -->
            <div class="panel-container">
                <h2 class="panel-title">📋 当前版本</h2>
                <div class="current-panel clearfix">
                    <div class="panel-header">收藏面板</div>
                    
                    <div class="collection-grid">
                        <div class="collection-slot">🛡️</div>
                        <div class="collection-slot">⚔️</div>
                        <div class="collection-slot">🏹</div>
                        <div class="collection-slot">🔮</div>
                        <div class="collection-slot">💎</div>
                        <div class="collection-slot">📜</div>
                        <div class="collection-slot">🗝️</div>
                        <div class="collection-slot">💰</div>
                        <div class="collection-slot">🧪</div>
                        <div class="collection-slot">🔥</div>
                        <div class="collection-slot">❄️</div>
                        <div class="collection-slot">⚡</div>
                        <div class="collection-slot">🌟</div>
                        <div class="collection-slot">🌙</div>
                        <div class="collection-slot">☀️</div>
                        <div class="collection-slot">🔱</div>
                    </div>
                    
                    <div class="detail-panel">
                        <div class="detail-title">详情</div>
                        <div class="detail-text">选择一个收藏品查看详情</div>
                    </div>
                    
                    <div class="toggle-button">⚙️</div>
                </div>
            </div>

            <!-- 优化版本 -->
            <div class="panel-container">
                <h2 class="panel-title">✨ 优化版本</h2>
                <div class="optimized-panel clearfix">
                    <div class="panel-header">⚔️ 收藏面板 ⚔️</div>
                    
                    <div class="collection-grid">
                        <div class="collection-slot">🛡️</div>
                        <div class="collection-slot">⚔️</div>
                        <div class="collection-slot">🏹</div>
                        <div class="collection-slot">🔮</div>
                        <div class="collection-slot">💎</div>
                        <div class="collection-slot">📜</div>
                        <div class="collection-slot">🗝️</div>
                        <div class="collection-slot">💰</div>
                        <div class="collection-slot">🧪</div>
                        <div class="collection-slot">🔥</div>
                        <div class="collection-slot">❄️</div>
                        <div class="collection-slot">⚡</div>
                        <div class="collection-slot">🌟</div>
                        <div class="collection-slot">🌙</div>
                        <div class="collection-slot">☀️</div>
                        <div class="collection-slot">🔱</div>
                    </div>
                    
                    <div class="detail-panel">
                        <div class="detail-title">📖 详情</div>
                        <div class="detail-text">
                            <span style="color: #ffd700;">传说之剑</span><br><br>
                            <span style="color: #87ceeb;">一把拥有古老力量的神器，曾属于传奇英雄。</span>
                        </div>
                    </div>
                    
                    <div class="toggle-button">📖</div>
                </div>
            </div>
        </div>

        <!-- 优化特性列表 -->
        <div class="features-list">
            <h2 class="features-title">🎨 美术优化特性</h2>
            
            <div class="feature-item">
                <span class="feature-label">🎭 不死族主题风格：</span>
                <span class="feature-desc">使用紫色渐变背景，体现魔兽争霸3不死族的神秘感</span>
            </div>
            
            <div class="feature-item">
                <span class="feature-label">✨ 发光边框效果：</span>
                <span class="feature-desc">面板和按钮添加紫色发光边框，增强视觉层次</span>
            </div>
            
            <div class="feature-item">
                <span class="feature-label">🌟 悬停交互反馈：</span>
                <span class="feature-desc">鼠标悬停时收藏槽会发光并放大，提供清晰的交互反馈</span>
            </div>
            
            <div class="feature-item">
                <span class="feature-label">🎨 WC3经典颜色：</span>
                <span class="feature-desc">标题使用金色，详情使用淡蓝色，符合魔兽争霸3的UI风格</span>
            </div>
            
            <div class="feature-item">
                <span class="feature-label">🔮 渐变背景纹理：</span>
                <span class="feature-desc">使用CSS模拟WC3原生纹理的渐变效果</span>
            </div>
            
            <div class="feature-item">
                <span class="feature-label">⚡ 动态阴影效果：</span>
                <span class="feature-desc">添加内阴影和外发光，营造立体感和魔法氛围</span>
            </div>
            
            <div class="feature-item">
                <span class="feature-label">🎯 统一视觉语言：</span>
                <span class="feature-desc">所有UI元素使用统一的紫金配色方案，保持视觉一致性</span>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.collection-slot').forEach(slot => {
            slot.addEventListener('mouseenter', function() {
                if (this.closest('.optimized-panel')) {
                    const detailText = this.closest('.optimized-panel').querySelector('.detail-text');
                    const items = ['传说之剑', '龙鳞护甲', '精灵之弓', '法师法杖', '生命宝石', '古老卷轴', '神秘钥匙', '黄金宝箱'];
                    const descs = ['拥有古老力量的神器', '龙族守护的坚固护甲', '精灵工匠的杰作', '蕴含魔法能量的法杖', '恢复生命力的宝石', '记录失传法术的卷轴', '开启秘密宝库的钥匙', '装满财富的宝箱'];
                    const index = Math.floor(Math.random() * items.length);
                    detailText.innerHTML = `<span style="color: #ffd700;">${items[index]}</span><br><br><span style="color: #87ceeb;">${descs[index]}</span>`;
                }
            });
        });
    </script>
</body>
</html>
