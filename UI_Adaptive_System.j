//===========================================================================
// 1.27版本UI自适应系统 - 解决全屏拉伸问题
//===========================================================================

globals
    // 屏幕适配参数
    real SCREEN_WIDTH = 0.8          // 屏幕宽度
    real SCREEN_HEIGHT = 0.6         // 屏幕高度
    real ASPECT_RATIO = 1.333        // 当前宽高比
    real TARGET_RATIO = 1.333        // 目标4:3比例
    real SCALE_FACTOR = 1.0          // 缩放因子
    
    // UI框架句柄
    integer MainGameUI = 0           // 主游戏UI
    integer AdaptiveContainer = 0    // 自适应容器
    
    // UI组件句柄
    integer TopPanel = 0             // 顶部面板
    integer BottomPanel = 0          // 底部面板
    integer LeftPanel = 0            // 左侧面板
    integer RightPanel = 0           // 右侧面板
    integer MiniMapFrame = 0         // 小地图
    integer CommandPanel = 0         // 命令面板
    integer ResourcePanel = 0        // 资源面板
    
    // 适配状态
    boolean IsUIAdapted = false      // UI是否已适配
    trigger ScreenResizeDetector = null  // 屏幕变化检测器
endglobals

//===========================================================================
// 屏幕比例检测和计算
//===========================================================================
function DetectScreenRatio takes nothing returns nothing
    local real gameWidth = DzGetClientWidth()
    local real gameHeight = DzGetClientHeight()
    
    // 计算当前宽高比
    if gameHeight > 0 then
        set ASPECT_RATIO = gameWidth / gameHeight
    endif
    
    // 计算缩放因子
    if ASPECT_RATIO > TARGET_RATIO then
        // 宽屏，需要在两侧添加黑边效果
        set SCALE_FACTOR = TARGET_RATIO / ASPECT_RATIO
        set SCREEN_WIDTH = 0.8 * SCALE_FACTOR
        set SCREEN_HEIGHT = 0.6
    else
        // 窄屏或标准比例
        set SCALE_FACTOR = 1.0
        set SCREEN_WIDTH = 0.8
        set SCREEN_HEIGHT = 0.6 * (ASPECT_RATIO / TARGET_RATIO)
    endif
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[适配] 屏幕比例: " + R2S(ASPECT_RATIO) + ", 缩放: " + R2S(SCALE_FACTOR) + "|r")
endfunction

//===========================================================================
// 创建自适应UI容器
//===========================================================================
function CreateAdaptiveContainer takes nothing returns nothing
    // 创建主容器
    set AdaptiveContainer = DzCreateFrameByTagName("BACKDROP", "AdaptiveContainer", DzGetGameUI(), "", 0)
    
    if AdaptiveContainer == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] 自适应容器创建失败|r")
        return
    endif
    
    // 设置容器大小和位置（居中）
    call DzFrameSetSize(AdaptiveContainer, SCREEN_WIDTH, SCREEN_HEIGHT)
    call DzFrameSetAbsolutePoint(AdaptiveContainer, 4, 0.4, 0.3)  // 居中定位
    
    // 设置半透明黑色背景（可选，用于调试）
    call DzFrameSetTexture(AdaptiveContainer, "UI\\Widgets\\EscMenu\\Human\\blank-background.blp", 0)
    call DzFrameSetAlpha(AdaptiveContainer, 0)  // 完全透明
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[适配] 自适应容器创建成功|r")
endfunction

//===========================================================================
// 创建顶部资源面板
//===========================================================================
function CreateTopResourcePanel takes nothing returns nothing
    local real panelWidth = SCREEN_WIDTH
    local real panelHeight = 0.08
    
    // 创建顶部面板
    set TopPanel = DzCreateFrameByTagName("BACKDROP", "TopPanel", AdaptiveContainer, "", 0)
    call DzFrameSetSize(TopPanel, panelWidth, panelHeight)
    call DzFrameSetPoint(TopPanel, 1, AdaptiveContainer, 1, 0.0, 0.0)  // 顶部对齐
    
    // 设置背景纹理
    call DzFrameSetTexture(TopPanel, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
    
    // 创建资源显示
    set ResourcePanel = DzCreateFrameByTagName("TEXT", "ResourceText", TopPanel, "", 0)
    call DzFrameSetPoint(ResourcePanel, 4, TopPanel, 4, 0.0, 0.0)
    call DzFrameSetFont(ResourcePanel, "Fonts\\FRIZQT__.ttf", 0.012, 0)
    call DzFrameSetText(ResourcePanel, "|cFFFFD700金币: 2656  |cFF87CEEB木材: 705  |cFF90EE90食物: 18/80|r")
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[适配] 顶部资源面板创建完成|r")
endfunction

//===========================================================================
// 创建底部命令面板
//===========================================================================
function CreateBottomCommandPanel takes nothing returns nothing
    local real panelWidth = SCREEN_WIDTH
    local real panelHeight = 0.15
    local integer i
    local integer buttonFrame
    local real buttonSize = 0.04
    local real startX = 0.02
    local real startY = 0.02
    
    // 创建底部面板
    set BottomPanel = DzCreateFrameByTagName("BACKDROP", "BottomPanel", AdaptiveContainer, "", 0)
    call DzFrameSetSize(BottomPanel, panelWidth, panelHeight)
    call DzFrameSetPoint(BottomPanel, 3, AdaptiveContainer, 3, 0.0, 0.0)  // 底部对齐
    
    // 设置背景纹理
    call DzFrameSetTexture(BottomPanel, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
    
    // 创建命令按钮网格 (4x3)
    set i = 0
    loop
        exitwhen i >= 12
        
        set buttonFrame = DzCreateFrameByTagName("BACKDROP", "CommandButton" + I2S(i), BottomPanel, "", 0)
        call DzFrameSetSize(buttonFrame, buttonSize, buttonSize)
        call DzFrameSetPoint(buttonFrame, 7, BottomPanel, 7, 
            startX + (i - (i / 4) * 4) * (buttonSize + 0.005), 
            startY + (i / 4) * (buttonSize + 0.005))
        
        // 设置按钮纹理
        call DzFrameSetTexture(buttonFrame, "ReplaceableTextures\\CommandButtons\\BTNCancel.blp", 0)
        
        set i = i + 1
    endloop
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[适配] 底部命令面板创建完成|r")
endfunction

//===========================================================================
// 创建左侧小地图
//===========================================================================
function CreateLeftMiniMap takes nothing returns nothing
    local real mapSize = 0.15
    
    // 创建小地图容器
    set MiniMapFrame = DzCreateFrameByTagName("BACKDROP", "MiniMapFrame", AdaptiveContainer, "", 0)
    call DzFrameSetSize(MiniMapFrame, mapSize, mapSize)
    call DzFrameSetPoint(MiniMapFrame, 7, AdaptiveContainer, 7, 0.02, 0.02)  // 左下角
    
    // 设置小地图背景
    call DzFrameSetTexture(MiniMapFrame, "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp", 0)
    
    // 创建小地图标题
    local integer mapTitle = DzCreateFrameByTagName("TEXT", "MapTitle", MiniMapFrame, "", 0)
    call DzFrameSetPoint(mapTitle, 2, MiniMapFrame, 2, 0.0, 0.01)
    call DzFrameSetFont(mapTitle, "Fonts\\FRIZQT__.ttf", 0.01, 0)
    call DzFrameSetText(mapTitle, "|cFFFFD700小地图|r")
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[适配] 左侧小地图创建完成|r")
endfunction

//===========================================================================
// 隐藏原生UI元素
//===========================================================================
function HideOriginalUI takes nothing returns nothing
    // 隐藏原生UI框架（需要根据实际情况调整）
    // 注意：这里需要使用DzAPI的隐藏功能
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[适配] 原生UI已隐藏|r")
endfunction

//===========================================================================
// 主初始化函数
//===========================================================================
function InitAdaptiveUI takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 开始初始化自适应UI系统 ===|r")
    
    // 1. 检测屏幕比例
    call DetectScreenRatio()
    
    // 2. 创建自适应容器
    call CreateAdaptiveContainer()
    
    if AdaptiveContainer == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] UI适配失败，容器创建失败|r")
        return
    endif
    
    // 3. 创建各UI组件
    call CreateTopResourcePanel()
    call CreateBottomCommandPanel()
    call CreateLeftMiniMap()
    
    // 4. 隐藏原生UI
    call HideOriginalUI()
    
    // 5. 标记适配完成
    set IsUIAdapted = true
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00=== UI自适应系统初始化完成 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700屏幕比例: " + R2S(ASPECT_RATIO) + " | 缩放因子: " + R2S(SCALE_FACTOR) + "|r")
endfunction

//===========================================================================
// 切换UI显示
//===========================================================================
function ToggleAdaptiveUI takes nothing returns nothing
    if IsUIAdapted then
        call DzFrameShow(AdaptiveContainer, not DzFrameIsVisible(AdaptiveContainer))
        
        if DzFrameIsVisible(AdaptiveContainer) then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00[适配] 自适应UI已显示|r")
        else
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00[适配] 自适应UI已隐藏|r")
        endif
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000[错误] UI系统未初始化|r")
    endif
endfunction
