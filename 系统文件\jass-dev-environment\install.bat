@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🎮 JASS Warcraft 3 Developer 安装器
echo ========================================
echo.

:: 检查Node.js
echo [1/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，请先安装Node.js 14.x或更高版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

:: 检查VS Code
echo.
echo [2/6] 检查VS Code环境...
code --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到VS Code，请先安装VS Code
    echo 下载地址: https://code.visualstudio.com/
    pause
    exit /b 1
)
echo ✅ VS Code环境正常

:: 安装依赖
echo.
echo [3/6] 安装项目依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

:: 编译TypeScript
echo.
echo [4/6] 编译TypeScript代码...
call npm run compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译完成

:: 安装vsce
echo.
echo [5/6] 安装扩展打包工具...
call npm install -g vsce
if %errorlevel% neq 0 (
    echo ⚠️ vsce安装失败，尝试手动安装: npm install -g vsce
)

:: 打包并安装扩展
echo.
echo [6/6] 打包并安装VS Code扩展...
call vsce package
if %errorlevel% neq 0 (
    echo ❌ 扩展打包失败
    pause
    exit /b 1
)

:: 查找生成的vsix文件
for %%f in (*.vsix) do (
    echo 正在安装扩展: %%f
    call code --install-extension "%%f"
    if %errorlevel% neq 0 (
        echo ❌ 扩展安装失败
        pause
        exit /b 1
    )
    echo ✅ 扩展安装成功
    goto :installed
)

echo ❌ 未找到生成的扩展文件
pause
exit /b 1

:installed
echo.
echo ========================================
echo           🎉 安装完成！
echo ========================================
echo.
echo 接下来的步骤:
echo 1. 重启VS Code
echo 2. 打开或创建.j文件
echo 3. 享受JASS开发体验！
echo.
echo 快捷键:
echo - Ctrl+Shift+V: 验证代码
echo - F5: 运行代码模拟
echo - 点击状态栏"JASS": 查看API参考
echo.
echo 配置文件位置: .vscode/settings.json
echo 推荐配置:
echo {
echo   "jass.warcraftVersion": "1.27",
echo   "jass.enableYDWEAPI": true,
echo   "jass.enableDZAPI": true
echo }
echo.
pause
