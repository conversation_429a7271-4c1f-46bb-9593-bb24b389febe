{"YDWE技能模板": {"prefix": "ydwe-skill", "body": ["function Trig_${1:技能名称}Actions takes nothing returns nothing", "    local timer ydl_timer", "    YDLocalInitialize()", "    ", "    // 获取基础单位", "    call YDLocal1Set(unit, \"a\", GetAttacker())", "    call YDLocal1Set(unit, \"b\", GetTriggerUnit())", "    ", "    // 获取技能等级", "    call YDLocal1Set(real, \"lv\", YDUserDataGet(unit, YDLocal1Get(unit, \"a\"),\"${2:技能名称}\", real))", "    ", "    // 计算伤害", "    call YDLocal1Set(real, \"sh\", (I2R(GetHeroAgi(YDLocal1Get(unit, \"a\"), true)) * ((YDLocal1Get(real, \"lv\") * ${3:2.40}) * (1.00 + YDUserDataGet(unit, YDLocal1Get(unit, \"a\"),\"技能伤害\", real)))))", "    ", "    // 技能效果", "    $0", "    ", "    // 清理资源", "    call YDLocal1Release()", "    set ydl_timer = null", "endfunction"], "description": "创建基于YDWE的技能函数模板"}, "DZAPI框架": {"prefix": "dz-frame", "body": ["local framehandle ydl_${1:frame}", "", "set ydl_${1:frame} = DzCreateFrameByTagName(\"${2:BACKDROP}\", \"${3:FrameName}\", DzGetGameUI(), \"\", 0)", "call DzFrameSetPoint(ydl_${1:frame}, FRAMEPOINT_${4:CENTER}, DzGetGameUI(), FRAMEPOINT_${4:CENTER}, ${5:0}, ${6:0})", "call DzFrameSetSize(ydl_${1:frame}, ${7:0.3}, ${8:0.2})", "call DzFrameShow(ydl_${1:frame}, true)", "", "set ydl_${1:frame} = null"], "description": "创建DZAPI UI框架"}, "YDLocal变量": {"prefix": "ydlocal", "body": ["call YDLocal1Set(${1:type}, \"${2:key}\", ${3:value})"], "description": "设置YDLocal变量"}, "YDUserData": {"prefix": "yduserdata", "body": ["call YDUserDataSet(${1:player}, ${2:key}, ${3:type}, ${4:value})"], "description": "设置用户数据"}, "局部变量声明": {"prefix": "local", "body": ["local ${1:type} ydl_${2:name}"], "description": "声明局部变量"}, "函数模板": {"prefix": "function", "body": ["function ${1:FunctionName} takes ${2:nothing} returns ${3:nothing}", "    $0", "endfunction"], "description": "基础函数模板"}, "循环模板": {"prefix": "loop", "body": ["loop", "    exitwhen ${1:condition}", "    $0", "endloop"], "description": "循环结构模板"}, "条件判断": {"prefix": "if", "body": ["if ${1:condition} then", "    $0", "endif"], "description": "条件判断模板"}, "调试输出": {"prefix": "debug", "body": ["call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, \"${1:调试信息}: \" + ${2:value})"], "description": "调试输出"}}