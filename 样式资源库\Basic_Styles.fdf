// 基础UI样式库 - 魔兽争霸3 1.24 + KK平台
// 适用于DzCreateFrameByTagName创建的框架

// 基础面板样式 - 不死族风格
Frame "BACKDROP" "BasicPanelStyle" {
    Width 0.4,
    Height 0.3,
    BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize 0.012,
    BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
    BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
}

// 基础面板样式 - 人族风格
Frame "BACKDROP" "HumanPanelStyle" {
    Width 0.4,
    Height 0.3,
    BackdropBackground "UI\Widgets\EscMenu\Human\human-options-menu-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize 0.012,
    BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
    BackdropEdgeFile "UI\Widgets\EscMenu\Human\human-tooltip-border.blp",
}

// 标准按钮样式
Frame "BUTTON" "StandardButtonStyle" {
    Width 0.06,
    Height 0.06,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
}

// 大按钮样式
Frame "BUTTON" "LargeButtonStyle" {
    Width 0.12,
    Height 0.08,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
}

// 小按钮样式
Frame "BUTTON" "SmallButtonStyle" {
    Width 0.04,
    Height 0.04,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
}

// 标准文本样式
Frame "TEXT" "StandardTextStyle" {
    FontShadowOffset 0.001 -0.001,
    FontShadowColor 0.0 0.0 0.0 1.0,
}

// 标题文本样式
Frame "TEXT" "TitleTextStyle" {
    FontShadowOffset 0.002 -0.002,
    FontShadowColor 0.0 0.0 0.0 1.0,
}

// 小文本样式
Frame "TEXT" "SmallTextStyle" {
    FontShadowOffset 0.0005 -0.0005,
    FontShadowColor 0.0 0.0 0.0 1.0,
}

// 按钮背景样式 - 默认
Frame "BACKDROP" "ButtonBackdropStyle" {
    BackdropBackground "ReplaceableTextures\CommandButtons\BTNCancel.blp",
    BackdropBlendAll,
}

// 按钮背景样式 - 高亮
Frame "BACKDROP" "ButtonHighlightStyle" {
    BackdropBackground "UI\Widgets\EscMenu\Human\quest-button-highlight.blp",
    BackdropBlendAll,
}

// 工具提示背景样式
Frame "BACKDROP" "TooltipBackdropStyle" {
    BackdropBackground "UI\Widgets\BattleNet\bnet-tooltip-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize 0.008,
    BackdropBackgroundInsets 0.002 0.002 0.002 0.002,
    BackdropEdgeFile "UI\Widgets\BattleNet\bnet-tooltip-border.blp",
}

// 滚动条样式
Frame "SCROLLBAR" "StandardScrollbarStyle" {
    Width 0.016,
    Height 0.2,
}

// 输入框样式
Frame "EDITBOX" "StandardEditboxStyle" {
    Width 0.2,
    Height 0.03,
    EditBorderSize 0.002,
}

// 列表框样式
Frame "LISTBOX" "StandardListboxStyle" {
    Width 0.25,
    Height 0.15,
}

// 滑块样式
Frame "SLIDER" "StandardSliderStyle" {
    Width 0.2,
    Height 0.02,
}

// 复选框样式
Frame "CHECKBOX" "StandardCheckboxStyle" {
    Width 0.02,
    Height 0.02,
}

// 进度条样式
Frame "STATUSBAR" "StandardProgressbarStyle" {
    Width 0.2,
    Height 0.02,
    StatusBarTexture "UI\Widgets\EscMenu\Human\human-progressbar-fill.blp",
}

// 分隔线样式
Frame "BACKDROP" "SeparatorStyle" {
    Width 0.3,
    Height 0.002,
    BackdropBackground "UI\Widgets\EscMenu\Human\human-separator.blp",
}

// 图标框架样式
Frame "BACKDROP" "IconFrameStyle" {
    Width 0.05,
    Height 0.05,
    BackdropBackground "UI\Widgets\Console\Human\human-console-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize 0.004,
}

// 透明背景样式
Frame "BACKDROP" "TransparentStyle" {
    BackdropBackground "",
    BackdropBlendAll,
}

// 半透明背景样式
Frame "BACKDROP" "SemiTransparentStyle" {
    BackdropBackground "UI\Widgets\EscMenu\Human\human-options-menu-background.blp",
    BackdropBlendAll,
    BackdropColor 1.0 1.0 1.0 0.5,
}

// 错误提示样式
Frame "TEXT" "ErrorTextStyle" {
    FontShadowOffset 0.001 -0.001,
    FontShadowColor 1.0 0.0 0.0 1.0,
    FontColor 1.0 0.2 0.2 1.0,
}

// 成功提示样式
Frame "TEXT" "SuccessTextStyle" {
    FontShadowOffset 0.001 -0.001,
    FontShadowColor 0.0 1.0 0.0 1.0,
    FontColor 0.2 1.0 0.2 1.0,
}

// 警告提示样式
Frame "TEXT" "WarningTextStyle" {
    FontShadowOffset 0.001 -0.001,
    FontShadowColor 1.0 1.0 0.0 1.0,
    FontColor 1.0 1.0 0.2 1.0,
}
