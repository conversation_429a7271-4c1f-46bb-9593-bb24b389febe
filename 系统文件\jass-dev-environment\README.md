# 🎮 JASS Warcraft 3 Developer

专为魔兽争霸3 JASS开发设计的VS Code扩展，完美支持1.27版本、YDWE API、DZAPI等中国定制环境。

## ✨ 功能特性

### 🔍 **智能代码分析**
- **实时语法检查** - 基于1.27版本JASS语法规则
- **错误诊断** - 精确定位语法错误和逻辑问题
- **警告提示** - 代码规范和最佳实践建议
- **变量命名检查** - 支持YDWE命名规范（ydl_、ydul_前缀）

### 🚀 **代码模拟器**
- **实时执行模拟** - 无需编译即可测试代码逻辑
- **变量状态跟踪** - 实时查看变量值变化
- **函数调用模拟** - 模拟YDWE API、DZAPI、原生JASS函数
- **执行步骤追踪** - 详细的代码执行流程

### 💡 **智能补全**
- **API函数补全** - 完整的YDWE、DZAPI函数库
- **参数提示** - 函数参数类型和说明
- **代码片段** - 基于你的编程模式的代码模板
- **变量补全** - 智能识别作用域内变量

### 📚 **API参考手册**
- **完整API文档** - YDWE、DZAPI、原生JASS函数
- **使用示例** - 基于你的实际代码模式
- **搜索功能** - 快速查找API函数
- **中文说明** - 完全中文化的文档

## 🛠️ 安装步骤

### 1. 环境准备
```bash
# 确保已安装Node.js (14.x或更高版本)
node --version

# 确保已安装VS Code
code --version
```

### 2. 安装扩展
```bash
# 进入扩展目录
cd jass-dev-environment

# 安装依赖
npm install

# 编译TypeScript
npm run compile

# 打包扩展
npm install -g vsce
vsce package

# 安装到VS Code
code --install-extension jass-warcraft3-dev-1.0.0.vsix
```

### 3. 配置扩展
在VS Code设置中配置：
```json
{
    "jass.warcraftVersion": "1.27",
    "jass.enableYDWEAPI": true,
    "jass.enableDZAPI": true,
    "jass.variableNamingConvention": "ydwe"
}
```

## 🎯 使用指南

### 创建JASS文件
1. 新建文件，保存为 `.j` 或 `.jass` 扩展名
2. VS Code会自动识别为JASS语言
3. 享受语法高亮和智能提示

### 代码验证
- **快捷键**: `Ctrl+Shift+V`
- **命令面板**: `JASS: 验证JASS代码`
- 实时错误提示会显示在问题面板

### 代码模拟
- **快捷键**: `F5`
- **命令面板**: `JASS: 运行代码模拟`
- 在侧边面板查看模拟结果

### API参考
- **状态栏**: 点击 `JASS` 按钮
- **命令面板**: `JASS: 显示API参考`

## 📖 代码示例

### 基础技能模板
```jass
function Trig_新技能Actions takes nothing returns nothing
    local timer ydl_timer
    YDLocalInitialize()
    
    // 获取基础单位
    call YDLocal1Set(unit, "a", GetAttacker())
    call YDLocal1Set(unit, "b", GetTriggerUnit())
    
    // 计算技能等级和伤害
    call YDLocal1Set(real, "lv", YDUserDataGet(unit, YDLocal1Get(unit, "a"),"技能名称", real))
    call YDLocal1Set(real, "sh", (I2R(GetHeroAgi(YDLocal1Get(unit, "a"), true)) * YDLocal1Get(real, "lv") * 2.40))
    
    // 显示伤害
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "伤害: " + R2S(YDLocal1Get(real, "sh")))
    
    // 清理资源
    call YDLocal1Release()
    set ydl_timer = null
endfunction
```

### DZAPI UI示例
```jass
function CreateCustomUI takes nothing returns nothing
    local framehandle ydl_frame
    
    // 创建UI框架
    set ydl_frame = DzCreateFrameByTagName("BACKDROP", "MyFrame", DzGetGameUI(), "", 0)
    call DzFrameSetPoint(ydl_frame, FRAMEPOINT_CENTER, DzGetGameUI(), FRAMEPOINT_CENTER, 0, 0)
    call DzFrameSetSize(ydl_frame, 0.3, 0.2)
    call DzFrameSetTexture(ydl_frame, "UI\\Widgets\\Console\\Human\\human-console-background.blp", 0)
    call DzFrameShow(ydl_frame, true)
    
    set ydl_frame = null
endfunction
```

## 🔧 高级功能

### 自定义代码片段
在 `.vscode/snippets/jass.json` 中添加：
```json
{
    "YDWE技能模板": {
        "prefix": "ydwe-skill",
        "body": [
            "function Trig_${1:技能名称}Actions takes nothing returns nothing",
            "    local timer ydl_timer",
            "    YDLocalInitialize()",
            "    $0",
            "    call YDLocal1Release()",
            "    set ydl_timer = null",
            "endfunction"
        ]
    }
}
```

### 调试配置
创建 `.vscode/launch.json`：
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "JASS模拟调试",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/simulator.js",
            "args": ["${file}"]
        }
    ]
}
```

## 🎨 主题配置

### 推荐的颜色主题设置
```json
{
    "editor.tokenColorCustomizations": {
        "textMateRules": [
            {
                "scope": "support.function.ydwe.jass",
                "settings": {
                    "foreground": "#4CAF50",
                    "fontStyle": "bold"
                }
            },
            {
                "scope": "support.function.dzapi.jass",
                "settings": {
                    "foreground": "#2196F3",
                    "fontStyle": "bold"
                }
            }
        ]
    }
}
```

## 🚀 性能优化

### 大文件处理
- 自动分块解析大型war3map.j文件
- 增量语法检查，只检查修改的部分
- 智能缓存，提高响应速度

### 内存管理
- 自动清理未使用的语法树
- 优化变量监控，避免内存泄漏
- 定期垃圾回收

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境搭建
```bash
git clone <repository>
cd jass-dev-environment
npm install
npm run watch
```

### 测试
```bash
npm test
```

## 📄 许可证

MIT License - 自由使用和修改

## 🙏 致谢

- 感谢YDWE团队提供的强大API
- 感谢魔兽争霸3中文社区的支持
- 基于用户实际项目需求开发

---

**🎮 让JASS开发更简单，让魔兽3地图制作更高效！**
