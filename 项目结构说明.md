# 项目结构规范

## 📁 目录结构

```
E:\WORLD EDIT\
├── 📄 722工作日志.md                   # 开发工作日志
├── 📄 项目结构说明.md                  # 本说明文档
├── 📄 Verified_JASS_Intelligence_System.md  # 经过验证的JASS智能约束系统
├── 📁 JASS技能代码\                     # JASS技能实现文件
│   ├── 📄 README.md                    # 技能文件说明
│   ├── 📄 fireball_skill.j             # 火球术弧形投射物技能
│   ├── 📄 crocodile_ultimate.j         # 变大持续伤害效果
│   ├── 📄 dark_arrow_rain.j            # 黑暗箭雨技能
│   └── 📄 BasicTest.j                  # 基础测试技能
├── 📁 UI界面模板\                      # UI模板文件
│   ├── 📄 README.md                    # UI模板说明
│   ├── 📄 Basic_Panel.j                # 基础面板模板
│   └── 📄 Button_Grid.j                # 按钮网格模板
├── 📁 样式资源库\                      # FDF样式库和TOC文件
│   ├── 📄 README.md                    # 样式库说明
│   ├── 📄 Basic_Styles.fdf             # 基础样式定义
│   └── 📄 UI_Basic.toc                 # 基础UI TOC
├── 📄 UI_Quick_Start_Template.j        # 快速启动UI模板
├── 📁 高手代码\                        # LLM生成的代码
│   ├── 📄 CreateMountainKing.j         # 山丘之王创建示例
│   ├── 📄 ai_generated_test.j          # AI生成测试代码
│   ├── 📄 Level_3_Test_Code_Master.j   # Level 3测试代码
│   └── 📄 leak_test.j                  # 内存泄漏测试
├── 📁 文档\                           # 知识库文档
│   ├── 📄 正确案例.md                  # 验证过的正确代码示例
│   ├── 📄 错误案例.md                  # 常见错误和解决方案
│   └── 📄 关键信息.md                  # 核心概念和重要发现
├── 📁 系统文件\                        # 系统和配置文件
│   ├── 📁 hayx\                        # JASS库文件
│   ├── 📁 jass编辑器库\                # YDWE/JAPI库
│   ├── 📁 jass-dev-environment\        # 开发环境
│   ├── 📁 jass-full-compiler\          # 编译器
│   └── 📄 *.md                         # 各种技术文档
└── 📁 .vscode\                         # VS Code配置
    └── 📁 snippets\                    # JASS代码片段
        └── 📄 jass.json                # 验证过的代码模板
```

## 📋 文件管理规范

### 🎯 根目录规则
- **只保留**：测试文件（*.j）和项目说明
- **禁止**：堆积大量文件，保持根目录整洁
- **命名**：测试文件使用描述性名称

### 🏆 高手代码文件夹
- **用途**：存放所有LLM生成的JASS代码
- **内容**：
  - AI助手编写的触发器代码
  - 示例代码和模板
  - 测试和验证代码
- **命名规范**：使用有意义的文件名，如`CreateMountainKing.j`

### 📚 文档文件夹
- **用途**：存放三个核心知识库文档
- **固定结构**：
  - `正确案例.md` - 验证过的工作代码
  - `错误案例.md` - 常见错误和调试方案
  - `关键信息.md` - 核心概念和重要发现
- **更新频率**：随着开发经验积累持续更新

### 🔧 系统文件文件夹
- **用途**：存放开发环境和库文件
- **内容**：
  - JASS库文件（hayx、jass编辑器库）
  - 开发工具（编译器、环境配置）
  - 技术文档和指南
- **维护**：定期更新，保持版本一致性

## 🔄 工作流程

### 📝 日常开发
1. **测试代码**：在根目录创建测试文件
2. **验证成功**：移动到"高手代码"文件夹
3. **记录经验**：更新"文档"文件夹中的相应文档
4. **清理根目录**：保持整洁

### 📖 知识管理
1. **成功案例**：记录到`正确案例.md`
2. **遇到错误**：分析并记录到`错误案例.md`
3. **重要发现**：添加到`关键信息.md`
4. **定期回顾**：整理和优化文档内容

### 🎯 新项目开始
1. **快速参考**：查看"文档"文件夹了解最佳实践
2. **代码复用**：从"高手代码"文件夹复制模板
3. **环境检查**：确认"系统文件"配置正确
4. **开始开发**：在根目录进行测试

## 🚀 优势

### ✅ 组织清晰
- 根目录整洁，便于快速测试
- 代码分类明确，便于查找和复用
- 文档结构化，便于知识积累

### ✅ 效率提升
- 快速找到需要的代码模板
- 避免重复犯同样的错误
- 新项目可以快速启动

### ✅ 知识传承
- 经验以文档形式保存
- 错误案例帮助避免陷阱
- 关键信息指导最佳实践

---
**建立日期**：2025-07-03  
**维护原则**：保持结构清晰，及时更新文档，持续优化组织方式
