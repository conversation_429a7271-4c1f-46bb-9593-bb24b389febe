# 🎯 JASS技能代码目录

## 📁 文件说明

本目录包含项目中所有的JASS技能实现文件。

### 🔥 技能列表

1. **fireball_skill.j** - 火球术弧形投射物技能
   - 贝塞尔曲线轨迹
   - 范围伤害效果
   - 定时器管理

2. **crocodile_ultimate.j** - 变大持续伤害效果
   - 单位变大效果
   - 持续伤害机制
   - 范围检测

3. **dark_arrow_rain.j** - 黑暗箭雨技能
   - 仿暴风雪效果
   - 多箭下落动画
   - 范围持续伤害

4. **BasicTest.j** - 基础测试技能
   - 简单技能模板
   - 测试用例

### 📋 技能开发规范

- 使用YDWE局部变量系统
- 包含完整的资源管理
- 遵循项目命名规范
- 包含详细注释说明

### 🔧 使用方法

1. 将技能文件导入到YDWE编辑器
2. 在地图初始化时调用InitTrig_技能名函数
3. 根据需要调整技能参数和效果

---
**维护日期**: 2025-07-23  
**文件数量**: 4个技能文件
