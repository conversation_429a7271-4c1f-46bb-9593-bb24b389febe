//===========================================================================
// 火球术 - 弧形投射物技能
//===========================================================================

// 全局变量声明
globals
    trigger gg_trg_Fireball = null
endglobals

function Trig_FireballTimer takes nothing returns nothing
    local timer ydl_timer = GetExpiredTimer()
    local unit caster = YDLocalGet(ydl_timer, unit, "caster")
    local unit target = YDLocalGet(ydl_timer, unit, "target")
    local effect projectile = YDLocalGet(ydl_timer, effect, "projectile")
    local real damage = YDLocalGet(ydl_timer, real, "damage")
    local real t = YDLocalGet(ydl_timer, real, "t")
    local real startX = YDLocalGet(ydl_timer, real, "startX")
    local real startY = YDLocalGet(ydl_timer, real, "startY")
    local real endX = YDLocalGet(ydl_timer, real, "endX")
    local real endY = YDLocalGet(ydl_timer, real, "endY")
    local real controlX = YDLocalGet(ydl_timer, real, "controlX")
    local real controlY = YDLocalGet(ydl_timer, real, "controlY")
    local real currentX
    local real currentY
    local real currentZ
    local group ydl_group
    local unit ydl_unit
    
    // 增加时间参数
    set t = t + 0.03
    call YDLocalSet(ydl_timer, real, "t", t)
    
    // 贝塞尔曲线计算当前位置
    set currentX = (1-t)*(1-t)*startX + 2*(1-t)*t*controlX + t*t*endX
    set currentY = (1-t)*(1-t)*startY + 2*(1-t)*t*controlY + t*t*endY
    set currentZ = 50.0 + t * 100.0  // 火球飞行高度，略微上升
    
    // 移动火球特效
    call EXSetEffectXY(projectile, currentX, currentY)
    call EXSetEffectZ(projectile, currentZ)
    
    // 检查是否到达目标附近50码
    if SquareRoot((currentX-endX)*(currentX-endX) + (currentY-endY)*(currentY-endY)) <= 50.0 or t >= 1.0 then
        // 创建爆炸特效
        call YDWETimerDestroyEffect(2.0, AddSpecialEffect("Abilities\\Spells\\Other\\Incinerate\\FireLordDeathExplode.mdl", currentX, currentY))
        
        // 范围伤害
        set ydl_group = CreateGroup()
        call GroupEnumUnitsInRange(ydl_group, currentX, currentY, 200.0, null)
        loop
            set ydl_unit = FirstOfGroup(ydl_group)
            exitwhen ydl_unit == null
            call GroupRemoveUnit(ydl_group, ydl_unit)
            
            if IsUnitEnemy(ydl_unit, GetOwningPlayer(caster)) and GetUnitState(ydl_unit, UNIT_STATE_LIFE) > 0.405 and not IsUnitType(ydl_unit, UNIT_TYPE_STRUCTURE) then
                call UnitDamageTarget(caster, ydl_unit, damage, true, false, ATTACK_TYPE_CHAOS, DAMAGE_TYPE_MAGIC, WEAPON_TYPE_WHOKNOWS)
                // 单体命中特效
                call YDWETimerDestroyEffect(1.0, AddSpecialEffect("Abilities\\Spells\\Human\\FlameStrike\\FlameStrike1.mdl", GetUnitX(ydl_unit), GetUnitY(ydl_unit)))
            endif
        endloop
        call DestroyGroup(ydl_group)
        
        // 销毁火球特效
        call DestroyEffect(projectile)
        
        // 清理定时器
        call YDLocalClear(ydl_timer)
        call DestroyTimer(ydl_timer)
    endif
    
    set ydl_timer = null
    set caster = null
    set target = null
    set projectile = null
    set ydl_group = null
    set ydl_unit = null
endfunction

function Trig_FireballActions takes nothing returns nothing
    local timer ydl_timer
    
    YDLocalInitialize()
    
    // 1. 获取基础单位
    call YDLocal1Set(unit, "caster", GetAttacker())
    call YDLocal1Set(unit, "target", GetTriggerUnit())
    
    // 2. 获取技能等级
    call YDLocal1Set(real, "skillLevel", YDUserDataGet(unit, YDLocal1Get(unit, "caster"), "火球术", real))
    call YDLocal1Set(real, "heroAgi", I2R(GetHeroAgi(YDLocal1Get(unit, "caster"), true)))
    call YDLocal1Set(real, "skillDamage", YDUserDataGet(unit, YDLocal1Get(unit, "caster"), "技能伤害", real))
    call YDLocal1Set(real, "critChance", YDUserDataGet(unit, YDLocal1Get(unit, "caster"), "暴击几率", real))
    call YDLocal1Set(real, "critDamage", YDUserDataGet(unit, YDLocal1Get(unit, "caster"), "暴击伤害", real))
    
    // 3. 计算伤害
    call YDLocal1Set(real, "baseDamage", (YDLocal1Get(real, "heroAgi") * 3.0 * YDLocal1Get(real, "skillLevel")))
    call YDLocal1Set(real, "finalDamage", (YDLocal1Get(real, "baseDamage") * (1.00 + YDLocal1Get(real, "skillDamage"))))
    
    // 4. 判断暴击
    if ((GetRandomReal(1.00, 100.00) <= YDLocal1Get(real, "critChance"))) then
        call YDLocal1Set(real, "finalDamage", (YDLocal1Get(real, "finalDamage") * YDLocal1Get(real, "critDamage")))
    endif
    
    // 5. 计算弧形轨迹
    call YDLocal1Set(real, "startX", GetUnitX(YDLocal1Get(unit, "caster")))
    call YDLocal1Set(real, "startY", GetUnitY(YDLocal1Get(unit, "caster")))
    call YDLocal1Set(real, "endX", GetUnitX(YDLocal1Get(unit, "target")))
    call YDLocal1Set(real, "endY", GetUnitY(YDLocal1Get(unit, "target")))
    
    // 计算控制点（弧形轨迹的顶点）
    call YDLocal1Set(real, "midX", (YDLocal1Get(real, "startX") + YDLocal1Get(real, "endX")) / 2.0)
    call YDLocal1Set(real, "midY", (YDLocal1Get(real, "startY") + YDLocal1Get(real, "endY")) / 2.0)
    call YDLocal1Set(real, "distance", SquareRoot((YDLocal1Get(real, "endX") - YDLocal1Get(real, "startX")) * (YDLocal1Get(real, "endX") - YDLocal1Get(real, "startX")) + (YDLocal1Get(real, "endY") - YDLocal1Get(real, "startY")) * (YDLocal1Get(real, "endY") - YDLocal1Get(real, "startY"))))
    call YDLocal1Set(real, "arcHeight", YDLocal1Get(real, "distance") * 0.3)  // 弧形高度为距离的30%
    
    // 计算垂直于连线的方向向量
    call YDLocal1Set(real, "dirX", YDLocal1Get(real, "endY") - YDLocal1Get(real, "startY"))
    call YDLocal1Set(real, "dirY", YDLocal1Get(real, "startX") - YDLocal1Get(real, "endX"))
    call YDLocal1Set(real, "dirLength", SquareRoot(YDLocal1Get(real, "dirX") * YDLocal1Get(real, "dirX") + YDLocal1Get(real, "dirY") * YDLocal1Get(real, "dirY")))
    
    if YDLocal1Get(real, "dirLength") > 0 then
        call YDLocal1Set(real, "dirX", YDLocal1Get(real, "dirX") / YDLocal1Get(real, "dirLength"))
        call YDLocal1Set(real, "dirY", YDLocal1Get(real, "dirY") / YDLocal1Get(real, "dirLength"))
    endif
    
    // 控制点位置
    call YDLocal1Set(real, "controlX", YDLocal1Get(real, "midX") + YDLocal1Get(real, "dirX") * YDLocal1Get(real, "arcHeight"))
    call YDLocal1Set(real, "controlY", YDLocal1Get(real, "midY") + YDLocal1Get(real, "dirY") * YDLocal1Get(real, "arcHeight"))
    
    // 6. 创建火球特效
    call YDLocal1Set(effect, "projectile", AddSpecialEffect("Abilities\\Spells\\Other\\Incinerate\\IncinerateBuff.mdl", YDLocal1Get(real, "startX"), YDLocal1Get(real, "startY")))
    call EXSetEffectZ(YDLocal1Get(effect, "projectile"), 50.0)
    
    // 7. 创建定时器并绑定数据
    set ydl_timer = CreateTimer()
    call YDLocalSet(ydl_timer, unit, "caster", YDLocal1Get(unit, "caster"))
    call YDLocalSet(ydl_timer, unit, "target", YDLocal1Get(unit, "target"))
    call YDLocalSet(ydl_timer, effect, "projectile", YDLocal1Get(effect, "projectile"))
    call YDLocalSet(ydl_timer, real, "damage", YDLocal1Get(real, "finalDamage"))
    call YDLocalSet(ydl_timer, real, "t", 0.0)
    call YDLocalSet(ydl_timer, real, "startX", YDLocal1Get(real, "startX"))
    call YDLocalSet(ydl_timer, real, "startY", YDLocal1Get(real, "startY"))
    call YDLocalSet(ydl_timer, real, "endX", YDLocal1Get(real, "endX"))
    call YDLocalSet(ydl_timer, real, "endY", YDLocal1Get(real, "endY"))
    call YDLocalSet(ydl_timer, real, "controlX", YDLocal1Get(real, "controlX"))
    call YDLocalSet(ydl_timer, real, "controlY", YDLocal1Get(real, "controlY"))
    call TimerStart(ydl_timer, 0.03, true, function Trig_FireballTimer)
    
    // 8. 施法特效
    call YDWETimerDestroyEffect(1.0, AddSpecialEffectTarget("Abilities\\Spells\\Other\\Incinerate\\IncinerateCaster.mdl", YDLocal1Get(unit, "caster"), "hand"))
    
    call YDLocal1Release()
    set ydl_timer = null
endfunction

function InitTrig_Fireball takes nothing returns nothing
    set gg_trg_Fireball = CreateTrigger()
    #ifdef DEBUG
        call YDWESaveTriggerName(gg_trg_Fireball, "Fireball")
    #endif
    call TriggerRegisterAnyUnitEventBJ(gg_trg_Fireball, EVENT_PLAYER_UNIT_ATTACKED)
    call TriggerAddAction(gg_trg_Fireball, function Trig_FireballActions)
endfunction
