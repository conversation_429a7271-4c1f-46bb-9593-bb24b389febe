import { Cst<PERSON>arser, IToken } from 'chevrotain';
import { allTokens, Function, EndFunction, Takes, Returns, Nothing, Local, Set, Call, Return, If, Then, <PERSON><PERSON>, <PERSON>seIf, EndIf, Loop, EndLoop, ExitWhen, Globals, EndGlobals, Constant, Array, Type, Extends, Native, Debug, And, Or, Not, IntegerType, RealType, BooleanType, StringType, HandleType, CodeType, UnitType, PlayerType, LocationType, RectType, RegionType, TimerType, TriggerType, GroupType, ForceType, EffectType, SoundType, FrameHandleType, True, False, Null, Identifier, IntegerLiteral, RealLiteral, StringLiteral, RawCode, Plus, Minus, Multiply, Divide, Modulo, Equal, NotEqual, LessThan, LessEqual, GreaterThan, GreaterEqual, Assign, LeftParen, RightParen, LeftBracket, RightBracket, Comma, Dot } from './JassLexer';

export class JassParser extends CstParser {
    constructor() {
        super(allTokens);
        this.performSelfAnalysis();
    }

    // 程序入口点
    public program = this.RULE('program', () => {
        this.MANY(() => {
            this.OR([
                { ALT: () => this.SUBRULE(this.globalDeclaration) },
                { ALT: () => this.SUBRULE(this.functionDeclaration) },
                { ALT: () => this.SUBRULE(this.nativeDeclaration) },
                { ALT: () => this.SUBRULE(this.typeDeclaration) }
            ]);
        });
    });

    // 全局变量声明块
    public globalDeclaration = this.RULE('globalDeclaration', () => {
        this.CONSUME(Globals);
        this.MANY(() => {
            this.SUBRULE(this.globalVariable);
        });
        this.CONSUME(EndGlobals);
    });

    // 全局变量
    public globalVariable = this.RULE('globalVariable', () => {
        this.OPTION(() => {
            this.CONSUME(Constant);
        });
        this.SUBRULE(this.typeReference);
        this.OPTION2(() => {
            this.CONSUME(Array);
        });
        this.CONSUME(Identifier);
        this.OPTION3(() => {
            this.CONSUME(Assign);
            this.SUBRULE(this.expression);
        });
    });

    // 函数声明
    public functionDeclaration = this.RULE('functionDeclaration', () => {
        this.OPTION(() => {
            this.CONSUME(Constant);
        });
        this.CONSUME(Function);
        this.CONSUME(Identifier);
        this.CONSUME(Takes);
        this.SUBRULE(this.parameterList);
        this.CONSUME(Returns);
        this.SUBRULE(this.typeReference);
        this.MANY(() => {
            this.SUBRULE(this.statement);
        });
        this.CONSUME(EndFunction);
    });

    // 参数列表
    public parameterList = this.RULE('parameterList', () => {
        this.OR([
            { ALT: () => this.CONSUME(Nothing) },
            { ALT: () => {
                this.SUBRULE(this.parameter);
                this.MANY(() => {
                    this.CONSUME(Comma);
                    this.SUBRULE2(this.parameter);
                });
            }}
        ]);
    });

    // 参数
    public parameter = this.RULE('parameter', () => {
        this.SUBRULE(this.typeReference);
        this.CONSUME(Identifier);
    });

    // 原生函数声明
    public nativeDeclaration = this.RULE('nativeDeclaration', () => {
        this.OPTION(() => {
            this.CONSUME(Constant);
        });
        this.CONSUME(Native);
        this.CONSUME(Identifier);
        this.CONSUME(Takes);
        this.SUBRULE(this.parameterList);
        this.CONSUME(Returns);
        this.SUBRULE(this.typeReference);
    });

    // 类型声明
    public typeDeclaration = this.RULE('typeDeclaration', () => {
        this.CONSUME(Type);
        this.CONSUME(Identifier);
        this.CONSUME(Extends);
        this.SUBRULE(this.typeReference);
    });

    // 类型引用
    public typeReference = this.RULE('typeReference', () => {
        this.OR([
            { ALT: () => this.CONSUME(IntegerType) },
            { ALT: () => this.CONSUME(RealType) },
            { ALT: () => this.CONSUME(BooleanType) },
            { ALT: () => this.CONSUME(StringType) },
            { ALT: () => this.CONSUME(HandleType) },
            { ALT: () => this.CONSUME(CodeType) },
            { ALT: () => this.CONSUME(UnitType) },
            { ALT: () => this.CONSUME(PlayerType) },
            { ALT: () => this.CONSUME(LocationType) },
            { ALT: () => this.CONSUME(RectType) },
            { ALT: () => this.CONSUME(RegionType) },
            { ALT: () => this.CONSUME(TimerType) },
            { ALT: () => this.CONSUME(TriggerType) },
            { ALT: () => this.CONSUME(GroupType) },
            { ALT: () => this.CONSUME(ForceType) },
            { ALT: () => this.CONSUME(EffectType) },
            { ALT: () => this.CONSUME(SoundType) },
            { ALT: () => this.CONSUME(FrameHandleType) },
            { ALT: () => this.CONSUME(Nothing) },
            { ALT: () => this.CONSUME(Identifier) }
        ]);
    });

    // 语句
    public statement = this.RULE('statement', () => {
        this.OR([
            { ALT: () => this.SUBRULE(this.localDeclaration) },
            { ALT: () => this.SUBRULE(this.setStatement) },
            { ALT: () => this.SUBRULE(this.callStatement) },
            { ALT: () => this.SUBRULE(this.returnStatement) },
            { ALT: () => this.SUBRULE(this.ifStatement) },
            { ALT: () => this.SUBRULE(this.loopStatement) },
            { ALT: () => this.SUBRULE(this.exitWhenStatement) }
        ]);
    });

    // 局部变量声明
    public localDeclaration = this.RULE('localDeclaration', () => {
        this.CONSUME(Local);
        this.SUBRULE(this.typeReference);
        this.OPTION(() => {
            this.CONSUME(Array);
        });
        this.CONSUME(Identifier);
        this.OPTION2(() => {
            this.CONSUME(Assign);
            this.SUBRULE(this.expression);
        });
    });

    // set语句
    public setStatement = this.RULE('setStatement', () => {
        this.CONSUME(Set);
        this.SUBRULE(this.variableReference);
        this.CONSUME(Assign);
        this.SUBRULE(this.expression);
    });

    // call语句
    public callStatement = this.RULE('callStatement', () => {
        this.CONSUME(Call);
        this.SUBRULE(this.functionCall);
    });

    // return语句
    public returnStatement = this.RULE('returnStatement', () => {
        this.CONSUME(Return);
        this.OPTION(() => {
            this.SUBRULE(this.expression);
        });
    });

    // if语句
    public ifStatement = this.RULE('ifStatement', () => {
        this.CONSUME(If);
        this.SUBRULE(this.expression);
        this.CONSUME(Then);
        this.MANY(() => {
            this.SUBRULE(this.statement);
        });
        this.MANY2(() => {
            this.CONSUME(ElseIf);
            this.SUBRULE2(this.expression);
            this.CONSUME2(Then);
            this.MANY3(() => {
                this.SUBRULE2(this.statement);
            });
        });
        this.OPTION(() => {
            this.CONSUME(Else);
            this.MANY4(() => {
                this.SUBRULE3(this.statement);
            });
        });
        this.CONSUME(EndIf);
    });

    // loop语句
    public loopStatement = this.RULE('loopStatement', () => {
        this.CONSUME(Loop);
        this.MANY(() => {
            this.SUBRULE(this.statement);
        });
        this.CONSUME(EndLoop);
    });

    // exitwhen语句
    public exitWhenStatement = this.RULE('exitWhenStatement', () => {
        this.CONSUME(ExitWhen);
        this.SUBRULE(this.expression);
    });

    // 变量引用
    public variableReference = this.RULE('variableReference', () => {
        this.CONSUME(Identifier);
        this.OPTION(() => {
            this.CONSUME(LeftBracket);
            this.SUBRULE(this.expression);
            this.CONSUME(RightBracket);
        });
    });

    // 函数调用
    public functionCall = this.RULE('functionCall', () => {
        this.CONSUME(Identifier);
        this.CONSUME(LeftParen);
        this.OPTION(() => {
            this.SUBRULE(this.argumentList);
        });
        this.CONSUME(RightParen);
    });

    // 参数列表
    public argumentList = this.RULE('argumentList', () => {
        this.SUBRULE(this.expression);
        this.MANY(() => {
            this.CONSUME(Comma);
            this.SUBRULE2(this.expression);
        });
    });

    // 表达式
    public expression = this.RULE('expression', () => {
        this.SUBRULE(this.orExpression);
    });

    // 或表达式
    public orExpression = this.RULE('orExpression', () => {
        this.SUBRULE(this.andExpression);
        this.MANY(() => {
            this.CONSUME(Or);
            this.SUBRULE2(this.andExpression);
        });
    });

    // 与表达式
    public andExpression = this.RULE('andExpression', () => {
        this.SUBRULE(this.equalityExpression);
        this.MANY(() => {
            this.CONSUME(And);
            this.SUBRULE2(this.equalityExpression);
        });
    });

    // 相等表达式
    public equalityExpression = this.RULE('equalityExpression', () => {
        this.SUBRULE(this.relationalExpression);
        this.MANY(() => {
            this.OR([
                { ALT: () => this.CONSUME(Equal) },
                { ALT: () => this.CONSUME(NotEqual) }
            ]);
            this.SUBRULE2(this.relationalExpression);
        });
    });

    // 关系表达式
    public relationalExpression = this.RULE('relationalExpression', () => {
        this.SUBRULE(this.additiveExpression);
        this.MANY(() => {
            this.OR([
                { ALT: () => this.CONSUME(LessThan) },
                { ALT: () => this.CONSUME(LessEqual) },
                { ALT: () => this.CONSUME(GreaterThan) },
                { ALT: () => this.CONSUME(GreaterEqual) }
            ]);
            this.SUBRULE2(this.additiveExpression);
        });
    });

    // 加法表达式
    public additiveExpression = this.RULE('additiveExpression', () => {
        this.SUBRULE(this.multiplicativeExpression);
        this.MANY(() => {
            this.OR([
                { ALT: () => this.CONSUME(Plus) },
                { ALT: () => this.CONSUME(Minus) }
            ]);
            this.SUBRULE2(this.multiplicativeExpression);
        });
    });

    // 乘法表达式
    public multiplicativeExpression = this.RULE('multiplicativeExpression', () => {
        this.SUBRULE(this.unaryExpression);
        this.MANY(() => {
            this.OR([
                { ALT: () => this.CONSUME(Multiply) },
                { ALT: () => this.CONSUME(Divide) },
                { ALT: () => this.CONSUME(Modulo) }
            ]);
            this.SUBRULE2(this.unaryExpression);
        });
    });

    // 一元表达式
    public unaryExpression = this.RULE('unaryExpression', () => {
        this.OR([
            { ALT: () => {
                this.OR2([
                    { ALT: () => this.CONSUME(Not) },
                    { ALT: () => this.CONSUME(Minus) },
                    { ALT: () => this.CONSUME(Plus) }
                ]);
                this.SUBRULE(this.unaryExpression);
            }},
            { ALT: () => this.SUBRULE(this.primaryExpression) }
        ]);
    });

    // 基本表达式
    public primaryExpression = this.RULE('primaryExpression', () => {
        this.OR([
            { ALT: () => this.CONSUME(IntegerLiteral) },
            { ALT: () => this.CONSUME(RealLiteral) },
            { ALT: () => this.CONSUME(StringLiteral) },
            { ALT: () => this.CONSUME(RawCode) },
            { ALT: () => this.CONSUME(True) },
            { ALT: () => this.CONSUME(False) },
            { ALT: () => this.CONSUME(Null) },
            { ALT: () => this.SUBRULE(this.variableReference) },
            { ALT: () => this.SUBRULE(this.functionCall) },
            { ALT: () => {
                this.CONSUME(LeftParen);
                this.SUBRULE(this.expression);
                this.CONSUME(RightParen);
            }}
        ]);
    });
}
