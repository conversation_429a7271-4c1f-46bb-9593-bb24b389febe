//===========================================================================
// 1.27版本宽屏UI适配器 - 移动原生UI而非重建
// 基于HIVE教程的正确方法
//===========================================================================

globals
    trigger gg_trg_WidescreenUI = null

    // 屏幕参数
    real SCREEN_RATIO = 1.333
    real SCALE_FACTOR = 1.0
    boolean IsAdapted = false

    // 原生UI句柄（使用专门的获取函数）
    integer Portrait = 0
    integer Minimap = 0
    integer CommandButton0 = 0
    integer HeroButton0 = 0
    integer ItemButton0 = 0
    integer ChatMessage = 0
endglobals

//===========================================================================
// 检测屏幕比例
//===========================================================================
function DetectScreenRatio takes nothing returns nothing
    // 由于DzGetClientWidth可能不存在，先用固定值测试
    // 后续可以根据实际API调整
    set SCREEN_RATIO = 1.777  // 假设16:9宽屏
    
    if SCREEN_RATIO > 1.6 then
        set SCALE_FACTOR = 1.333 / SCREEN_RATIO
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700[检测] 宽屏模式，缩放因子: " + R2S(SCALE_FACTOR) + "|r")
    else
        set SCALE_FACTOR = 1.0
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB[检测] 标准比例，无需缩放|r")
    endif
endfunction

//===========================================================================
// 获取原生UI框架（使用专门的DzAPI函数）
//===========================================================================
function GetNativeFrames takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 获取原生UI框架 ===|r")

    // 获取肖像框架
    set Portrait = DzFrameGetPortrait()
    if Portrait != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ Portrait: " + I2S(Portrait) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ Portrait 获取失败|r")
    endif

    // 获取小地图
    set Minimap = DzFrameGetMinimap()
    if Minimap != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ Minimap: " + I2S(Minimap) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ Minimap 获取失败|r")
    endif

    // 获取命令按钮（第0行第0列）
    set CommandButton0 = DzFrameGetCommandBarButton(0, 0)
    if CommandButton0 != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ CommandButton(0,0): " + I2S(CommandButton0) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600? CommandButton(0,0) 未找到|r")
    endif

    // 获取英雄按钮
    set HeroButton0 = DzFrameGetHeroBarButton(0)
    if HeroButton0 != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ HeroButton(0): " + I2S(HeroButton0) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600? HeroButton(0) 未找到|r")
    endif

    // 获取物品按钮
    set ItemButton0 = DzFrameGetItemBarButton(0)
    if ItemButton0 != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ ItemButton(0): " + I2S(ItemButton0) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600? ItemButton(0) 未找到|r")
    endif

    // 获取聊天消息框架
    set ChatMessage = DzFrameGetChatMessage()
    if ChatMessage != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ ChatMessage: " + I2S(ChatMessage) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600? ChatMessage 未找到|r")
    endif
endfunction

//===========================================================================
// 适配宽屏布局
//===========================================================================
function AdaptWidescreenLayout takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 开始宽屏适配 ===|r")

    // 启用宽屏模式（防止拉伸）
    call DzEnableWideScreen(true)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 启用宽屏模式|r")

    // 编辑黑边（移除上下黑边）
    call DzFrameEditBlackBorders(0.0, 0.0)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 移除黑边|r")

    // 检测屏幕比例
    call DetectScreenRatio()

    // 获取原生框架
    call GetNativeFrames()

    // 方法1：缩放小地图
    if Minimap != 0 then
        call DzFrameSetScale(Minimap, SCALE_FACTOR)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 缩放小地图: " + R2S(SCALE_FACTOR) + "|r")
    endif

    // 方法2：移动命令按钮（基于HIVE教程的移动方法）
    if CommandButton0 != 0 then
        call DzFrameClearAllPoints(CommandButton0)
        call DzFrameSetPoint(CommandButton0, 4, DzGetGameUI(), 4, -0.1, -0.1)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 移动了命令按钮|r")
    endif

    // 方法3：移动肖像框架
    if Portrait != 0 then
        call DzFrameClearAllPoints(Portrait)
        call DzFrameSetPoint(Portrait, 7, DzGetGameUI(), 7, 0.05, 0.05)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 移动了肖像框架|r")
    endif

    set IsAdapted = true
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00=== 宽屏适配完成 ===|r")
endfunction

//===========================================================================
// 恢复原始布局
//===========================================================================
function RestoreOriginalLayout takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 恢复原始布局 ===|r")

    // 禁用宽屏模式
    call DzEnableWideScreen(false)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 禁用宽屏模式|r")

    // 恢复黑边
    call DzFrameEditBlackBorders(0.1, 0.1)
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 恢复黑边|r")

    // 恢复小地图缩放
    if Minimap != 0 then
        call DzFrameSetScale(Minimap, 1.0)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 恢复小地图缩放|r")
    endif

    // 恢复命令按钮位置（需要重新设置到原始位置）
    if CommandButton0 != 0 then
        call DzFrameClearAllPoints(CommandButton0)
        // 这里需要设置回原始位置，暂时用默认值
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 恢复命令按钮位置|r")
    endif

    // 恢复肖像框架位置
    if Portrait != 0 then
        call DzFrameClearAllPoints(Portrait)
        // 这里需要设置回原始位置，暂时用默认值
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 恢复肖像框架位置|r")
    endif

    set IsAdapted = false
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00=== 原始布局已恢复 ===|r")
endfunction

//===========================================================================
// 切换适配状态
//===========================================================================
function ToggleAdaptation takes nothing returns nothing
    if IsAdapted then
        call RestoreOriginalLayout()
    else
        call AdaptWidescreenLayout()
    endif
endfunction

//===========================================================================
// 基础API测试
//===========================================================================
function TestBasicAPI takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 基础API测试 ===|r")

    // 测试DzGetGameUI
    if DzGetGameUI() != 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ DzGetGameUI 可用: " + I2S(DzGetGameUI()) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ DzGetGameUI 不可用|r")
        return
    endif

    // 测试获取框架
    call GetNativeFrames()

    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 基础测试完成|r")
endfunction

//===========================================================================
// 列出可用框架（使用专门的获取函数）
//===========================================================================
function ListAvailableFrames takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 搜索可用框架 ===|r")

    // 直接调用获取函数测试
    call GetNativeFrames()

    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 框架搜索完成 ===|r")
endfunction

//===========================================================================
// 命令处理
//===========================================================================
function HandleCommands takes nothing returns nothing
    local string command = GetEventPlayerChatString()
    
    if command == "-test" then
        call TestBasicAPI()
    elseif command == "-adapt" then
        call AdaptWidescreenLayout()
    elseif command == "-restore" then
        call RestoreOriginalLayout()
    elseif command == "-toggle" then
        call ToggleAdaptation()
    elseif command == "-list" then
        call ListAvailableFrames()
    elseif command == "-widescreen" then
        call DzEnableWideScreen(true)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 启用宽屏模式|r")
    elseif command == "-fullscreen" then
        call DzEnableWideScreen(false)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 禁用宽屏模式|r")
    elseif command == "-noborder" then
        call DzFrameEditBlackBorders(0.0, 0.0)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 移除黑边|r")
    elseif command == "-help" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 宽屏UI适配器命令 ===|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-test       基础API测试|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-adapt      应用宽屏适配|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-restore    恢复原始布局|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-toggle     切换适配状态|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-list       列出可用框架|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-widescreen 启用宽屏模式|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-fullscreen 禁用宽屏模式|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-noborder   移除黑边|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-help       显示帮助|r")
    endif
endfunction

//===========================================================================
// 初始化函数
//===========================================================================
function InitTrig_WidescreenUI takes nothing returns nothing
    set gg_trg_WidescreenUI = CreateTrigger()
    
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-test", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-adapt", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-restore", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-toggle", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-list", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-help", true)
    
    call TriggerAddAction(gg_trg_WidescreenUI, function HandleCommands)
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 宽屏UI适配器已加载 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -help 查看命令|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700建议先输入 -test 测试环境|r")
endfunction

//===========================================================================
// 主函数
//===========================================================================
function main takes nothing returns nothing
    call InitTrig_WidescreenUI()
endfunction
