//===========================================================================
// 1.27版本宽屏UI适配器 - 移动原生UI而非重建
// 基于HIVE教程的正确方法
//===========================================================================

globals
    trigger gg_trg_WidescreenUI = null
    
    // 屏幕参数
    real SCREEN_RATIO = 1.333
    real SCALE_FACTOR = 1.0
    boolean IsAdapted = false
    
    // 原生UI句柄
    framehandle ConsoleUI = null
    framehandle CommandPanel = null
    framehandle ResourceBar = null
endglobals

//===========================================================================
// 检测屏幕比例
//===========================================================================
function DetectScreenRatio takes nothing returns nothing
    // 由于DzGetClientWidth可能不存在，先用固定值测试
    // 后续可以根据实际API调整
    set SCREEN_RATIO = 1.777  // 假设16:9宽屏
    
    if SCREEN_RATIO > 1.6 then
        set SCALE_FACTOR = 1.333 / SCREEN_RATIO
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700[检测] 宽屏模式，缩放因子: " + R2S(SCALE_FACTOR) + "|r")
    else
        set SCALE_FACTOR = 1.0
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB[检测] 标准比例，无需缩放|r")
    endif
endfunction

//===========================================================================
// 获取原生UI框架
//===========================================================================
function GetNativeFrames takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 获取原生UI框架 ===|r")
    
    // 获取主控制台
    set ConsoleUI = DzGetFrameByName("ConsoleUI", 0)
    if ConsoleUI != null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ ConsoleUI: " + I2S(ConsoleUI) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ ConsoleUI 获取失败|r")
    endif
    
    // 获取资源栏
    set ResourceBar = DzGetFrameByName("ResourceBarFrame", 0)
    if ResourceBar != null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ ResourceBarFrame: " + I2S(ResourceBar) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600? ResourceBarFrame 未找到，尝试其他名称|r")
    endif
    
    // 尝试获取命令面板
    set CommandPanel = DzGetFrameByName("CommandButton", 0)
    if CommandPanel != null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ CommandButton: " + I2S(CommandPanel) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600? CommandButton 未找到|r")
    endif
endfunction

//===========================================================================
// 适配宽屏布局
//===========================================================================
function AdaptWidescreenLayout takes nothing returns nothing
    local framehandle backdrop
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 开始宽屏适配 ===|r")
    
    // 检测屏幕比例
    call DetectScreenRatio()
    
    // 获取原生框架
    call GetNativeFrames()
    
    // 方法1：隐藏黑色背景条（基于HIVE教程）
    set backdrop = DzGetFrameByName("ConsoleUIBackdrop", 0)
    if backdrop != null then
        call DzFrameShow(backdrop, false)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 隐藏了ConsoleUIBackdrop|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600? ConsoleUIBackdrop 未找到|r")
    endif
    
    // 方法2：尝试缩放整个ConsoleUI
    if ConsoleUI != null then
        call DzFrameSetScale(ConsoleUI, SCALE_FACTOR)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 缩放ConsoleUI: " + R2S(SCALE_FACTOR) + "|r")
    endif
    
    set IsAdapted = true
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00=== 宽屏适配完成 ===|r")
endfunction

//===========================================================================
// 恢复原始布局
//===========================================================================
function RestoreOriginalLayout takes nothing returns nothing
    local framehandle backdrop
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 恢复原始布局 ===|r")
    
    // 恢复背景条
    set backdrop = DzGetFrameByName("ConsoleUIBackdrop", 0)
    if backdrop != null then
        call DzFrameShow(backdrop, true)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 恢复了ConsoleUIBackdrop|r")
    endif
    
    // 恢复缩放
    if ConsoleUI != null then
        call DzFrameSetScale(ConsoleUI, 1.0)
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 恢复ConsoleUI缩放|r")
    endif
    
    set IsAdapted = false
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00=== 原始布局已恢复 ===|r")
endfunction

//===========================================================================
// 切换适配状态
//===========================================================================
function ToggleAdaptation takes nothing returns nothing
    if IsAdapted then
        call RestoreOriginalLayout()
    else
        call AdaptWidescreenLayout()
    endif
endfunction

//===========================================================================
// 基础API测试
//===========================================================================
function TestBasicAPI takes nothing returns nothing
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 基础API测试 ===|r")
    
    // 测试DzGetGameUI
    if DzGetGameUI() != null then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ DzGetGameUI 可用: " + I2S(DzGetGameUI()) + "|r")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF0000✗ DzGetGameUI 不可用|r")
        return
    endif
    
    // 测试获取框架
    call GetNativeFrames()
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ 基础测试完成|r")
endfunction

//===========================================================================
// 列出可用框架
//===========================================================================
function ListAvailableFrames takes nothing returns nothing
    local framehandle frame
    local integer i = 0
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 搜索可用框架 ===|r")
    
    // 常见框架名称列表
    local string array frameNames
    set frameNames[0] = "ConsoleUI"
    set frameNames[1] = "ConsoleUIBackdrop"
    set frameNames[2] = "ResourceBarFrame"
    set frameNames[3] = "CommandButton"
    set frameNames[4] = "InfoPanelIconBackdrop"
    set frameNames[5] = "MiniMapFrame"
    set frameNames[6] = "UpperButtonBarFrame"
    
    loop
        exitwhen i > 6
        set frame = DzGetFrameByName(frameNames[i], 0)
        if frame != null then
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00✓ " + frameNames[i] + ": " + I2S(frame) + "|r")
        else
            call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFF6600✗ " + frameNames[i] + ": 未找到|r")
        endif
        set i = i + 1
    endloop
endfunction

//===========================================================================
// 命令处理
//===========================================================================
function HandleCommands takes nothing returns nothing
    local string command = GetEventPlayerChatString()
    
    if command == "-test" then
        call TestBasicAPI()
    elseif command == "-adapt" then
        call AdaptWidescreenLayout()
    elseif command == "-restore" then
        call RestoreOriginalLayout()
    elseif command == "-toggle" then
        call ToggleAdaptation()
    elseif command == "-list" then
        call ListAvailableFrames()
    elseif command == "-help" then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 宽屏UI适配器命令 ===|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-test     基础API测试|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-adapt    应用宽屏适配|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-restore  恢复原始布局|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-toggle   切换适配状态|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-list     列出可用框架|r")
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700-help     显示帮助|r")
    endif
endfunction

//===========================================================================
// 初始化函数
//===========================================================================
function InitTrig_WidescreenUI takes nothing returns nothing
    set gg_trg_WidescreenUI = CreateTrigger()
    
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-test", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-adapt", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-restore", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-toggle", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-list", true)
    call TriggerRegisterPlayerChatEvent(gg_trg_WidescreenUI, Player(0), "-help", true)
    
    call TriggerAddAction(gg_trg_WidescreenUI, function HandleCommands)
    
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF87CEEB=== 宽屏UI适配器已加载 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700输入 -help 查看命令|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFD700建议先输入 -test 测试环境|r")
endfunction

//===========================================================================
// 主函数
//===========================================================================
function main takes nothing returns nothing
    call InitTrig_WidescreenUI()
endfunction
