# JASS错误案例库

## ❌ 触发器开发常见错误

### 🚫 错误1：触发器不执行
```jass
// 错误代码：只定义了InitTrig函数但未调用
globals
    trigger gg_trg_Test = null
endglobals

function InitTrig_Test takes nothing returns nothing
    set gg_trg_Test = CreateTrigger()
    call TriggerRegisterTimerEventSingle(gg_trg_Test, 5.0)
    call TriggerAddAction(gg_trg_Test, function Trig_TestActions)
endfunction
// 缺少main函数调用！
```

**错误原因**：InitTrig_*函数必须在main函数中手动调用才能执行  
**症状**：触发器代码看起来正确但完全不工作  
**解决方案**：
```jass
function main takes nothing returns nothing
    call InitTrig_Test()  // 添加这一行！
endfunction
```

**重要性**：⭐⭐⭐⭐⭐ 极其常见的新手错误

### 🚫 错误2：CreateUnit参数类型错误
```jass
// 错误代码：坐标使用integer类型
call CreateUnit(Player(0), 'Hmkg', 0, 0, 0)
```

**错误原因**：CreateUnit要求坐标参数为real类型，不是integer  
**编译器报错**：参数类型不匹配  
**解决方案**：
```jass
// 正确：使用real类型
call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
```

**学习要点**：JASS对类型要求严格，integer和real不能混用

### 🚫 错误3：使用1.30+版本函数
```jass
// 错误代码：在1.27环境使用1.30+函数
call BlzSetUnitMaxHP(unit, 1000)  // 1.30+函数
```

**错误原因**：1.27版本不支持Blz开头的新API函数  
**编译器报错**：未知函数名  
**解决方案**：使用1.27兼容的替代方案
```jass
// 1.27兼容方式
call SetUnitState(unit, UNIT_STATE_MAX_LIFE, 1000.0)
```

**预防措施**：查阅1.27 API文档，避免使用新版本函数

## ❌ 语法错误

### 🚫 错误4：全局变量声明错误
```jass
// 错误代码：在globals块中使用local关键字
globals
    local trigger gg_trg_Test = null  // 错误！
endglobals
```

**错误原因**：globals块中不需要local关键字  
**编译器报错**：语法错误  
**解决方案**：
```jass
globals
    trigger gg_trg_Test = null  // 正确
endglobals
```

### 🚫 错误5：函数参数解析错误
```jass
// 可能被错误解析的代码
call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
```

**错误原因**：某些IDE扩展可能错误解析嵌套函数调用  
**症状**：IDE报告参数数量错误，但代码实际正确  
**解决方案**：
1. 检查IDE扩展配置
2. 使用临时变量分解复杂调用
```jass
local player p = Player(0)
call CreateUnit(p, 'Hmkg', 0.0, 0.0, 0.0)
```

## ❌ 环境配置错误

### 🚫 错误6：扩展版本不匹配
**错误现象**：正确的1.27代码被标记为错误  
**原因**：VS Code扩展配置了错误的版本检查  
**解决方案**：
1. 确认使用1.27兼容的扩展版本
2. 检查黑名单配置是否正确
3. 重新安装正确版本的扩展

### 🚫 错误7：路径配置问题
**错误现象**：API函数无法识别  
**原因**：扩展无法找到JASS库文件  
**解决方案**：
1. 检查库文件路径配置
2. 确认common.j、blizzard.j等文件存在
3. 使用绝对路径而非相对路径

## 🔍 调试技巧

### 排查步骤
1. **确认函数调用**：检查InitTrig_*是否在main中调用
2. **验证语法**：确认参数类型和数量正确
3. **检查版本兼容性**：避免使用新版本API
4. **测试环境**：确认IDE扩展配置正确
