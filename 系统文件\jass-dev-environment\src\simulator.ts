/**
 * JASS代码模拟器
 * 模拟JASS代码执行，提供变量状态跟踪和逻辑验证
 */

interface SimulationResult {
    success: boolean;
    error?: string;
    variables?: { [key: string]: any };
    output?: string[];
    executionSteps?: ExecutionStep[];
}

interface ExecutionStep {
    line: number;
    type: 'call' | 'set' | 'local' | 'return';
    description: string;
    variables?: { [key: string]: any };
}

export class JassSimulator {
    private variables: Map<string, any> = new Map();
    private functions: Map<string, any> = new Map();
    private output: string[] = [];
    private executionSteps: ExecutionStep[] = [];
    
    constructor() {
        this.initializeBuiltinFunctions();
    }
    
    /**
     * 模拟JASS代码执行
     */
    async simulate(code: string): Promise<SimulationResult> {
        try {
            this.reset();
            
            // 解析代码
            const lines = code.split('\n');
            let currentFunction: string | null = null;
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                const lineNumber = i + 1;
                
                if (!line || line.startsWith('//')) continue;
                
                try {
                    await this.executeLine(line, lineNumber);
                } catch (error) {
                    return {
                        success: false,
                        error: `第${lineNumber}行执行错误: ${error.message}`
                    };
                }
            }
            
            return {
                success: true,
                variables: this.getVariablesSnapshot(),
                output: this.output,
                executionSteps: this.executionSteps
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 执行单行代码
     */
    private async executeLine(line: string, lineNumber: number): Promise<void> {
        // 解析local变量声明
        if (line.startsWith('local ')) {
            this.executeLocalDeclaration(line, lineNumber);
            return;
        }
        
        // 解析set语句
        if (line.startsWith('set ')) {
            this.executeSetStatement(line, lineNumber);
            return;
        }
        
        // 解析call语句
        if (line.startsWith('call ')) {
            await this.executeCallStatement(line, lineNumber);
            return;
        }
        
        // 解析return语句
        if (line.startsWith('return')) {
            this.executeReturnStatement(line, lineNumber);
            return;
        }
        
        // 忽略函数定义和结束标记
        if (line.startsWith('function ') || line === 'endfunction' || 
            line === 'globals' || line === 'endglobals') {
            return;
        }
    }
    
    /**
     * 执行local变量声明
     */
    private executeLocalDeclaration(line: string, lineNumber: number): void {
        const localRegex = /local\s+(\w+)\s+(\w+)(?:\s*=\s*(.+))?/;
        const match = line.match(localRegex);
        
        if (!match) {
            throw new Error('local变量声明语法错误');
        }
        
        const [, type, name, initialValue] = match;
        let value = this.getDefaultValue(type);
        
        if (initialValue) {
            value = this.evaluateExpression(initialValue);
        }
        
        this.variables.set(name, value);
        
        this.addExecutionStep(lineNumber, 'local', `声明变量 ${name} = ${value}`, {
            [name]: value
        });
    }
    
    /**
     * 执行set语句
     */
    private executeSetStatement(line: string, lineNumber: number): void {
        const setRegex = /set\s+(\w+)\s*=\s*(.+)/;
        const match = line.match(setRegex);
        
        if (!match) {
            throw new Error('set语句语法错误');
        }
        
        const [, varName, expression] = match;
        const value = this.evaluateExpression(expression);
        
        this.variables.set(varName, value);
        
        this.addExecutionStep(lineNumber, 'set', `设置 ${varName} = ${value}`, {
            [varName]: value
        });
    }
    
    /**
     * 执行call语句
     */
    private async executeCallStatement(line: string, lineNumber: number): Promise<void> {
        const callRegex = /call\s+(\w+)\s*\((.*)\)/;
        const match = line.match(callRegex);
        
        if (!match) {
            throw new Error('call语句语法错误');
        }
        
        const [, funcName, argsStr] = match;
        const args = this.parseArguments(argsStr);
        
        // 执行函数
        const result = await this.executeFunction(funcName, args);
        
        this.addExecutionStep(lineNumber, 'call', `调用函数 ${funcName}(${argsStr})`, {});
    }
    
    /**
     * 执行函数调用
     */
    private async executeFunction(funcName: string, args: any[]): Promise<any> {
        // 检查是否是内置函数
        if (this.functions.has(funcName)) {
            const func = this.functions.get(funcName);
            return await func(...args);
        }
        
        // 模拟YDWE API函数
        if (funcName.startsWith('YD')) {
            return this.simulateYDWEFunction(funcName, args);
        }
        
        // 模拟DZAPI函数
        if (funcName.startsWith('Dz')) {
            return this.simulateDZAPIFunction(funcName, args);
        }
        
        // 模拟原生JASS函数
        return this.simulateNativeFunction(funcName, args);
    }
    
    /**
     * 模拟YDWE API函数
     */
    private simulateYDWEFunction(funcName: string, args: any[]): any {
        switch (funcName) {
            case 'YDLocal1Set':
                const [type, key, value] = args;
                this.variables.set(`ydlocal_${key}`, value);
                this.output.push(`YDLocal设置: ${key} = ${value}`);
                return null;
                
            case 'YDLocal1Get':
                const [getType, getKey] = args;
                const localValue = this.variables.get(`ydlocal_${getKey}`) || this.getDefaultValue(getType);
                this.output.push(`YDLocal获取: ${getKey} = ${localValue}`);
                return localValue;
                
            case 'YDUserDataSet':
                const [player, dataKey, dataType, dataValue] = args;
                this.variables.set(`userdata_${dataKey}`, dataValue);
                this.output.push(`用户数据设置: ${dataKey} = ${dataValue}`);
                return null;
                
            case 'YDUserDataGet':
                const [getPlayer, getDataKey, getDataType] = args;
                const userData = this.variables.get(`userdata_${getDataKey}`) || this.getDefaultValue(getDataType);
                this.output.push(`用户数据获取: ${getDataKey} = ${userData}`);
                return userData;
                
            default:
                this.output.push(`模拟YDWE函数: ${funcName}`);
                return null;
        }
    }
    
    /**
     * 模拟DZAPI函数
     */
    private simulateDZAPIFunction(funcName: string, args: any[]): any {
        switch (funcName) {
            case 'DzFrameSetPoint':
                this.output.push(`设置UI框架位置: ${funcName}`);
                return null;
                
            case 'DzFrameSetText':
                const [frame, text] = args;
                this.output.push(`设置UI文本: ${text}`);
                return null;
                
            default:
                this.output.push(`模拟DZAPI函数: ${funcName}`);
                return null;
        }
    }
    
    /**
     * 模拟原生JASS函数
     */
    private simulateNativeFunction(funcName: string, args: any[]): any {
        switch (funcName) {
            case 'DisplayTextToPlayer':
                const [player, x, y, message] = args;
                this.output.push(`显示消息: ${message}`);
                return null;
                
            case 'GetAttacker':
                this.output.push('获取攻击者');
                return 'MockUnit_Attacker';
                
            case 'GetTriggerUnit':
                this.output.push('获取触发单位');
                return 'MockUnit_Target';
                
            case 'GetHeroAgi':
                const [unit, includeBonus] = args;
                const agiValue = 25; // 模拟敏捷值
                this.output.push(`获取英雄敏捷: ${agiValue}`);
                return agiValue;
                
            case 'I2R':
                const [intValue] = args;
                const realValue = parseFloat(intValue.toString());
                this.output.push(`整数转实数: ${intValue} -> ${realValue}`);
                return realValue;
                
            default:
                this.output.push(`模拟原生函数: ${funcName}`);
                return null;
        }
    }
    
    /**
     * 计算表达式
     */
    private evaluateExpression(expression: string): any {
        expression = expression.trim();
        
        // 处理字符串字面量
        if (expression.startsWith('"') && expression.endsWith('"')) {
            return expression.slice(1, -1);
        }
        
        // 处理数字
        if (/^\d+(\.\d+)?$/.test(expression)) {
            return expression.includes('.') ? parseFloat(expression) : parseInt(expression);
        }
        
        // 处理布尔值
        if (expression === 'true') return true;
        if (expression === 'false') return false;
        if (expression === 'null') return null;
        
        // 处理变量引用
        if (this.variables.has(expression)) {
            return this.variables.get(expression);
        }
        
        // 处理简单的数学表达式
        if (/^[\d\+\-\*\/\.\s\(\)]+$/.test(expression)) {
            try {
                return eval(expression);
            } catch {
                return 0;
            }
        }
        
        // 处理函数调用
        const funcCallRegex = /(\w+)\s*\((.*)\)/;
        const match = expression.match(funcCallRegex);
        if (match) {
            const [, funcName, argsStr] = match;
            const args = this.parseArguments(argsStr);
            return this.executeFunction(funcName, args);
        }
        
        return expression;
    }
    
    /**
     * 解析函数参数
     */
    private parseArguments(argsStr: string): any[] {
        if (!argsStr.trim()) return [];
        
        const args = [];
        const parts = argsStr.split(',');
        
        for (const part of parts) {
            args.push(this.evaluateExpression(part.trim()));
        }
        
        return args;
    }
    
    /**
     * 获取类型默认值
     */
    private getDefaultValue(type: string): any {
        switch (type) {
            case 'integer': return 0;
            case 'real': return 0.0;
            case 'boolean': return false;
            case 'string': return '';
            default: return null;
        }
    }
    
    /**
     * 添加执行步骤
     */
    private addExecutionStep(line: number, type: any, description: string, variables: any): void {
        this.executionSteps.push({
            line,
            type,
            description,
            variables: { ...variables }
        });
    }
    
    /**
     * 获取变量快照
     */
    private getVariablesSnapshot(): { [key: string]: any } {
        const snapshot: { [key: string]: any } = {};
        this.variables.forEach((value, key) => {
            snapshot[key] = value;
        });
        return snapshot;
    }
    
    /**
     * 重置模拟器状态
     */
    private reset(): void {
        this.variables.clear();
        this.output = [];
        this.executionSteps = [];
    }
    
    /**
     * 初始化内置函数
     */
    private initializeBuiltinFunctions(): void {
        // 可以在这里添加更多内置函数的模拟
    }
    
    /**
     * 执行return语句
     */
    private executeReturnStatement(line: string, lineNumber: number): void {
        const returnRegex = /return\s*(.*)/;
        const match = line.match(returnRegex);
        
        if (match) {
            const [, expression] = match;
            const value = expression ? this.evaluateExpression(expression) : null;
            this.addExecutionStep(lineNumber, 'return', `返回值: ${value}`, {});
        }
    }
}
