/**
 * 测试用户项目分析器
 */

const UserProjectAnalyzer = require('./user-project-analyzer.js');

console.log('开始测试用户项目分析器...');

const analyzer = new UserProjectAnalyzer();

// 分析用户项目
analyzer.analyzeUserProject();

// 测试代码验证
console.log('\n=== 测试代码验证 ===');

// 测试正确的代码
const correctCode = `
function TestFunction takes nothing returns nothing
    call BJDebugMsg("测试")
    set udg_SL = 5
endfunction
`;

// 测试错误的代码
const errorCode = `
function TestFunction takes nothing returns nothing
    call UndefinedFunction()
    set undefined_var = 5
    local unknowntype var
endfunction
`;

console.log('验证正确代码:');
const correctResult = analyzer.validateCode(correctCode);
console.log(`错误数量: ${correctResult.errors.length}`);

console.log('\n验证错误代码:');
const errorResult = analyzer.validateCode(errorCode);
console.log(`错误数量: ${errorResult.errors.length}`);
errorResult.errors.forEach(error => {
    console.log(`  第${error.line}行: ${error.message}`);
});

console.log('\n测试完成！');
