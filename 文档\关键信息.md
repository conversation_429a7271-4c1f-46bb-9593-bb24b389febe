# JASS关键信息库

## 🔑 核心机制（必须掌握）

### ⚡ 触发器执行机制
**最关键发现**：InitTrig_*函数必须手动调用才能执行
- **原理**：魔兽3只自动执行main函数
- **影响**：99%的"触发器不工作"问题都是这个原因
- **解决**：在main函数中调用所有InitTrig_*函数
- **重要性**：⭐⭐⭐⭐⭐ 最核心的概念

### 🎯 函数调用优先级
1. **main函数**：游戏启动时自动执行
2. **InitTrig_*函数**：需要在main中手动调用
3. **事件响应函数**：由触发器事件自动调用

### 📝 标准代码结构
```jass
// 必须按此顺序
globals
    // 全局变量声明
endglobals

function ActionFunction takes nothing returns nothing
    // 触发器动作
endfunction

function InitTrigFunction takes nothing returns nothing
    // 触发器初始化
endfunction

function main takes nothing returns nothing
    call InitTrigFunction()  // 关键调用
endfunction
```

## 🔧 版本兼容性（严格要求）

### 📊 1.27 vs 1.30+ 差异
**关键限制**：严格使用1.27 API
- **禁用前缀**：Blz*开头的函数（1.30+特有）
- **允许API**：原生native函数、YDWE API、JAPI、部分DZAPI
- **检查方法**：使用配置好的VS Code扩展验证

### 🛡️ 兼容性检查清单
- ✅ 函数名不以Blz开头
- ✅ 坐标参数使用real类型（0.0而非0）
- ✅ 使用经过验证的API列表
- ✅ 测试环境为1.27版本

## 🏗️ 开发环境配置

### 💻 推荐配置组合
**核心组件**：
- Warcraft 3 1.27版本
- KK平台支持
- DZAPI + YDWE API + JAPI
- VS Code + 配置好的JASS扩展

### 🔍 扩展配置要点
**关键设置**：
- 1.30+函数黑名单已配置
- 库文件路径正确设置（绝对路径）
- 错误检测机制已优化
- 支持中文错误提示

## 📚 API使用规范

### 🎯 函数参数类型（严格要求）
**必须遵守**：
- **坐标参数**：必须使用real类型（0.0而非0）
- **单位ID**：使用四字符码格式（'Hmkg'）
- **玩家参数**：使用Player(index)函数
- **布尔值**：true/false，不是1/0

### 📖 CreateUnit函数模板
```jass
// 标准格式：5个参数
CreateUnit(player, unitid, real, real, real)
// 示例
call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
```

## 🚀 性能优化

### ⚡ YDWE本地变量系统（推荐）
```jass
YDLocalInitialize()
call YDLocal1Set(type, "key", value)
local type var = YDLocal1Get(type, "key")
call YDLocal1Release()
```

**优势**：
- 避免全局变量污染
- 更好的内存管理
- 支持复杂数据传递

### 🎯 代码风格建议
- **函数命名**：遵循Trig_*Actions模式
- **变量命名**：使用有意义的名称
- **注释习惯**：关键逻辑必须注释
- **错误处理**：添加必要的检查

## 🔄 调试策略

### 🎯 问题排查顺序（重要性排序）
1. **检查main函数调用**（90%的问题）
2. **验证参数类型**（语法错误）
3. **确认API兼容性**（版本问题）
4. **测试环境配置**（工具问题）

### 🛠️ 调试工具组合
- **VS Code扩展**：实时语法检查
- **游戏内测试**：最终验证方法
- **日志输出**：使用DisplayText调试
- **分步测试**：逐个功能验证

## 📈 学习路径

### 🎯 技能等级划分
1. **Level 1**：基础语法和触发器结构
2. **Level 2**：API熟练使用和错误处理
3. **Level 3**：性能优化和高级技巧

### 🎓 关键里程碑
- ✅ 理解触发器执行机制（main函数调用）
- ✅ 掌握1.27 API限制
- ✅ 熟练使用YDWE系统
- ✅ 能够独立调试问题

## 🎯 用户偏好设置

### 💡 开发习惯
- 偏好纯JASS代码结构，不是GUI转换格式
- 使用YDLocalInitialize/Release无local声明模式
- 使用SetHeroLevelBJ而非SetHeroLevel
- 包含#ifdef DEBUG块和YDWESaveTriggerName

### 🔍 调试偏好
- 系统化函数分析，使用COM.J参考
- 中文网站资源优先（避免HIVE WORK 1.30+代码）
- 简单结构验证后再增加复杂性

## 🎯 AI沟通高效化要点 (2025-07-21新增)

### 📋 特效沟通标准化格式
```
特效需求描述格式：
- 特效类型：[掉落/投射/爆炸/持续/瞬发]
- 运动轨迹：[从天空掉落/直线飞行/弧线/原地]
- 起始位置：[天空高度800/单位位置/目标位置]
- 结束位置：[地面/目标单位/消失]
- 特效文件：[具体路径或"使用农民模型"]
- 动画效果：[旋转/缩放/透明度变化]
```

### ⚠️ JASS语言限制（重要）
- **变量名必须使用英文** - 不支持中文标识符
- **函数名必须使用英文** - 避免编译错误
- **全局变量声明** - 需要在globals块中声明trigger变量
- **初始化函数调用** - 必须在main函数中调用InitTrig_XXX()

### 🛡️ 代码质量保证原则
1. **只用项目中已验证的函数** - 严格基于实际war3map.j
2. **不添加未经验证的API** - 如果不确定函数是否存在，明确询问
3. **遵循项目变量命名规范** - YDLocal系列、udg_前缀等
4. **不使用实验性功能** - 只用成熟稳定的JASS语法
5. **内存管理严格** - 每个创建的对象都有对应的销毁

### 🎮 技能分类系统
**触发方式**：
- 攻击触发 - EVENT_PLAYER_UNIT_ATTACKED
- 主动技能触发 - EVENT_PLAYER_UNIT_SPELL_EFFECT
- 计时器周期触发 - TriggerRegisterTimerEventPeriodic

**技能类型**：
- 暴风雪类 - 区域持续伤害
- 贝塞尔曲线 - 弧形投射物
- 冲击波类 - 直线推进伤害
- 风暴之锤类 - 弹跳投射物
- 召唤类 - 创建单位

---
**更新日期**：2025-07-21
**核心提醒**：触发器不工作？99%是忘记在main函数中调用InitTrig_*函数！
