Frame "GLUEBUTTON" "Demo_Button" {
    Width 0.04,
    Height 0.04,
    ControlStyle "AUTOTRACK|HIGHLIGHTONFOCUS|HIGHLIGHTONMOUSEOVER",
    
    // 正常状态背景
    ControlBackdrop "Demo_ButtonBack",
    Frame "BACKDROP" "Demo_ButtonBack" {
        BackdropBackground "ReplaceableTextures\\CommandButtons\\BTN3M1.blp",
        BackdropBlendAll,
        SetAllPoints,
    },
    
    // 禁用状态背景
    ControlDisabledBackdrop "Demo_ButtonDisBack",
    Frame "BACKDROP" "Demo_ButtonDisBack" {
        BackdropBackground "ReplaceableTextures\\PassiveButtons\\PASBTNEnvenomedSpears.blp",
        BackdropBlendAll,
        SetAllPoints,
    },
    
    // 按下状态背景
    ControlPushedBackdrop "Demo_ButtonPushedBack",
    Frame "BACKDROP" "Demo_ButtonPushedBack" {
        BackdropBackground "ReplaceableTextures\\CommandButtonsDisabled\\DISBTNBA1.blp",
        BackdropBlendAll,
        SetAllPoints,
    },
    
    // 鼠标悬停高亮
    ControlMouseOverHighlight "Demo_ButtonHig",
    Frame "HIGHLIGHT" "Demo_ButtonHig" {
        HighlightType "FILETEXTURE",
        HighlightAlphaFile "UI\\Widgets\\EscMenu\\Human\\quest-button-highlight.blp",
        HighlightAlphaMode "ADD",
        SetAllPoints,
    }
}