[DTes]
_parent = "DTes"
-- 颜色值(蓝)
colorB = 220
-- 颜色值(绿)
colorG = 200
-- 颜色值(红)
colorR = 180
-- 模型文件
file = "Doodads\\Dungeon\\Terrain\\EggSack\\EggSack1.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.2

[DTg5]
_parent = "DTg5"
-- 模型文件
file = "war3mapImported\\BT_Gate_Solid.mdl"

[DTg7]
_parent = "DTg7"
-- 模型文件
file = "war3mapImported\\BT_Gate_Solid.mdl"
-- 固定角度
fixedRot = 90.0
-- 最大比例
maxScale = 0.35
-- 最小比例
minScale = 0.35

[DTsh]
_parent = "DTsh"
-- 颜色值(蓝)
colorB = 150
-- 颜色值(绿)
colorG = 150
-- 颜色值(红)
colorR = 150
-- 最大比例
maxScale = 1.75
-- 最小比例
minScale = 1.0
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""

[DTsp]
_parent = "DTsp"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 2.0
-- 路径纹理
pathTex = ""

[Dofw]
_parent = "Dofw"
-- 路径纹理
pathTex = "PathTextures\\2x2Default.tga"

[FTtw]
_parent = "FTtw"
-- 颜色值(蓝)
colorB = 200
-- 颜色值(绿)
colorG = 200
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 1.0
-- 悬崖上可放置
onCliffs = 1

[GTsh]
_parent = "GTsh"
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""

[IOt0]
_parent = "IOt0"
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[ITg2]
_parent = "ITg2"
-- 最大比例
maxScale = 0.7
-- 最小比例
minScale = 0.7

[ITtg]
_parent = "ITtg"
-- 模型文件
file = "war3mapImported\\BT_Gate_Solid.mdl"
-- 固定角度
fixedRot = 270.0
-- 最大比例
maxScale = 0.8
-- 最小比例
minScale = 0.8
-- 路径纹理
pathTex = ""
-- 阴影
shadow = ""

[ITtw]
_parent = "ITtw"
-- 生命值
HP = 10000000.0
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""

[ITw0]
_parent = "ITw0"
-- 生命值
HP = 1000.0
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""
-- 阴影
shadow = ""
-- 作为目标类型
targType = "debris"

[ITw2]
_parent = "ITw2"
-- 颜色值(绿)
colorG = 230
-- 颜色值(红)
colorR = 230
-- 固定角度
fixedRot = -1.0
-- 最大比例
maxScale = 0.75
-- 最小比例
minScale = 0.75
-- 路径纹理
pathTex = ""
-- 阴影
shadow = ""
-- 作为目标类型
targType = "debris"

[ITx1]
_parent = "ITx1"
-- 路径纹理
pathTex = ""
-- 游戏中可选择
selectable = 0

[ITx3]
_parent = "ITx3"
-- 路径纹理
pathTex = ""
-- 游戏中可选择
selectable = 0

[LTex]
_parent = "LTex"
-- 颜色值(蓝)
colorB = 150
-- 颜色值(绿)
colorG = 150

[LTg3]
_parent = "LTg3"
-- 生命值
HP = 5000.0
-- 名字
Name = "深渊之门"
-- 固定角度
fixedRot = -1.0
-- 最大比例
maxScale = 0.35
-- 最小比例
minScale = 0.35
-- 路径纹理
pathTex = ""
-- 模型文件 - 头像
portraitmodel = ".mdl"

[LTlt]
_parent = "LTlt"
-- 最大比例
maxScale = 1.75
-- 最小比例
minScale = 1.25
-- 悬崖上可放置
onCliffs = 1
-- 小地图 - 显示
showInMM = 0

[LTr4]
_parent = "LTr4"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 2.0

[LTrc]
_parent = "LTrc"
-- 颜色值(绿)
colorG = 200
-- 颜色值(红)
colorR = 200
-- 路径纹理
pathTex = ""

[LTw0]
_parent = "LTw0"
-- 路径纹理
pathTex = ""
-- 作为目标类型
targType = "debris"

[LTw2]
_parent = "LTw2"
-- 颜色值(绿)
colorG = 210
-- 颜色值(红)
colorR = 210
-- 模型文件
file = "Doodads\\LordaeronSummer\\Terrain\\StoneWall90\\StoneWall900.mdl"
-- 路径纹理
pathTex = ""
-- 作为目标类型
targType = "debris"

[WTst]
_parent = "WTst"
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 1.5
-- 悬崖上可放置
onCliffs = 1

[WTtw]
_parent = "WTtw"
-- 小地图颜色(蓝)
MMBlue = 150
-- 小地图颜色(红)
MMRed = 0
-- 最大比例
maxScale = 1.25
-- 最小比例
minScale = 1.25
-- 悬崖上可放置
onCliffs = 1

[XOkt]
_parent = "XOkt"
-- 颜色值(蓝)
colorB = 200
-- 颜色值(绿)
colorG = 150
-- 颜色值(红)
colorR = 150
-- 固定角度
fixedRot = 90.0

[XTbd]
_parent = "XTbd"
-- 颜色值(蓝)
colorB = 200
-- 颜色值(绿)
colorG = 150
-- 颜色值(红)
colorR = 0
-- 最大比例
maxScale = 3.0
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""
-- 阴影
shadow = ""

[XTmp]
_parent = "XTmp"
-- 最大比例
maxScale = 6.0
-- 最小比例
minScale = 6.0

[XTmx]
_parent = "XTmx"
-- 颜色值(蓝)
colorB = 125
-- 颜色值(绿)
colorG = 125
-- 颜色值(红)
colorR = 125

[XTvt]
_parent = "XTvt"
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""

[YTcx]
_parent = "YTcx"
-- 生命值
HP = 99999.0
-- 固定角度
fixedRot = -1.0
-- 路径纹理
pathTex = ""
-- 阴影
shadow = ""
-- 作为目标类型
targType = "ground"

[YTwt]
_parent = "YTwt"
-- 模型文件
file = "war3mapImported\\LordaeronTree4.mdl"
-- 模型文件 - 有附属模型
lightweight = 0
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 模型文件 - 样式总数
numVar = 1
-- 小地图 - 显示
showInMM = 0
-- 替换纹理文件
texFile = "ReplaceableTextures\\LordaeronTree\\LordaeronFallTree.blp"
-- 小地图 - 使用自定义颜色
useMMColor = 0

[ZTw0]
_parent = "ZTw0"
-- 作为目标类型
targType = "debris"

[B000]
_parent = "DTrc"

[B001]
_parent = "LTba"
-- 生命值
HP = 300.0
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 作为目标类型
targType = "debris,air"

[B002]
_parent = "OTip"
-- 模型文件
file = "war3mapImported\\tizi.mdl"
-- 固定角度
fixedRot = -1.0
-- 最大比例
maxScale = 6.0
-- 最小比例
minScale = 6.0

[B003]
_parent = "LTcr"
-- 名字
Name = "火盆"
-- 模型文件
file = "Doodads\\Ashenvale\\Props\\Brazier\\Brazier.mdl"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 模型文件 - 样式总数
numVar = 1

[B004]
_parent = "OTip"
-- 模型文件
file = "panzi.mdl"
-- 固定角度
fixedRot = -1.0
-- 最大比例
maxScale = 3.0
-- 最小比例
minScale = 3.0
-- 路径纹理
pathTex = ""

[B005]
_parent = "YTwt"
-- 名字
Name = "红枫"
-- 模型文件
file = "war3mapImported\\LordaeronTree4.mdl"
-- 模型文件 - 有附属模型
lightweight = 0
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 模型文件 - 样式总数
numVar = 1
-- 小地图 - 显示
showInMM = 0
-- 替换纹理文件
texFile = "ReplaceableTextures\\LordaeronTree\\LordaeronFallTree.blp"
-- 小地图 - 使用自定义颜色
useMMColor = 0

[B006]
_parent = "YTwt"
-- 编辑器后缀
EditorSuffix = "1"
-- 名字
Name = "雪珊"
-- 模型文件
file = "war3mapImported\\LordaeronTree4.mdl"
-- 模型文件 - 有附属模型
lightweight = 0
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.0
-- 模型文件 - 样式总数
numVar = 1
-- 小地图 - 显示
showInMM = 0
-- 替换纹理文件
texFile = "ReplaceableTextures\\LordaeronTree\\LordaeronSnowTree.blp"
-- 小地图 - 使用自定义颜色
useMMColor = 0

[B007]
_parent = "ITcr"
-- 名字
Name = "寒冰岩石1"
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[B008]
_parent = "LTba"
-- 名字
Name = "传送门"

[B009]
_parent = "XTbd"
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""
-- 阴影
shadow = ""

[B00A]
_parent = "WTst"
-- 最大比例
maxScale = 1.75
-- 最小比例
minScale = 1.5
-- 悬崖上可放置
onCliffs = 1

[B00B]
_parent = "FTtw"
-- 最大比例
maxScale = 2.5
-- 最小比例
minScale = 2.2
-- 悬崖上可放置
onCliffs = 1

[B00C]
_parent = "LTlt"
-- 最大比例
maxScale = 4.0
-- 最小比例
minScale = 4.0
-- 路径纹理
pathTex = "PathTextures\\StoneWall2Path.tga"
-- 小地图 - 显示
showInMM = 0

[B00D]
_parent = "WTtw"
-- 模型文件
file = "Doodads\\Terrain\\LordaeronTree\\LordaeronTree8S.mdl"
-- 最大比例
maxScale = 2.0
-- 最小比例
minScale = 2.0
-- 悬崖上可放置
onCliffs = 1

[B00E]
_parent = "WTtw"
-- 编辑器后缀
EditorSuffix = "p2"
-- 小地图颜色(绿)
MMGreen = 32
-- 最大比例
maxScale = 1.5
-- 最小比例
minScale = 1.5
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""

[B00F]
_parent = "WTtw"
-- 编辑器后缀
EditorSuffix = "p3"
-- 小地图颜色(蓝)
MMBlue = 96
-- 小地图颜色(绿)
MMGreen = 32
-- 最大比例
maxScale = 1.0
-- 最小比例
minScale = 1.0
-- 悬崖上可放置
onCliffs = 1
-- 路径纹理
pathTex = ""
-- 小地图 - 显示
showInMM = 0

[B00G]
_parent = "LTlt"
-- 编辑器后缀
EditorSuffix = "2"
-- 最大比例
maxScale = 1.75
-- 最小比例
minScale = 1.25
-- 悬崖上可放置
onCliffs = 1
-- 小地图 - 显示
showInMM = 0
