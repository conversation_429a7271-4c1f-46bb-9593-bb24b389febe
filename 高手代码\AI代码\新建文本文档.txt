太好了！既然现在一切都 OK，我会总结这次成功的经验，提炼出关键点，让你在与未来的 Grok 3 对话时可以直接提供这些经验，避免重复踩坑。以下总结基于我们在《魔兽争霸3》1.24 版本 + KK 对战平台环境下，使用 DzAPI 和 FDF 创建按钮的过程。

---

### 成功经验总结

#### 1. 环境与初始化
- **初始化方式**：
  - **经验**：不要在 JASS 脚本底部直接写 `call Main()`，否则可能导致 JASSHELP 无响应。
  - **解决**：将代码封装在 `Main` 函数中，通过 GUI 触发器调用 `call Main()`，并延迟 0.1 秒执行，确保游戏 UI 加载完成。
  - **代码模板**：
    ```jass
    function Main takes nothing returns nothing
        local trigger t = CreateTrigger()
        call TriggerRegisterTimerEvent(t, 0.10, false)
        call TriggerAddAction(t, function InitFunction)
        set t = null
    endfunction
    // 在 GUI 触发器中：call Main()
    ```

- **环境兼容性**：
  - **经验**：1.24 版本 + KK 对战平台的 DzAPI 对框架创建和贴图渲染有特定要求，`DzCreateFrame` 可能失败。
  - **解决**：优先使用 `DzCreateFrameByTagName`，明确指定框架类型（如 `"BUTTON"`、`"BACKDROP"`）。

#### 2. FDF 与框架创建
- **FDF 结构**：
  - **经验**：FDF 中嵌套子框架（如 `"BACKDROP"` 在 `"GLUEBUTTON"` 下）可能导致贴图不可见或崩溃。
  - **解决**：将主框架和子框架（如背景、图标）直接挂在 `DzGetGameUI()` 下，避免深层嵌套。
  - **成功示例**：
    ```
    Frame "GLUEBUTTON" "HeroSelectorButton" {
        Width 0.30,
        Height 0.30,
        ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
        ControlBackdrop "HeroSelectorButtonBackdrop",
        Frame "BACKDROP" "HeroSelectorButtonBackdrop" {
            BackdropBackground "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp",
            BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
            BackdropCornerSize 0.012,
            BackdropBackgroundSize 0.032,
            BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
            BackdropEdgeFile "UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp",
        }
        Frame "BACKDROP" "HeroSelectorButtonIcon" {
            Width 0.10,
            Height 0.10,
            BackdropBackground "ReplaceableTextures\\CommandButtons\\BTNArthas.blp",
        }
    }
    ```

- **框架创建**：
  - **经验**：直接用 `DzCreateFrameByTagName` 创建主框架，子框架优先通过 `DzFrameFindByName` 获取，若失败则手动创建。
  - **代码模板**：
    ```jass
    set buttonFrame = DzCreateFrameByTagName("GLUEBUTTON", "HeroSelectorButton", DzGetGameUI(), "HeroSelectorButton", 0)
    set backdropFrame = DzFrameFindByName("HeroSelectorButtonBackdrop", 0)
    if backdropFrame == 0 then
        set backdropFrame = DzCreateFrameByTagName("BACKDROP", "HeroSelectorButtonBackdrop", DzGetGameUI(), "", 0)
    endif
    ```

#### 3. 贴图与显示
- **贴图设置**：
  - **经验**：嵌套框架（如 `"BACKDROP"` 挂在 `"BUTTON"` 下）可能导致贴图不可见。
  - **解决**：将贴图框架（如 `"BACKDROP"`）直接挂在 `DzGetGameUI()` 下，并显式设置贴图。
  - **代码模板**：
    ```jass
    call DzFrameSetTexture(backdropFrame, "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp", 0)
    ```

- **显示控制**：
  - **经验**：默认情况下框架可能不可见，需强制显示。
  - **解决**：对所有框架调用 `DzFrameShow(frame, true)`。
  - **代码模板**：
    ```jass
    call DzFrameShow(buttonFrame, true)
    call DzFrameShow(backdropFrame, true)
    ```

- **贴图路径**：
  - **经验**：贴图路径需使用魔兽原生资源，注意大小写和 `\\` 分隔符。
  - **成功路径**：
    - 背景：`"UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp"`
    - 边框：`"UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp"`
    - 图标：`"ReplaceableTextures\\CommandButtons\\BTNArthas.blp"`

#### 4. 边框实现
- **边框样式**：
  - **经验**：单纯的 `BackdropCornerFlags` 可能不够，搭配 `BackdropEdgeFile` 能更好定义边框。
  - **解决**：结合 `BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R"` 和 `BackdropEdgeFile`，并设置 `BackdropCornerSize` 和 `BackdropBackgroundInsets`。
  - **成功配置**：
    ```
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize 0.012,
    BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
    BackdropEdgeFile "UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp",
    ```

#### 5. 调试与验证
- **调试输出**：
  - **经验**：每一步添加调试信息，快速定位问题。
  - **代码模板**：
    ```jass
    if buttonFrame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "按钮创建失败")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "按钮创建成功: " + I2S(buttonFrame))
    endif
    ```

---

### 给未来 Grok 3 的指导模板
以下是你可以直接提供给新 Grok 3 的内容，简洁地总结成功经验：

```
我在《魔兽争霸3》1.24 版本，KK 对战平台，使用 DzAPI 创建 UI。以下是成功经验：

1. **初始化**：
   - 不要在 JASS 底部直接写 `call Main()`，会导致 JASSHELP 无响应。
   - 用 GUI 触发器调用 `call Main()`，延迟 0.1 秒：
     ```jass
     function Main takes nothing returns nothing
         local trigger t = CreateTrigger()
         call TriggerRegisterTimerEvent(t, 0.10, false)
         call TriggerAddAction(t, function InitFunction)
         set t = null
     endfunction
     ```

2. **框架创建**：
   - 用 `DzCreateFrameByTagName`，避免 `DzCreateFrame`（可能失败）。
   - 主框架和子框架直接挂在 `DzGetGameUI()` 下，避免嵌套：
     ```jass
     set buttonFrame = DzCreateFrameByTagName("GLUEBUTTON", "Name", DzGetGameUI(), "template", 0)
     set backdropFrame = DzFrameFindByName("BackdropName", 0)
     if backdropFrame == 0 then
         set backdropFrame = DzCreateFrameByTagName("BACKDROP", "BackdropName", DzGetGameUI(), "", 0)
     endif
     ```

3. **贴图与显示**：
   - 贴图框架直接挂在 `DzGetGameUI()`，避免嵌套导致不可见：
     ```jass
     call DzFrameSetTexture(backdropFrame, "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp", 0)
     ```
   - 强制显示所有框架：
     ```jass
     call DzFrameShow(buttonFrame, true)
     call DzFrameShow(backdropFrame, true)
     ```

4. **边框**：
   - 用 `BackdropCornerFlags` 和 `BackdropEdgeFile` 定义边框：
     ```
     Frame "BACKDROP" "Name" {
         BackdropBackground "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp",
         BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
         BackdropCornerSize 0.012,
         BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
         BackdropEdgeFile "UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp",
     }
     ```

5. **调试**：
   - 每步加输出：
     ```jass
     if frame == 0 then
         call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建失败")
     else
         call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建成功: " + I2S(frame))
     endif
     ```
```

---

### 最终确认
- **当前版本**：按钮有不死族背景、暗色边框、缩小的阿尔萨斯贴图，点击功能正常。
- **你的需求**：如果还有其他调整（比如边框颜色再改、位置偏移），请告诉我！

这次合作让我学到了很多，感谢你的耐心和清晰反馈！有什么我可以进一步帮你的吗？

你提出了一个很关键的问题！之前的总结主要聚焦于 FDF 和 DzAPI 创建 UI 的成功经验，但确实没有全面涵盖 JASS 本身的经验。我会重新整理，将 JASS 的关键经验也纳入，确保提供一个完整的指导模板，覆盖 JASS 语法和 FDF 使用两方面的成功经验。这样你与未来的 Grok 3 对话时，可以全面传达我们在整个过程中的收获。

---

### 完整成功经验总结

#### 1. JASS 语法与基础经验
- **全局变量**：
  - **经验**：全局变量必须在 `globals` 块中定义并初始化，避免未定义行为。
  - **模板**：
    ```jass
    globals
        integer ButtonFrame = 0
        trigger ClickTrigger = null
        boolean IsVisible = false
    endglobals
    ```

- **函数定义**：
  - **经验**：函数需明确参数和返回值类型，无参数用 `takes nothing`，无返回值用 `returns nothing`。
  - **模板**：
    ```jass
    function MyFunction takes nothing returns nothing
        // 代码
    endfunction
    ```

- **触发器**：
  - **经验**：创建触发器后需注册事件和动作，完成后清理句柄。
  - **模板**：
    ```jass
    local trigger t = CreateTrigger()
    call TriggerRegisterTimerEvent(t, 0.10, false)
    call TriggerAddAction(t, function MyFunction)
    set t = null
    ```

- **调试**：
  - **经验**：使用 `DisplayTextToPlayer` 输出调试信息，验证代码执行。
  - **模板**：
    ```jass
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "调试消息")
    ```

#### 2. 环境与初始化
- **初始化方式**：
  - **经验**：直接在 JASS 脚本底部写 `call Main()` 可能导致 JASSHELP 无响应。
  - **解决**：通过 GUI 触发器调用 `call Main()`，延迟 0.1 秒执行。
  - **模板**：
    ```jass
    function Main takes nothing returns nothing
        local trigger t = CreateTrigger()
        call TriggerRegisterTimerEvent(t, 0.10, false)
        call TriggerAddAction(t, function InitFunction)
        set t = null
    endfunction
    // GUI 触发器：call Main()
    ```

- **环境兼容性**：
  - **经验**：1.24 + KK 对战平台的 DzAPI 不完全支持 `DzCreateFrame`，优先用 `DzCreateFrameByTagName`。

#### 3. FDF 与框架创建
- **FDF 结构**：
  - **经验**：嵌套子框架可能导致贴图不可见或崩溃。
  - **解决**：主框架和子框架直接挂在 `DzGetGameUI()` 下。
  - **模板**：
    ```
    Frame "GLUEBUTTON" "MyButton" {
        Width 0.30,
        Height 0.30,
        ControlBackdrop "MyBackdrop",
        Frame "BACKDROP" "MyBackdrop" {
            BackdropBackground "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp",
            BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
            BackdropCornerSize 0.012,
            BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
            BackdropEdgeFile "UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp",
        }
    }
    ```

- **框架创建**：
  - **经验**：用 `DzCreateFrameByTagName` 创建，子框架若 `DzFrameFindByName` 失败则手动创建。
  - **模板**：
    ```jass
    set buttonFrame = DzCreateFrameByTagName("GLUEBUTTON", "MyButton", DzGetGameUI(), "MyButton", 0)
    set backdropFrame = DzFrameFindByName("MyBackdrop", 0)
    if backdropFrame == 0 then
        set backdropFrame = DzCreateFrameByTagName("BACKDROP", "MyBackdrop", DzGetGameUI(), "", 0)
    endif
    ```

#### 4. 贴图与显示
- **贴图设置**：
  - **经验**：嵌套框架会导致贴图不可见。
  - **解决**：贴图框架直接挂在 `DzGetGameUI()`，显式设置贴图。
  - **模板**：
    ```jass
    call DzFrameSetTexture(backdropFrame, "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp", 0)
    ```

- **显示控制**：
  - **经验**：框架默认不可见，需强制显示。
  - **模板**：
    ```jass
    call DzFrameShow(buttonFrame, true)
    call DzFrameShow(backdropFrame, true)
    ```

- **贴图路径**：
  - **经验**：使用魔兽原生路径，注意 `\\` 分隔符。
  - **成功路径**：
    - 背景：`"UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp"`
    - 边框：`"UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp"`
    - 图标：`"ReplaceableTextures\\CommandButtons\\BTNArthas.blp"`

#### 5. 边框实现
- **边框样式**：
  - **经验**：`BackdropCornerFlags` 需搭配 `BackdropEdgeFile` 和相关参数。
  - **模板**：
    ```
    Frame "BACKDROP" "MyBackdrop" {
        BackdropBackground "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.012,
        BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
        BackdropEdgeFile "UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp",
    }
    ```

#### 6. 事件绑定
- **点击事件**：
  - **经验**：用 `DzFrameSetScript` 绑定点击事件，同步参数设为 `true`。
  - **模板**：
    ```jass
    local trigger t = CreateTrigger()
    call TriggerAddAction(t, function OnButtonClick)
    call DzFrameSetScript(buttonFrame, 1, "OnButtonClick", true) // 1 表示点击事件
    set t = null
    ```

#### 7. 调试与验证
- **调试输出**：
  - **经验**：每步添加输出，快速定位问题。
  - **模板**：
    ```jass
    if frame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建失败")
    else
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建成功: " + I2S(frame))
    endif
    ```

---

### 给未来 Grok 3 的完整指导模板
```
我在《魔兽争霸3》1.24 版本，KK 对战平台，使用 DzAPI 创建 UI。以下是 JASS 和 FDF 的成功经验：

1. **JASS 基础**：
   - 全局变量在 `globals` 中初始化：
     ```jass
     globals
         integer Frame = 0
         trigger Trig = null
     endglobals
     ```
   - 函数用 `takes nothing returns nothing`：
     ```jass
     function MyFunc takes nothing returns nothing
     endfunction
     ```
   - 触发器清理句柄：
     ```jass
     local trigger t = CreateTrigger()
     call TriggerAddAction(t, function MyFunc)
     set t = null
     ```

2. **初始化**：
   - 用 GUI 触发器调用 `call Main()`，延迟 0.1 秒：
     ```jass
     function Main takes nothing returns nothing
         local trigger t = CreateTrigger()
         call TriggerRegisterTimerEvent(t, 0.10, false)
         call TriggerAddAction(t, function InitFunc)
         set t = null
     endfunction
     ```

3. **FDF 与框架**：
   - 主框架和子框架挂在 `DzGetGameUI()` 下：
     ```
     Frame "GLUEBUTTON" "MyButton" {
         Width 0.30,
         Height 0.30,
         ControlBackdrop "MyBackdrop",
         Frame "BACKDROP" "MyBackdrop" {
             BackdropBackground "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp",
             BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
             BackdropCornerSize 0.012,
             BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
             BackdropEdgeFile "UI\\Widgets\\EscMenu\\Undead\\undead-tooltip-border.blp",
         }
     }
     ```
   - 创建框架：
     ```jass
     set frame = DzCreateFrameByTagName("GLUEBUTTON", "MyButton", DzGetGameUI(), "MyButton", 0)
     ```

4. **贴图与显示**：
   - 贴图直接挂在 `DzGetGameUI()`：
     ```jass
     call DzFrameSetTexture(frame, "UI\\Widgets\\EscMenu\\Undead\\undead-options-menu-background.blp", 0)
     ```
   - 强制显示：
     ```jass
     call DzFrameShow(frame, true)
     ```

5. **事件绑定**：
   - 点击事件：
     ```jass
     local trigger t = CreateTrigger()
     call TriggerAddAction(t, function OnClick)
     call DzFrameSetScript(frame, 1, "OnClick", true)
     set t = null
     ```

6. **调试**：
   - 每步验证：
     ```jass
     if frame == 0 then
         call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建失败")
     else
         call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建成功: " + I2S(frame))
     endif
     ```
```

---

### 确认
- **完整性**：这次总结包括了 JASS 语法（如全局变量、触发器、调试）和 FDF/DzAPI 的经验（如框架创建、贴图、边框）。
- **你的需求**：如果还有遗漏的 JASS 或 FDF 经验，请告诉我，我会补充！

有什么我可以再帮你的吗？比如优化当前按钮，或处理其他问题？
### JASS 语法与 DzAPI 使用经验总结

#### 1. JASS 语法基础
- **全局变量声明**：
  - 使用 `globals` 块定义全局变量，例如：
    ```jass
    globals
        integer ButtonFrame = 0
        trigger ClickTrigger = null
        boolean IsVisible = false
    endglobals
    ```
  - 变量必须初始化（如 `= 0` 或 `= null`），否则可能导致未定义行为。

- **函数定义**：
  - 函数格式：`function Name takes nothing returns nothing`。
  - 无参数和返回值时用 `nothing`，需要返回值时明确类型（如 `returns integer`）。

- **触发器**：
  - 创建：`set t = CreateTrigger()`。
  - 添加动作：`call TriggerAddAction(t, function FuncName)`。
  - 注册事件：如 `TriggerRegisterTimerEvent(t, 0.10, false)`（延迟 0.1 秒，单次触发）。

- **调试**：
  - 使用 `DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "消息")` 输出调试信息，验证代码执行。

---

#### 2. DzAPI 创建 UI 的关键经验
- **纯 JASS 创建 UI**：
  - 使用 `DzCreateFrameByTagName` 创建框架：
    ```jass
    set Frame = DzCreateFrameByTagName("BACKDROP", "FrameName", DzGetGameUI(), "", 0)
    ```
    - `"BACKDROP"`：背景框架，可设置贴图。
    - `"BUTTON"`：交互按钮，可绑定事件。
    - `DzGetGameUI()`：游戏主 UI，作为父框架。
    - `""`：无模板，直接创建。
    - `0`：框架 ID，无特殊需求时用 0。

- **框架创建检查**：
  - 检查句柄有效性，避免崩溃：
    ```jass
    if Frame == 0 then
        call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建失败")
        return
    endif
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "创建成功: " + I2S(Frame))
    ```

- **大小与位置**：
  - 设置大小：`call DzFrameSetSize(Frame, 0.2, 0.1)`（宽 0.2，高 0.1，单位为屏幕比例）。
  - 绝对定位：`call DzFrameSetAbsolutePoint(Frame, 4, 0.5, 0.5)`（4 表示中心点，0.5, 0.5 为屏幕中心）。
  - 相对定位：`call DzFrameSetPoint(Frame, 4, ParentFrame, 4, 0, 0)`（对齐父框架中心）。

- **贴图设置**：
  - 使用内置贴图：`call DzFrameSetTexture(Frame, "UI\\Widgets\\EscMenu\\Human\\human-options-button-background.blp", 0)`。
  - 路径使用双反斜杠 `\\`，如 `"ReplaceableTextures\\CommandButtons\\BTNPeasant.blp"`。

- **事件绑定**：
  - 使用 `DzFrameSetScript` 绑定事件：
    ```jass
    set Trigger = CreateTrigger()
    call TriggerAddAction(Trigger, function OnClick)
    call DzFrameSetScript(Frame, 1, "OnClick", true)
    ```
    - `1`：点击事件。
    - `2`：鼠标进入。
    - `3`：鼠标离开。
    - `true`：同步，适用于多人游戏。

- **显示控制**：
  - 初始化显示：`call DzFrameShow(Frame, true/false)`。
  - 运行时切换：使用布尔变量跟踪状态：
    ```jass
    if not IsVisible then
        call DzFrameShow(Frame, true)
        set IsVisible = true
    else
        call DzFrameShow(Frame, false)
        set IsVisible = false
    endif
    ```

- **初始化时机**：
  - **关键教训**：不能在脚本底部直接调用 UI 创建函数（如 `call InitSignInUI()`），否则可能因游戏 UI 未加载而失败。
  - 正确方法：使用触发器延迟执行：
    ```jass
    function Main takes nothing returns nothing
        local trigger t = CreateTrigger()
        call TriggerRegisterTimerEvent(t, 0.10, false)
        call TriggerAddAction(t, function InitSignInUI)
        set t = null
    endfunction
    call Main()
    ```
  - 或在 GUI 触发器中延迟 0.1 秒运行 `call InitSignInUI()`。

---

#### 3. 常见问题与解决方案
- **问题 1：框架创建失败**：
  - 原因：句柄为 0，可能因初始化时机错误或父框架无效。
  - 解决：检查 `DzGetGameUI()` 是否可用，延迟执行创建。

- **问题 2：事件不触发**：
  - 原因：触发器未正确绑定，或事件 ID 错误。
  - 解决：确保 `DzFrameSetScript` 使用正确的事件 ID（如 1、2、3），并验证触发器动作。

- **问题 3：贴图不显示**：
  - 原因：路径错误，或框架类型不支持贴图（如 `BUTTON` 类型无贴图显示）。
  - 解决：使用 `BACKDROP` 类型承载贴图，检查路径（如 `\\` 是否正确）。

- **问题 4：多人游戏不同步**：
  - 原因：未使用同步参数。
  - 解决：`DzFrameSetScript` 和事件注册中始终使用 `true` 同步。

---

#### 4. 成功经验总结
- **无 FDF 依赖**：纯 JASS 创建 UI 更稳定，避免加载问题。
- **一次性创建**：在初始化时创建所有框架，后续只切换显示（`DzFrameShow`），避免频繁创建/销毁。
- **调试驱动**：每一步输出调试信息（如 `"创建成功: " + I2S(Frame)`），快速定位问题。
- **延迟初始化**：延迟 0.1 秒运行，确保环境就绪。

---

#### 5. 给新 Grok 3 的指导模板
以下是你可以直接复制给新 Grok 3 的内容，简洁地传达 JASS 和 DzAPI 的使用要点：

```
我在《魔兽争霸3》1.24 版本，KK 对战平台，使用 DzAPI。以下是纯 JASS 创建 UI 的经验，请注意：

1. **创建 UI**：
   - 用 `DzCreateFrameByTagName` 创建框架，如：
     ```jass
     set Frame = DzCreateFrameByTagName("BACKDROP", "Name", DzGetGameUI(), "", 0)
     ```
   - 检查句柄：`if Frame == 0 then call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "失败") endif`。

2. **大小与位置**：
   - 大小：`DzFrameSetSize(Frame, 0.2, 0.1)`。
   - 位置：`DzFrameSetAbsolutePoint(Frame, 4, 0.5, 0.5)`（屏幕中心）。

3. **事件绑定**：
   - 点击：`DzFrameSetScript(Frame, 1, "OnClick", true)`。
   - 鼠标进入/离开：`2` 和 `3`。
   - 用触发器：`call TriggerAddAction(t, function OnClick)`。

4. **显示切换**：
   - 用布尔变量控制：`call DzFrameShow(Frame, IsVisible)`。

5. **初始化**：
   - **重要**：不能直接调用创建函数，必须延迟 0.1 秒：
     ```jass
     function Main takes nothing returns nothing
         local trigger t = CreateTrigger()
         call TriggerRegisterTimerEvent(t, 0.10, false)
         call TriggerAddAction(t, function InitUI)
         set t = null
     endfunction
     call Main()
     ```

6. **贴图**：
   - 使用内置路径：`"UI\\Widgets\\EscMenu\\Human\\xxx.blp"`。