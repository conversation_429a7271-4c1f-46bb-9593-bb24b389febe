import * as vscode from 'vscode';
import { JassCompiler, CompileOptions, CompileResult } from './compiler/JassCompiler';

let diagnosticCollection: vscode.DiagnosticCollection;
let jassCompiler: JassCompiler;
let outputChannel: vscode.OutputChannel;

export function activate(context: vscode.ExtensionContext) {
    console.log('🔧 JASS Full Compiler 已激活');

    // 创建诊断集合
    diagnosticCollection = vscode.languages.createDiagnosticCollection('jass');
    context.subscriptions.push(diagnosticCollection);

    // 创建输出通道
    outputChannel = vscode.window.createOutputChannel('JASS Compiler');
    context.subscriptions.push(outputChannel);

    // 初始化编译器
    jassCompiler = new JassCompiler();

    // 注册命令
    registerCommands(context);

    // 注册事件监听器
    registerEventListeners(context);

    // 检查当前已打开的JASS文件
    vscode.workspace.textDocuments.forEach(document => {
        if (document.languageId === 'jass') {
            compileDocument(document);
        }
    });

    vscode.window.showInformationMessage('🔧 JASS Full Compiler 已就绪！按 Ctrl+Shift+B 编译代码');
}

function registerCommands(context: vscode.ExtensionContext): void {
    // 编译当前文件
    const compileCommand = vscode.commands.registerCommand('jass.compile', () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor || !editor.document.fileName.endsWith('.j')) {
            vscode.window.showWarningMessage('请在JASS文件中使用此命令');
            return;
        }
        
        compileDocument(editor.document, true);
    });

    // 验证整个项目
    const validateProjectCommand = vscode.commands.registerCommand('jass.validateProject', async () => {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            vscode.window.showWarningMessage('请在工作区中使用此命令');
            return;
        }

        await validateProject(workspaceFolder);
    });

    // 显示编译输出
    const showOutputCommand = vscode.commands.registerCommand('jass.showCompileOutput', () => {
        outputChannel.show();
    });

    context.subscriptions.push(compileCommand, validateProjectCommand, showOutputCommand);
}

function registerEventListeners(context: vscode.ExtensionContext): void {
    // 监听文档变化
    const documentChangeListener = vscode.workspace.onDidChangeTextDocument(event => {
        if (event.document.languageId === 'jass') {
            const config = vscode.workspace.getConfiguration('jass.compiler');
            if (config.get('autoCompileOnSave', true)) {
                // 延迟编译，避免频繁触发
                setTimeout(() => {
                    compileDocument(event.document);
                }, 1000);
            }
        }
    });

    // 监听文档保存
    const documentSaveListener = vscode.workspace.onDidSaveTextDocument(document => {
        if (document.languageId === 'jass') {
            compileDocument(document, true);
        }
    });

    // 监听文档打开
    const documentOpenListener = vscode.workspace.onDidOpenTextDocument(document => {
        if (document.languageId === 'jass') {
            compileDocument(document);
        }
    });

    // 监听配置变化
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('jass.compiler')) {
            // 重新编译所有打开的JASS文件
            vscode.workspace.textDocuments.forEach(document => {
                if (document.languageId === 'jass') {
                    compileDocument(document);
                }
            });
        }
    });

    context.subscriptions.push(
        documentChangeListener,
        documentSaveListener,
        documentOpenListener,
        configChangeListener
    );
}

function compileDocument(document: vscode.TextDocument, showOutput: boolean = false): void {
    const config = vscode.workspace.getConfiguration('jass.compiler');
    
    if (!config.get('enabled', true)) {
        return;
    }

    const options: CompileOptions = {
        warcraft3Version: config.get('warcraft3Version', '1.27'),
        enableYDWE: config.get('enableYDWE', true),
        enableDZAPI: config.get('enableDZAPI', true),
        enableJassHelper: config.get('enableJassHelper', false),
        strictMode: config.get('strictMode', true),
        treatWarningsAsErrors: config.get('treatWarningsAsErrors', false)
    };

    try {
        const startTime = Date.now();
        const result = jassCompiler.compile(document.getText(), options, document.fileName);
        const endTime = Date.now();

        // 更新诊断
        updateDiagnostics(document, result);

        // 显示编译结果
        if (showOutput || !result.success) {
            showCompileResult(result, document.fileName, endTime - startTime);
        }

        // 更新状态栏
        updateStatusBar(result);

    } catch (error) {
        console.error('编译错误:', error);
        vscode.window.showErrorMessage(`编译器错误: ${error}`);
        
        outputChannel.appendLine(`❌ 编译器内部错误: ${error}`);
        outputChannel.appendLine(`文件: ${document.fileName}`);
        outputChannel.appendLine(`时间: ${new Date().toLocaleString()}`);
        outputChannel.appendLine('---');
    }
}

function updateDiagnostics(document: vscode.TextDocument, result: CompileResult): void {
    const diagnostics: vscode.Diagnostic[] = [];

    // 添加错误
    result.errors.forEach(error => {
        const range = new vscode.Range(
            Math.max(0, error.line - 1),
            Math.max(0, error.column - 1),
            Math.max(0, error.line - 1),
            Math.max(0, error.column - 1 + error.length)
        );
        
        const diagnostic = new vscode.Diagnostic(
            range,
            error.message,
            vscode.DiagnosticSeverity.Error
        );
        diagnostic.source = 'JASS Compiler';
        diagnostic.code = error.code;
        diagnostics.push(diagnostic);
    });

    // 添加警告
    result.warnings.forEach(warning => {
        const range = new vscode.Range(
            Math.max(0, warning.line - 1),
            Math.max(0, warning.column - 1),
            Math.max(0, warning.line - 1),
            Math.max(0, warning.column - 1 + warning.length)
        );
        
        const diagnostic = new vscode.Diagnostic(
            range,
            warning.message,
            vscode.DiagnosticSeverity.Warning
        );
        diagnostic.source = 'JASS Compiler';
        diagnostic.code = warning.code;
        diagnostics.push(diagnostic);
    });

    // 更新诊断
    diagnosticCollection.set(document.uri, diagnostics);
}

function showCompileResult(result: CompileResult, fileName: string, duration: number): void {
    const shortFileName = fileName.split(/[/\\]/).pop() || fileName;
    
    outputChannel.appendLine(`🔧 编译文件: ${shortFileName}`);
    outputChannel.appendLine(`⏱️ 编译时间: ${duration}ms`);
    
    if (result.success) {
        outputChannel.appendLine(`✅ 编译成功`);
        if (result.warnings.length > 0) {
            outputChannel.appendLine(`⚠️ ${result.warnings.length} 个警告`);
        }
    } else {
        outputChannel.appendLine(`❌ 编译失败`);
        outputChannel.appendLine(`🔴 ${result.errors.length} 个错误`);
        outputChannel.appendLine(`⚠️ ${result.warnings.length} 个警告`);
    }

    // 显示错误详情
    if (result.errors.length > 0) {
        outputChannel.appendLine('\n🔴 错误详情:');
        result.errors.forEach((error, index) => {
            outputChannel.appendLine(`${index + 1}. 第${error.line}行:${error.column} - ${error.message} [${error.code}]`);
        });
    }

    // 显示警告详情
    if (result.warnings.length > 0) {
        outputChannel.appendLine('\n⚠️ 警告详情:');
        result.warnings.forEach((warning, index) => {
            outputChannel.appendLine(`${index + 1}. 第${warning.line}行:${warning.column} - ${warning.message} [${warning.code}]`);
        });
    }

    // 显示符号表统计
    const stats = result.symbolTable.getStatistics();
    outputChannel.appendLine(`\n📊 符号统计: ${stats.functions} 个函数, ${stats.globalVariables} 个全局变量, ${stats.localVariables} 个局部变量`);
    
    outputChannel.appendLine(`🕐 ${new Date().toLocaleString()}`);
    outputChannel.appendLine('---\n');
}

function updateStatusBar(result: CompileResult): void {
    if (result.success) {
        if (result.warnings.length > 0) {
            vscode.window.setStatusBarMessage(`⚠️ JASS: ${result.warnings.length} 个警告`, 5000);
        } else {
            vscode.window.setStatusBarMessage('✅ JASS: 编译成功', 3000);
        }
    } else {
        vscode.window.setStatusBarMessage(`❌ JASS: ${result.errors.length} 个错误`, 10000);
    }
}

async function validateProject(workspaceFolder: vscode.WorkspaceFolder): Promise<void> {
    const pattern = new vscode.RelativePattern(workspaceFolder, '**/*.j');
    const files = await vscode.workspace.findFiles(pattern);
    
    if (files.length === 0) {
        vscode.window.showInformationMessage('项目中没有找到JASS文件');
        return;
    }

    outputChannel.appendLine(`🔍 开始验证项目: ${workspaceFolder.name}`);
    outputChannel.appendLine(`📁 找到 ${files.length} 个JASS文件`);
    outputChannel.show();

    let totalErrors = 0;
    let totalWarnings = 0;
    let successCount = 0;

    for (const file of files) {
        try {
            const document = await vscode.workspace.openTextDocument(file);
            compileDocument(document);
            
            // 获取诊断结果
            const diagnostics = diagnosticCollection.get(file);
            if (diagnostics) {
                const errors = diagnostics.filter(d => d.severity === vscode.DiagnosticSeverity.Error).length;
                const warnings = diagnostics.filter(d => d.severity === vscode.DiagnosticSeverity.Warning).length;
                
                totalErrors += errors;
                totalWarnings += warnings;
                
                if (errors === 0) {
                    successCount++;
                }
            }
        } catch (error) {
            outputChannel.appendLine(`❌ 无法处理文件: ${file.fsPath} - ${error}`);
        }
    }

    outputChannel.appendLine(`\n📊 项目验证完成:`);
    outputChannel.appendLine(`✅ 成功: ${successCount}/${files.length} 个文件`);
    outputChannel.appendLine(`❌ 总错误: ${totalErrors}`);
    outputChannel.appendLine(`⚠️ 总警告: ${totalWarnings}`);
    
    if (totalErrors === 0) {
        vscode.window.showInformationMessage(`🎉 项目验证成功！${successCount} 个文件通过编译`);
    } else {
        vscode.window.showWarningMessage(`⚠️ 项目验证完成，发现 ${totalErrors} 个错误`);
    }
}

export function deactivate() {
    if (diagnosticCollection) {
        diagnosticCollection.dispose();
    }
    if (outputChannel) {
        outputChannel.dispose();
    }
    console.log('🔧 JASS Full Compiler 已停用');
}
