# JASS成功案例库

## 🎯 触发器开发

### ✅ 基础触发器结构（已验证）
```jass
globals
    trigger gg_trg_Test = null
endglobals

function Trig_TestActions takes nothing returns nothing
    call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
endfunction

function InitTrig_Test takes nothing returns nothing
    set gg_trg_Test = CreateTrigger()
    call TriggerRegisterTimerEventSingle(gg_trg_Test, 5.0)
    call TriggerAddAction(gg_trg_Test, function Trig_TestActions)
endfunction

// 关键：必须在main函数中调用
function main takes nothing returns nothing
    call InitTrig_Test()
endfunction
```

**验证状态**：✅ 用户确认工作正常  
**适用版本**：Warcraft 3 1.27  
**关键要点**：InitTrig_*函数必须手动调用

### ✅ 山丘之王创建示例（已验证）
```jass
function Trig_CreateMountainKingActions takes nothing returns nothing
    local integer ydul_i
    YDLocalInitialize()

    // 创建山丘之王
    call YDLocal1Set(unit, "dw", CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 270.0))
    set udg_MountainKing = YDLocal1Get(unit, "dw")

    // 设置英雄等级
    call SetHeroLevel(YDLocal1Get(unit, "dw"), 10, true)

    // 学习技能
    call SelectHeroSkill(YDLocal1Get(unit, "dw"), 'AHav')

    // 施放技能
    call IssueImmediateOrder(YDLocal1Get(unit, "dw"), "avatar")

    call YDLocal1Release()
endfunction
```

**验证状态**：✅ 来自工作项目  
**特点**：使用YDWE本地变量系统  
**最佳实践**：YDLocalInitialize/Release配对使用

## 🔧 API使用

### ✅ CreateUnit函数正确格式
```jass
// 正确：5个参数，坐标使用real类型
call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)

// 参数说明：
// player id - 玩家
// integer unitid - 单位ID（四字符码）
// real x - X坐标
// real y - Y坐标  
// real face - 朝向角度
```

**验证状态**：✅ API文档确认  
**常见错误**：使用integer而非real类型坐标

### ✅ 1.27兼容的函数列表
```jass
// 单位操作
CreateUnit, KillUnit, RemoveUnit
SetUnitX, SetUnitY, SetUnitPosition
SetUnitFacing, SetUnitScale
UnitAddAbility, UnitRemoveAbility

// 触发器操作
CreateTrigger, TriggerRegisterTimerEventSingle
TriggerAddAction, EnableTrigger, DisableTrigger

// 计时器操作
CreateTimer, TimerStart, PauseTimer, ResumeTimer
```

**验证状态**：✅ 1.27环境测试通过  
**重要性**：避免使用1.30+函数

## 🏗️ 代码结构

### ✅ 推荐的触发器命名规范
```jass
// 全局变量
trigger gg_trg_TriggerName = null

// 动作函数
function Trig_TriggerNameActions takes nothing returns nothing

// 初始化函数
function InitTrig_TriggerName takes nothing returns nothing
```

**验证状态**：✅ 符合魔兽3编辑器标准  
**优势**：与GUI转换的JASS保持一致
