# 🚀 JASS VS Code开发环境 - 快速开始指南

## 🎯 **立即开始使用**

### **第一步：一键安装**
```bash
# 在项目目录下运行
install.bat
```
安装脚本会自动：
- ✅ 检查Node.js和VS Code环境
- ✅ 安装所有依赖
- ✅ 编译TypeScript代码
- ✅ 打包并安装VS Code扩展

### **第二步：测试功能**
1. 重启VS Code
2. 打开 `test-examples/skill-example.j`
3. 体验以下功能：

#### **🔍 实时语法检查**
- 代码中的错误会立即显示红色波浪线
- 警告显示黄色波浪线
- 查看"问题"面板获取详细信息

#### **🚀 代码模拟器**
- 按 `F5` 运行代码模拟
- 在右侧面板查看：
  - ✅ 执行结果
  - 📊 变量状态
  - 📝 执行步骤
  - 💬 输出信息

#### **💡 智能补全**
- 输入 `YD` 查看YDWE API补全
- 输入 `Dz` 查看DZAPI补全
- 输入变量名获取智能提示

#### **📚 API参考**
- 点击状态栏的 `JASS` 按钮
- 或使用命令 `JASS: 显示API参考`
- 搜索你需要的API函数

## 🎮 **基于你的编程模式**

### **技能开发模板**
```jass
function Trig_新技能Actions takes nothing returns nothing
    local timer ydl_timer
    YDLocalInitialize()
    
    // 1. 获取基础单位
    call YDLocal1Set(unit, "a", GetAttacker())
    call YDLocal1Set(unit, "b", GetTriggerUnit())
    
    // 2. 获取技能数据
    call YDLocal1Set(real, "lv", YDUserDataGet(unit, YDLocal1Get(unit, "a"),"技能名称", real))
    
    // 3. 计算伤害 (基于你的公式模式)
    call YDLocal1Set(real, "sh", (I2R(GetHeroAgi(YDLocal1Get(unit, "a"), true)) * ((YDLocal1Get(real, "lv") * 2.40) * (1.00 + YDUserDataGet(unit, YDLocal1Get(unit, "a"),"技能伤害", real)))))
    
    // 4. 执行技能效果
    // 在这里添加你的技能逻辑
    
    // 5. 清理资源
    call YDLocal1Release()
    set ydl_timer = null
endfunction
```

### **DZAPI UI模板**
```jass
function CreateCustomUI takes nothing returns nothing
    local framehandle ydl_frame
    
    set ydl_frame = DzCreateFrameByTagName("BACKDROP", "MyFrame", DzGetGameUI(), "", 0)
    call DzFrameSetPoint(ydl_frame, FRAMEPOINT_CENTER, DzGetGameUI(), FRAMEPOINT_CENTER, 0, 0)
    call DzFrameSetSize(ydl_frame, 0.3, 0.2)
    call DzFrameShow(ydl_frame, true)
    
    set ydl_frame = null
endfunction
```

## 🛠️ **开发工作流程**

### **1. 创建新技能**
```
1. 新建 .j 文件
2. 使用技能模板 (输入 ydwe-skill)
3. 实时编写代码，查看语法提示
4. 按 F5 测试代码逻辑
5. 修复错误，优化代码
```

### **2. 调试技能**
```
1. 在代码中添加 DisplayTextToPlayer 输出调试信息
2. 使用模拟器查看变量值变化
3. 检查执行步骤是否符合预期
4. 验证伤害计算公式
```

### **3. 优化代码**
```
1. 查看警告提示，优化命名规范
2. 使用 Ctrl+Shift+V 验证代码
3. 参考API文档，使用最佳实践
4. 确保资源正确清理
```

## 🎨 **个性化配置**

### **VS Code设置**
在 `.vscode/settings.json` 中添加：
```json
{
    "jass.warcraftVersion": "1.27",
    "jass.enableYDWEAPI": true,
    "jass.enableDZAPI": true,
    "jass.variableNamingConvention": "ydwe",
    "editor.fontSize": 14,
    "editor.tabSize": 4,
    "editor.insertSpaces": true
}
```

### **代码片段**
在 `.vscode/snippets/jass.json` 中添加：
```json
{
    "YDWE技能": {
        "prefix": "ydwe-skill",
        "body": [
            "function Trig_${1:技能名称}Actions takes nothing returns nothing",
            "    local timer ydl_timer",
            "    YDLocalInitialize()",
            "    $0",
            "    call YDLocal1Release()",
            "    set ydl_timer = null",
            "endfunction"
        ]
    },
    "DZAPI框架": {
        "prefix": "dz-frame",
        "body": [
            "local framehandle ydl_${1:frame}",
            "set ydl_${1:frame} = DzCreateFrameByTagName(\"${2:BACKDROP}\", \"${3:FrameName}\", DzGetGameUI(), \"\", 0)",
            "call DzFrameSetPoint(ydl_${1:frame}, FRAMEPOINT_${4:CENTER}, DzGetGameUI(), FRAMEPOINT_${4:CENTER}, ${5:0}, ${6:0})",
            "call DzFrameSetSize(ydl_${1:frame}, ${7:0.3}, ${8:0.2})",
            "call DzFrameShow(ydl_${1:frame}, true)",
            "set ydl_${1:frame} = null"
        ]
    }
}
```

## 🔧 **常见问题解决**

### **Q: 扩展没有激活？**
```
A: 确保文件扩展名是 .j 或 .jass
   重启VS Code
   检查扩展是否正确安装
```

### **Q: 语法高亮不正常？**
```
A: 检查语言模式是否设置为 "JASS"
   右下角点击语言模式进行切换
```

### **Q: 模拟器报错？**
```
A: 检查代码语法是否正确
   确保使用支持的JASS语法
   查看输出面板的详细错误信息
```

### **Q: API补全不工作？**
```
A: 确保启用了YDWE和DZAPI支持
   检查设置中的API开关
   重新加载窗口 (Ctrl+Shift+P -> Reload Window)
```

## 🎯 **下一步学习**

### **进阶功能**
1. **自定义API函数** - 添加你自己的函数库
2. **代码格式化** - 统一代码风格
3. **批量重构** - 快速修改变量名和函数名
4. **性能分析** - 优化代码执行效率

### **扩展开发**
1. **添加新的API支持** - 扩展函数库
2. **自定义语法检查规则** - 符合你的编码规范
3. **集成外部工具** - 连接YDWE或其他工具

## 🎉 **开始你的JASS开发之旅！**

现在你已经拥有了一个完整的JASS开发环境：
- ✅ 实时语法检查和错误提示
- ✅ 智能代码补全和API参考
- ✅ 代码模拟器和调试功能
- ✅ 基于你实际项目的开发模式

**立即开始编写你的第一个技能吧！** 🚀

---

💡 **提示**: 如果遇到任何问题，查看README.md获取详细文档，或者检查test-examples中的示例代码。
