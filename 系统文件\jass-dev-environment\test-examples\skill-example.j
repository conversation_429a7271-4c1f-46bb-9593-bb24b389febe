//===========================================================================
// 测试技能: 暗影冲击 (基于你的实际代码模式)
//===========================================================================

// 全局变量声明
globals
    real udg_SkillDamageMultiplier = 2.40
    real udg_BaseAgility = 25.0
endglobals

//===========================================================================
// 暗影冲击技能函数 - 完全基于你的编程模式
//===========================================================================
function Trig_anyingchongjiActions takes nothing returns nothing
    local timer ydl_timer
    local unit ydl_attacker
    local unit ydl_target
    local real ydl_skillLevel
    local real ydl_damage
    local real ydl_agility
    
    // 初始化YDLocal系统
    YDLocalInitialize()
    
    // 获取攻击者和目标单位
    call YDLocal1Set(unit, "a", GetAttacker())
    call YDLocal1Set(unit, "b", GetTriggerUnit())
    
    // 获取技能等级
    call YDLocal1Set(real, "lv", YDUserDataGet(unit, YDLocal1Get(unit, "a"), "暗影冲击", real))
    
    // 计算伤害 - 基于敏捷值和技能等级
    call YDLocal1Set(real, "sh", (I2R(GetHeroAgi(YDLocal1Get(unit, "a"), true)) * ((YDLocal1Get(real, "lv") * udg_SkillDamageMultiplier) * (1.00 + YDUserDataGet(unit, YDLocal1Get(unit, "a"), "技能伤害", real)))))
    
    // 获取计算后的值用于后续处理
    set ydl_attacker = YDLocal1Get(unit, "a")
    set ydl_target = YDLocal1Get(unit, "b")
    set ydl_skillLevel = YDLocal1Get(real, "lv")
    set ydl_damage = YDLocal1Get(real, "sh")
    
    // 显示调试信息
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "暗影冲击触发!")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "技能等级: " + R2S(ydl_skillLevel))
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "造成伤害: " + R2S(ydl_damage))
    
    // 创建特效
    call YDWECreateEffectOnUnit(ydl_target, "Abilities\\Spells\\Undead\\DarkRitual\\DarkRitualTarget.mdl")
    
    // 造成伤害
    call UnitDamageTarget(ydl_attacker, ydl_target, ydl_damage, true, false, ATTACK_TYPE_NORMAL, DAMAGE_TYPE_MAGIC, WEAPON_TYPE_WHOKNOWS)
    
    // 设置冷却时间
    call YDUserDataSet(unit, ydl_attacker, "暗影冲击_冷却", real, 5.0)
    
    // 清理资源
    call YDLocal1Release()
    set ydl_timer = null
    set ydl_attacker = null
    set ydl_target = null
endfunction

//===========================================================================
// DZAPI UI示例 - 显示技能信息面板
//===========================================================================
function CreateSkillInfoPanel takes nothing returns nothing
    local framehandle ydl_mainFrame
    local framehandle ydl_titleText
    local framehandle ydl_damageText
    
    // 创建主框架
    set ydl_mainFrame = DzCreateFrameByTagName("BACKDROP", "SkillInfoPanel", DzGetGameUI(), "", 0)
    call DzFrameSetPoint(ydl_mainFrame, FRAMEPOINT_TOPRIGHT, DzGetGameUI(), FRAMEPOINT_TOPRIGHT, -0.02, -0.15)
    call DzFrameSetSize(ydl_mainFrame, 0.25, 0.12)
    call DzFrameSetTexture(ydl_mainFrame, "UI\\Widgets\\Console\\Human\\human-console-background.blp", 0)
    
    // 创建标题文本
    set ydl_titleText = DzCreateFrameByTagName("TEXT", "SkillTitle", ydl_mainFrame, "", 0)
    call DzFrameSetPoint(ydl_titleText, FRAMEPOINT_TOP, ydl_mainFrame, FRAMEPOINT_TOP, 0, -0.01)
    call DzFrameSetText(ydl_titleText, "|cFFFFD700暗影冲击|r")
    call DzFrameSetTextAlignment(ydl_titleText, TEXT_JUSTIFY_CENTER, TEXT_JUSTIFY_MIDDLE)
    
    // 创建伤害显示文本
    set ydl_damageText = DzCreateFrameByTagName("TEXT", "SkillDamage", ydl_mainFrame, "", 0)
    call DzFrameSetPoint(ydl_damageText, FRAMEPOINT_CENTER, ydl_mainFrame, FRAMEPOINT_CENTER, 0, -0.02)
    call DzFrameSetText(ydl_damageText, "伤害: 计算中...")
    call DzFrameSetTextAlignment(ydl_damageText, TEXT_JUSTIFY_CENTER, TEXT_JUSTIFY_MIDDLE)
    
    // 显示面板
    call DzFrameShow(ydl_mainFrame, true)
    
    // 清理局部变量
    set ydl_mainFrame = null
    set ydl_titleText = null
    set ydl_damageText = null
endfunction

//===========================================================================
// 技能数据初始化函数
//===========================================================================
function InitializeSkillData takes nothing returns nothing
    local player ydl_player
    local integer ydl_playerIndex
    
    // 为所有玩家初始化技能数据
    set ydl_playerIndex = 0
    loop
        exitwhen ydl_playerIndex > 11
        set ydl_player = Player(ydl_playerIndex)
        
        // 设置默认技能等级
        call YDUserDataSet(unit, GetPlayerStartLocation(ydl_player), "暗影冲击", real, 1.0)
        call YDUserDataSet(unit, GetPlayerStartLocation(ydl_player), "技能伤害", real, 0.0)
        call YDUserDataSet(unit, GetPlayerStartLocation(ydl_player), "暗影冲击_冷却", real, 0.0)
        
        set ydl_playerIndex = ydl_playerIndex + 1
    endloop
    
    // 显示初始化完成消息
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFF00FF00技能系统初始化完成!|r")
    
    set ydl_player = null
endfunction

//===========================================================================
// 技能升级函数
//===========================================================================
function UpgradeSkillLevel takes unit whichUnit, string skillName returns nothing
    local real ydl_currentLevel
    local real ydl_newLevel
    
    // 获取当前技能等级
    set ydl_currentLevel = YDUserDataGet(unit, whichUnit, skillName, real)
    
    // 计算新等级 (最大5级)
    if ydl_currentLevel < 5.0 then
        set ydl_newLevel = ydl_currentLevel + 1.0
        call YDUserDataSet(unit, whichUnit, skillName, real, ydl_newLevel)
        
        // 显示升级消息
        call DisplayTextToPlayer(GetOwningPlayer(whichUnit), 0, 0, skillName + " 升级到 " + I2S(R2I(ydl_newLevel)) + " 级!")
    else
        call DisplayTextToPlayer(GetOwningPlayer(whichUnit), 0, 0, skillName + " 已达到最高等级!")
    endif
endfunction

//===========================================================================
// 测试函数 - 用于验证代码逻辑
//===========================================================================
function TestSkillSystem takes nothing returns nothing
    local unit ydl_testUnit
    local real ydl_testDamage
    
    // 创建测试单位
    set ydl_testUnit = CreateUnit(Player(0), 'Hpal', 0, 0, 0)
    
    // 设置测试数据
    call SetHeroAgi(ydl_testUnit, 50, true)
    call YDUserDataSet(unit, ydl_testUnit, "暗影冲击", real, 3.0)
    call YDUserDataSet(unit, ydl_testUnit, "技能伤害", real, 0.2)
    
    // 计算预期伤害
    set ydl_testDamage = I2R(GetHeroAgi(ydl_testUnit, true)) * (3.0 * udg_SkillDamageMultiplier) * (1.0 + 0.2)
    
    // 显示测试结果
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "|cFFFFFF00=== 技能测试结果 ===|r")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "单位敏捷: " + I2S(GetHeroAgi(ydl_testUnit, true)))
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "技能等级: 3")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "伤害加成: 20%")
    call DisplayTextToPlayer(GetLocalPlayer(), 0, 0, "预期伤害: " + R2S(ydl_testDamage))
    
    // 清理测试单位
    call RemoveUnit(ydl_testUnit)
    set ydl_testUnit = null
endfunction
