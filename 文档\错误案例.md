# JASS错误案例库

## ❌ 最常见错误

### 🚫 错误1：触发器不执行（重要性：⭐⭐⭐⭐⭐）
```jass
// 错误代码：只定义了InitTrig函数但未调用
globals
    trigger gg_trg_Test = null
endglobals

function InitTrig_Test takes nothing returns nothing
    set gg_trg_Test = CreateTrigger()
    call TriggerRegisterTimerEventSingle(gg_trg_Test, 5.0)
    call TriggerAddAction(gg_trg_Test, function Trig_TestActions)
endfunction
// 缺少main函数调用！
```

**错误原因**：InitTrig_*函数必须在main函数中手动调用才能执行  
**症状**：触发器代码看起来正确但完全不工作  
**解决方案**：
```jass
function main takes nothing returns nothing
    call InitTrig_Test()  // 添加这一行！
endfunction
```

### 🚫 错误2：CreateUnit参数类型错误
```jass
// 错误代码：坐标使用integer类型
call CreateUnit(Player(0), 'Hmkg', 0, 0, 0)
```

**错误原因**：CreateUnit要求坐标参数为real类型，不是integer  
**编译器报错**：参数类型不匹配  
**解决方案**：
```jass
// 正确：使用real类型
call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
```

### 🚫 错误3：使用1.30+版本函数
```jass
// 错误代码：在1.27环境使用1.30+函数
call BlzSetUnitMaxHP(unit, 1000)  // 1.30+函数
```

**错误原因**：1.27版本不支持Blz开头的新API函数  
**解决方案**：使用1.27兼容的替代方案
```jass
// 1.27兼容方式
call SetUnitState(unit, UNIT_STATE_MAX_LIFE, 1000.0)
```

## ❌ 语法错误

### 🚫 错误4：全局变量声明错误
```jass
// 错误代码：在globals块中使用local关键字
globals
    local trigger gg_trg_Test = null  // 错误！
endglobals
```

**解决方案**：
```jass
globals
    trigger gg_trg_Test = null  // 正确
endglobals
```

### 🚫 错误5：IDE扩展参数解析错误
```jass
// 可能被错误解析的代码
call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
```

**错误现象**：IDE报告参数数量错误，但代码实际正确  
**原因**：某些IDE扩展错误解析嵌套函数调用  
**解决方案**：
```jass
local player p = Player(0)
call CreateUnit(p, 'Hmkg', 0.0, 0.0, 0.0)
```

## ❌ 环境配置错误

### 🚫 错误6：扩展版本不匹配
**错误现象**：正确的1.27代码被标记为错误  
**原因**：VS Code扩展配置了错误的版本检查  
**解决方案**：
1. 确认使用1.27兼容的扩展版本
2. 检查黑名单配置是否正确
3. 重新安装正确版本的扩展

### 🚫 错误7：路径配置问题
**错误现象**：API函数无法识别
**原因**：扩展无法找到JASS库文件
**解决方案**：
1. 检查库文件路径配置
2. 确认common.j、blizzard.j等文件存在
3. 使用绝对路径而非相对路径

### 🚫 错误8：中文变量名错误（2025-07-21新增）
```jass
globals
    trigger gg_trg_农民雨 = null  // 编译错误
endglobals

function Trig_农民雨Actions takes nothing returns nothing  // 编译错误
    // ...
endfunction
```

**错误原因**：JASS不支持中文标识符
**编译器报错**：`Invalid identifier name`
**解决方案**：所有变量名、函数名必须使用英文
```jass
globals
    trigger gg_trg_PeasantRain = null  // 正确
endglobals

function Trig_PeasantRainActions takes nothing returns nothing  // 正确
    // ...
endfunction
```

## 🎯 AI沟通错误案例（2025-07-21新增）

### 🚫 错误9：特效描述不准确导致的问题
**错误沟通**：
用户："做一个农民雨技能"
AI理解：在地面创建农民特效

**正确沟通**：
用户："农民从天空800高度掉落到地面，有掉落动画轨迹"
AI理解：创建从高空到地面的运动轨迹

**教训**：特效需求必须包含运动轨迹和位置信息

### 🚫 错误10：过度复杂化代码
**AI常见错误**：添加用户未要求的复杂功能
- 不必要的条件检查
- 过度的特效处理
- 未经验证的API调用

**正确做法**：严格按用户需求，最小化实现

## 🔍 调试检查清单

### 排查步骤（按优先级）
1. **检查main函数调用**（90%的问题在这里）
2. **验证参数类型**（real vs integer）
3. **确认API兼容性**（避免1.30+函数）
4. **检查变量名是否为英文**（编译错误）
5. **测试环境配置**（IDE扩展设置）

### 常见症状对应
- **触发器不工作** → 检查main函数调用
- **参数错误提示** → 检查参数类型
- **函数未识别** → 检查版本兼容性
- **Invalid identifier name** → 检查是否使用中文变量名
- **IDE报错但代码正确** → 检查扩展配置

---
**更新日期**：2025-07-21
**重要提醒**：90%的问题都是忘记在main函数中调用InitTrig_*函数！
