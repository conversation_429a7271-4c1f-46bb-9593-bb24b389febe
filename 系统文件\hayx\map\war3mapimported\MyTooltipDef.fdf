﻿IncludeFile "UI\FrameDef\Glue\BattleNetTemplates.fdf",

// --- Tooltip 背景样式 (通过名称关联) ---
Frame "BACKDROP" "TooltipBackground" { // <<< 名称与 JASS 中 DzCreateFrame 一致
    BackdropBackground          "UI\Widgets\BattleNet\bnet-tooltip-background.blp",
    BackdropEdgeFile            "UI\Widgets\BattleNet\bnet-tooltip-border.blp",
    BackdropCornerFlags         "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize          0.012,
    BackdropBackgroundInsets    0.005 0.005 0.005 0.005,
    BackdropEdgeSize            0.012,
    BackdropBlendAll,
    // Visible: 由 JASS 控制显隐
}

// --- Tooltip 文本模板 ---
Frame "TEXT" "TooltipTextTemplate" { // <<< 模板名称，供 DzCreateFrameByTagName 使用
    FontJustificationH JUSTIFYLEFT,   // 水平左对齐
    FontJustificationV JUSTIFYMIDDLE, // 垂直居中
    TextAreaInset 0.005,              // 内边距 (可选)
    // Font "fonts.ttf", 0.011, 0,    // 字体也可以在这里设置
}



