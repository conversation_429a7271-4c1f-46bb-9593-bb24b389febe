import { ApiFunction, ApiType } from './ApiDatabase';

export interface Symbol {
    name: string;
    type: string;
    category: 'variable' | 'function' | 'type' | 'parameter';
    scope: 'global' | 'local' | 'parameter';
    line?: number;
    column?: number;
    used?: boolean;
    constant?: boolean;
    array?: boolean;
}

export interface FunctionSymbol extends Symbol {
    returnType: string;
    parameters: Array<{name: string, type: string}>;
    native?: boolean;
}

export interface VariableSymbol extends Symbol {
    initialValue?: any;
}

export interface TypeSymbol extends Symbol {
    baseType?: string;
}

export class SymbolTable {
    private globalSymbols: Map<string, Symbol> = new Map();
    private localScopes: Map<string, Map<string, Symbol>> = new Map(); // functionName -> symbols
    private currentFunction: string | null = null;
    private types: Map<string, TypeSymbol> = new Map();
    private functions: Map<string, FunctionSymbol> = new Map();

    public clear(): void {
        this.globalSymbols.clear();
        this.localScopes.clear();
        this.types.clear();
        this.functions.clear();
        this.currentFunction = null;
    }

    public addBuiltinTypes(): void {
        const builtinTypes = [
            'integer', 'real', 'boolean', 'string', 'handle', 'code', 'nothing',
            'unit', 'player', 'location', 'rect', 'region', 'timer', 'trigger',
            'group', 'force', 'effect', 'sound', 'framehandle', 'multiboard',
            'dialog', 'button', 'leaderboard', 'lightning', 'image', 'ubersplat', 'hashtable'
        ];

        builtinTypes.forEach(typeName => {
            this.types.set(typeName, {
                name: typeName,
                type: 'type',
                category: 'type',
                scope: 'global',
                baseType: typeName === 'handle' ? undefined : 
                         ['unit', 'player', 'location', 'rect', 'region', 'timer', 'trigger', 
                          'group', 'force', 'effect', 'sound', 'framehandle', 'multiboard',
                          'dialog', 'button', 'leaderboard', 'lightning', 'image', 'ubersplat', 'hashtable'].includes(typeName) ? 'handle' : undefined
            });
        });
    }

    public addApiFunctions(functions: ApiFunction[]): void {
        functions.forEach(func => {
            this.functions.set(func.name, {
                name: func.name,
                type: func.returnType,
                category: 'function',
                scope: 'global',
                returnType: func.returnType,
                parameters: func.parameters,
                native: func.category === 'native' || func.category === 'ydwe' || func.category === 'dzapi'
            });
        });
    }

    public enterFunction(functionName: string): void {
        this.currentFunction = functionName;
        if (!this.localScopes.has(functionName)) {
            this.localScopes.set(functionName, new Map());
        }
    }

    public exitFunction(): void {
        this.currentFunction = null;
    }

    public addGlobalVariable(name: string, type: string, constant: boolean = false, array: boolean = false, line?: number, column?: number): void {
        const symbol: VariableSymbol = {
            name,
            type,
            category: 'variable',
            scope: 'global',
            constant,
            array,
            line,
            column,
            used: false
        };
        this.globalSymbols.set(name, symbol);
    }

    public addLocalVariable(name: string, type: string, array: boolean = false, line?: number, column?: number): void {
        if (!this.currentFunction) {
            throw new Error('Cannot add local variable outside of function');
        }

        const symbol: VariableSymbol = {
            name,
            type,
            category: 'variable',
            scope: 'local',
            array,
            line,
            column,
            used: false
        };

        const localScope = this.localScopes.get(this.currentFunction)!;
        localScope.set(name, symbol);
    }

    public addParameter(functionName: string, name: string, type: string, line?: number, column?: number): void {
        const symbol: VariableSymbol = {
            name,
            type,
            category: 'parameter',
            scope: 'parameter',
            line,
            column,
            used: false
        };

        if (!this.localScopes.has(functionName)) {
            this.localScopes.set(functionName, new Map());
        }

        const localScope = this.localScopes.get(functionName)!;
        localScope.set(name, symbol);
    }

    public addFunction(name: string, returnType: string, parameters: Array<{name: string, type: string}>, line?: number, column?: number): void {
        const symbol: FunctionSymbol = {
            name,
            type: returnType,
            category: 'function',
            scope: 'global',
            returnType,
            parameters,
            line,
            column,
            used: false,
            native: false
        };
        this.functions.set(name, symbol);
    }

    public addNativeFunction(name: string, returnType: string, parameters: Array<{name: string, type: string}>): void {
        const symbol: FunctionSymbol = {
            name,
            type: returnType,
            category: 'function',
            scope: 'global',
            returnType,
            parameters,
            used: false,
            native: true
        };
        this.functions.set(name, symbol);
    }

    public addType(name: string, baseType: string, line?: number, column?: number): void {
        const symbol: TypeSymbol = {
            name,
            type: 'type',
            category: 'type',
            scope: 'global',
            baseType,
            line,
            column,
            used: false
        };
        this.types.set(name, symbol);
    }

    public hasSymbol(name: string): boolean {
        return this.hasVariable(name) || this.hasFunction(name) || this.hasType(name);
    }

    public hasVariable(name: string): boolean {
        // 检查局部作用域
        if (this.currentFunction) {
            const localScope = this.localScopes.get(this.currentFunction);
            if (localScope && localScope.has(name)) {
                return true;
            }
        }
        
        // 检查全局作用域
        return this.globalSymbols.has(name);
    }

    public hasFunction(name: string): boolean {
        return this.functions.has(name);
    }

    public hasType(name: string): boolean {
        return this.types.has(name);
    }

    public getVariable(name: string): VariableSymbol | undefined {
        // 优先检查局部作用域
        if (this.currentFunction) {
            const localScope = this.localScopes.get(this.currentFunction);
            if (localScope && localScope.has(name)) {
                const symbol = localScope.get(name)!;
                symbol.used = true;
                return symbol as VariableSymbol;
            }
        }
        
        // 检查全局作用域
        if (this.globalSymbols.has(name)) {
            const symbol = this.globalSymbols.get(name)!;
            symbol.used = true;
            return symbol as VariableSymbol;
        }
        
        return undefined;
    }

    public getFunction(name: string): FunctionSymbol | undefined {
        if (this.functions.has(name)) {
            const symbol = this.functions.get(name)!;
            symbol.used = true;
            return symbol;
        }
        return undefined;
    }

    public getType(name: string): TypeSymbol | undefined {
        if (this.types.has(name)) {
            const symbol = this.types.get(name)!;
            symbol.used = true;
            return symbol;
        }
        return undefined;
    }

    public markSymbolUsed(name: string): void {
        // 标记变量为已使用
        if (this.currentFunction) {
            const localScope = this.localScopes.get(this.currentFunction);
            if (localScope && localScope.has(name)) {
                localScope.get(name)!.used = true;
                return;
            }
        }
        
        if (this.globalSymbols.has(name)) {
            this.globalSymbols.get(name)!.used = true;
            return;
        }
        
        if (this.functions.has(name)) {
            this.functions.get(name)!.used = true;
            return;
        }
        
        if (this.types.has(name)) {
            this.types.get(name)!.used = true;
        }
    }

    public getUnusedSymbols(): Symbol[] {
        const unused: Symbol[] = [];
        
        // 检查全局变量
        for (const symbol of this.globalSymbols.values()) {
            if (!symbol.used) {
                unused.push(symbol);
            }
        }
        
        // 检查函数
        for (const symbol of this.functions.values()) {
            if (!symbol.used && !symbol.native) {
                unused.push(symbol);
            }
        }
        
        // 检查局部变量
        for (const localScope of this.localScopes.values()) {
            for (const symbol of localScope.values()) {
                if (!symbol.used && symbol.category !== 'parameter') {
                    unused.push(symbol);
                }
            }
        }
        
        return unused;
    }

    public getAllSymbols(): Symbol[] {
        const allSymbols: Symbol[] = [];
        
        // 添加全局符号
        allSymbols.push(...this.globalSymbols.values());
        allSymbols.push(...this.functions.values());
        allSymbols.push(...this.types.values());
        
        // 添加局部符号
        for (const localScope of this.localScopes.values()) {
            allSymbols.push(...localScope.values());
        }
        
        return allSymbols;
    }

    public getSymbolsInScope(functionName?: string): Symbol[] {
        const symbols: Symbol[] = [];
        
        // 添加全局符号
        symbols.push(...this.globalSymbols.values());
        symbols.push(...this.functions.values());
        symbols.push(...this.types.values());
        
        // 添加指定函数的局部符号
        if (functionName && this.localScopes.has(functionName)) {
            symbols.push(...this.localScopes.get(functionName)!.values());
        }
        
        return symbols;
    }

    public validateSymbolName(name: string): boolean {
        // JASS标识符规则：字母或下划线开头，后跟字母、数字或下划线
        return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
    }

    public isReservedWord(name: string): boolean {
        const reservedWords = [
            'function', 'endfunction', 'takes', 'returns', 'nothing',
            'local', 'set', 'call', 'return', 'if', 'then', 'else',
            'elseif', 'endif', 'loop', 'endloop', 'exitwhen',
            'globals', 'endglobals', 'constant', 'array', 'type',
            'extends', 'native', 'debug', 'and', 'or', 'not',
            'true', 'false', 'null'
        ];
        return reservedWords.includes(name.toLowerCase());
    }

    public getStatistics(): {
        globalVariables: number;
        localVariables: number;
        functions: number;
        types: number;
        totalSymbols: number;
    } {
        let localVariableCount = 0;
        for (const localScope of this.localScopes.values()) {
            localVariableCount += localScope.size;
        }

        return {
            globalVariables: this.globalSymbols.size,
            localVariables: localVariableCount,
            functions: this.functions.size,
            types: this.types.size,
            totalSymbols: this.globalSymbols.size + localVariableCount + this.functions.size + this.types.size
        };
    }
}
