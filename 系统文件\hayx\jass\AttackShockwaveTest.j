//===========================================================================
// 攻击冲击波技能测试文件
// 演示如何使用攻击冲击波系统
//===========================================================================

#include "AttackShockwave.j"

library AttackShockwaveTest initializer TestInit

// 测试初始化
private function TestInit takes nothing returns nothing
    local trigger testTrigger = CreateTrigger()
    local integer i = 0
    
    // 注册聊天命令触发器
    loop
        exitwhen i >= bj_MAX_PLAYER_SLOTS
        call TriggerRegisterPlayerChatEvent(testTrigger, Player(i), "-shockwave", true)
        call TriggerRegisterPlayerChatEvent(testTrigger, Player(i), "-removeshockwave", true)
        call TriggerRegisterPlayerChatEvent(testTrigger, Player(i), "-testshockwave", true)
        set i = i + 1
    endloop
    
    call TriggerAddAction(testTrigger, function TestCommands)
    set testTrigger = null
endfunction

// 测试命令处理
private function TestCommands takes nothing returns nothing
    local string cmd = GetEventPlayerChatString()
    local player p = GetTriggerPlayer()
    local group g = CreateGroup()
    local unit u
    
    if cmd == "-shockwave" then
        // 为选中的单位添加攻击冲击波技能
        call GroupEnumUnitsSelected(g, p, null)
        loop
            set u = FirstOfGroup(g)
            exitwhen u == null
            call GroupRemoveUnit(g, u)
            call AddAttackShockwaveAbility(u)
            call DisplayTextToPlayer(p, 0, 0, "|cff00ff00已为 " + GetUnitName(u) + " 添加攻击冲击波技能！|r")
        endloop
        
    elseif cmd == "-removeshockwave" then
        // 移除选中单位的攻击冲击波技能
        call GroupEnumUnitsSelected(g, p, null)
        loop
            set u = FirstOfGroup(g)
            exitwhen u == null
            call GroupRemoveUnit(g, u)
            call RemoveAttackShockwaveAbility(u)
            call DisplayTextToPlayer(p, 0, 0, "|cffff0000已移除 " + GetUnitName(u) + " 的攻击冲击波技能！|r")
        endloop
        
    elseif cmd == "-testshockwave" then
        // 显示帮助信息
        call DisplayTextToPlayer(p, 0, 0, "|cffFFFF00=== 攻击冲击波技能测试命令 ===|r")
        call DisplayTextToPlayer(p, 0, 0, "|cff00FFFF-shockwave|r - 为选中单位添加攻击冲击波技能")
        call DisplayTextToPlayer(p, 0, 0, "|cff00FFFF-removeshockwave|r - 移除选中单位的攻击冲击波技能")
        call DisplayTextToPlayer(p, 0, 0, "|cffFF8000技能效果：|r")
        call DisplayTextToPlayer(p, 0, 0, "- 攻击时25%概率触发冲击波")
        call DisplayTextToPlayer(p, 0, 0, "- 冲击波造成攻击力1.5倍伤害")
        call DisplayTextToPlayer(p, 0, 0, "- 影响前方60度扇形区域，范围400")
    endif
    
    call DestroyGroup(g)
    set g = null
    set u = null
    set p = null
endfunction

endlibrary
