//===========================================================================
// 最基础的测试 - 确认代码能否执行
//===========================================================================

// 最简单的测试函数
function BasicTest takes nothing returns nothing
    call DisplayTextToPlayer(Player(0), 0, 0, "|cFFFF0000如果你看到这条消息，说明代码可以执行！|r")
    call DisplayTextToPlayer(Player(0), 0, 0, "|cFFFFFF00现在测试DZAPI是否可用...|r")
    
    // 测试DZAPI
    if DzGetGameUI() != 0 then
        call DisplayTextToPlayer(Player(0), 0, 0, "|cFF00FF00DZAPI可用！GameUI句柄: " + I2S(DzGetGameUI()) + "|r")
    else
        call DisplayTextToPlayer(Player(0), 0, 0, "|cFFFF0000DZAPI不可用！这就是问题所在！|r")
    endif
endfunction

//===========================================================================
// 使用方法：
// 1. 创建触发器："基础测试"
// 2. 事件：地图初始化
// 3. 动作：自定义代码 -> call BasicTest()
//
// 如果连这个都没反应，说明：
// 1. 触发器没有正确创建
// 2. 代码复制有问题
// 3. 或者其他基础问题
//===========================================================================
