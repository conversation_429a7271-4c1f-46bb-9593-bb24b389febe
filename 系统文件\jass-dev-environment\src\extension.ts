import * as vscode from 'vscode';
import { JassLanguageServer } from './languageServer';
import { JassSimulator } from './simulator';
import { JassApiProvider } from './apiProvider';

let languageServer: JassLanguageServer;
let simulator: JassSimulator;
let apiProvider: J<PERSON><PERSON><PERSON>Provider;

export function activate(context: vscode.ExtensionContext) {
    console.log('JASS Warcraft 3 Developer 扩展已激活');

    // 初始化语言服务器
    languageServer = new JassLanguageServer();
    simulator = new JassSimulator();
    apiProvider = new JassApiProvider();

    // 注册命令
    registerCommands(context);
    
    // 注册语言功能
    registerLanguageFeatures(context);
    
    // 注册诊断提供者
    registerDiagnostics(context);
    
    // 状态栏
    createStatusBar(context);
}

function registerCommands(context: vscode.ExtensionContext) {
    // 验证代码命令
    const validateCommand = vscode.commands.registerCommand('jass.validateCode', () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) return;
        
        const document = editor.document;
        if (document.languageId !== 'jass') {
            vscode.window.showWarningMessage('请在JASS文件中使用此命令');
            return;
        }
        
        const code = document.getText();
        const result = languageServer.validateCode(code);
        
        if (result.errors.length === 0) {
            vscode.window.showInformationMessage('✅ JASS代码验证通过！');
        } else {
            vscode.window.showErrorMessage(`❌ 发现 ${result.errors.length} 个错误`);
        }
    });

    // 运行模拟命令
    const simulateCommand = vscode.commands.registerCommand('jass.runSimulation', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) return;
        
        const code = editor.document.getText();
        
        // 创建模拟器面板
        const panel = vscode.window.createWebviewPanel(
            'jassSimulator',
            'JASS代码模拟器',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );
        
        panel.webview.html = getSimulatorWebviewContent();
        
        // 运行模拟
        const result = await simulator.simulate(code);
        panel.webview.postMessage({
            command: 'simulationResult',
            result: result
        });
    });

    // API参考命令
    const apiReferenceCommand = vscode.commands.registerCommand('jass.showApiReference', () => {
        const panel = vscode.window.createWebviewPanel(
            'jassApiReference',
            'JASS API参考',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true
            }
        );
        
        panel.webview.html = getApiReferenceWebviewContent();
    });

    context.subscriptions.push(validateCommand, simulateCommand, apiReferenceCommand);
}

function registerLanguageFeatures(context: vscode.ExtensionContext) {
    // 自动补全
    const completionProvider = vscode.languages.registerCompletionItemProvider(
        'jass',
        {
            provideCompletionItems(document, position) {
                return apiProvider.getCompletionItems(document, position);
            }
        },
        '.' // 触发字符
    );

    // 悬停提示
    const hoverProvider = vscode.languages.registerHoverProvider('jass', {
        provideHover(document, position) {
            return apiProvider.getHoverInfo(document, position);
        }
    });

    // 定义跳转
    const definitionProvider = vscode.languages.registerDefinitionProvider('jass', {
        provideDefinition(document, position) {
            return apiProvider.getDefinition(document, position);
        }
    });

    // 符号提供者
    const symbolProvider = vscode.languages.registerDocumentSymbolProvider('jass', {
        provideDocumentSymbols(document) {
            return languageServer.getDocumentSymbols(document);
        }
    });

    context.subscriptions.push(
        completionProvider,
        hoverProvider,
        definitionProvider,
        symbolProvider
    );
}

function registerDiagnostics(context: vscode.ExtensionContext) {
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('jass');
    context.subscriptions.push(diagnosticCollection);

    // 文档变化时进行诊断
    const onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument(event => {
        if (event.document.languageId === 'jass') {
            updateDiagnostics(event.document, diagnosticCollection);
        }
    });

    // 文档打开时进行诊断
    const onDidOpenTextDocument = vscode.workspace.onDidOpenTextDocument(document => {
        if (document.languageId === 'jass') {
            updateDiagnostics(document, diagnosticCollection);
        }
    });

    context.subscriptions.push(onDidChangeTextDocument, onDidOpenTextDocument);
}

function updateDiagnostics(document: vscode.TextDocument, collection: vscode.DiagnosticCollection) {
    const code = document.getText();
    const result = languageServer.validateCode(code);
    
    const diagnostics: vscode.Diagnostic[] = [];
    
    // 添加错误
    result.errors.forEach(error => {
        const range = new vscode.Range(
            error.line - 1, 0,
            error.line - 1, 1000
        );
        const diagnostic = new vscode.Diagnostic(
            range,
            error.message,
            vscode.DiagnosticSeverity.Error
        );
        diagnostics.push(diagnostic);
    });
    
    // 添加警告
    result.warnings.forEach(warning => {
        const range = new vscode.Range(
            warning.line - 1, 0,
            warning.line - 1, 1000
        );
        const diagnostic = new vscode.Diagnostic(
            range,
            warning.message,
            vscode.DiagnosticSeverity.Warning
        );
        diagnostics.push(diagnostic);
    });
    
    collection.set(document.uri, diagnostics);
}

function createStatusBar(context: vscode.ExtensionContext) {
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "$(code) JASS";
    statusBarItem.tooltip = "JASS开发环境已就绪";
    statusBarItem.command = 'jass.showApiReference';
    statusBarItem.show();
    
    context.subscriptions.push(statusBarItem);
}

function getSimulatorWebviewContent(): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>JASS模拟器</title>
        <style>
            body { font-family: 'Segoe UI', sans-serif; margin: 20px; }
            .result { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
            .error { background: #ffebee; border-left: 4px solid #f44336; }
            .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
            .variable { margin: 5px 0; padding: 8px; background: #fff; border: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <h2>🎮 JASS代码模拟器</h2>
        <div id="output">
            <p>等待代码执行...</p>
        </div>
        
        <script>
            window.addEventListener('message', event => {
                const message = event.data;
                if (message.command === 'simulationResult') {
                    displayResult(message.result);
                }
            });
            
            function displayResult(result) {
                const output = document.getElementById('output');
                output.innerHTML = '';
                
                if (result.success) {
                    output.innerHTML = '<div class="result success">✅ 模拟执行成功</div>';
                    
                    if (result.variables) {
                        output.innerHTML += '<h3>变量状态:</h3>';
                        Object.entries(result.variables).forEach(([name, value]) => {
                            output.innerHTML += \`<div class="variable">\${name} = \${value}</div>\`;
                        });
                    }
                } else {
                    output.innerHTML = \`<div class="result error">❌ 模拟执行失败: \${result.error}</div>\`;
                }
            }
        </script>
    </body>
    </html>`;
}

function getApiReferenceWebviewContent(): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>JASS API参考</title>
        <style>
            body { font-family: 'Segoe UI', sans-serif; margin: 20px; }
            .api-section { margin: 20px 0; }
            .api-function { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; }
            .function-name { font-weight: bold; color: #0066cc; }
            .function-desc { color: #666; margin-top: 5px; }
            .search-box { width: 100%; padding: 10px; margin-bottom: 20px; border: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <h2>📚 JASS API参考手册</h2>
        <input type="text" class="search-box" placeholder="搜索API函数..." onkeyup="searchAPI(this.value)">
        
        <div class="api-section">
            <h3>🔧 YDWE API</h3>
            <div class="api-function">
                <div class="function-name">YDLocal1Set(type, key, value)</div>
                <div class="function-desc">设置局部变量值，用于避免内存泄漏</div>
            </div>
            <div class="api-function">
                <div class="function-name">YDUserDataSet(player, key, type, value)</div>
                <div class="function-desc">设置玩家自定义数据</div>
            </div>
        </div>
        
        <div class="api-section">
            <h3>🌐 DZAPI</h3>
            <div class="api-function">
                <div class="function-name">DzFrameSetPoint(frame, point, relativeFrame, relativePoint, x, y)</div>
                <div class="function-desc">设置UI框架位置</div>
            </div>
        </div>
        
        <script>
            function searchAPI(query) {
                const functions = document.querySelectorAll('.api-function');
                functions.forEach(func => {
                    const text = func.textContent.toLowerCase();
                    func.style.display = text.includes(query.toLowerCase()) ? 'block' : 'none';
                });
            }
        </script>
    </body>
    </html>`;
}

export function deactivate() {
    console.log('JASS扩展已停用');
}
