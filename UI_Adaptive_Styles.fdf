// UI自适应系统样式文件

// 自适应容器
Frame "BACKDROP" "AdaptiveContainer" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.012,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.006 0.006 0.006 0.006,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-panel-border.blp",
}

// 顶部面板
Frame "BACKDROP" "TopPanel" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.008,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-panel-border.blp",
}

// 底部面板
Frame "BACKDROP" "BottomPanel" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.012,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.006 0.006 0.006 0.006,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-panel-border.blp",
}

// 小地图框架
Frame "BACKDROP" "MiniMapFrame" {
    BackdropTileBackground,
    BackdropBackground  "UI\\Widgets\\EscMenu\\Human\\human-panel-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize  0.008,
    BackdropBackgroundSize  0.256,
    BackdropBackgroundInsets 0.004 0.004 0.004 0.004,
    BackdropEdgeFile  "UI\\Widgets\\EscMenu\\Human\\human-panel-border.blp",
}

// 文本样式
Frame "TEXT" "ResourceText" {
    FontJustificationH JUSTIFYLEFT,
    FontJustificationV JUSTIFYMIDDLE,
    FontFlags "FIXEDSIZE",
    FontColor 1.0 1.0 1.0 1.0,
    FontShadowColor 0.0 0.0 0.0 1.0,
    FontShadowOffset 0.001 -0.001,
}

Frame "TEXT" "MapTitle" {
    FontJustificationH JUSTIFYCENTER,
    FontJustificationV JUSTIFYMIDDLE,
    FontFlags "FIXEDSIZE",
    FontColor 1.0 0.843 0.0 1.0,
    FontShadowColor 0.0 0.0 0.0 1.0,
    FontShadowOffset 0.001 -0.001,
}
