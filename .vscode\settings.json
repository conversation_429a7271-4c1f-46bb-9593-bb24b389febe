{
    "files.associations": {
        "*.j": "jass",
        "*.jass": "jass"
    },
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.wordWrap": "on",
    "editor.fontSize": 14,
    "editor.fontFamily": "'Consolas', 'Courier New', monospace",
    "editor.rulers": [80, 120],
    "editor.minimap.enabled": true,
    "editor.bracketPairColorization.enabled": true,
    "editor.guides.bracketPairs": true,
    
    // JASS特定设置
    "[jass]": {
        "editor.defaultFormatter": null,
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.autoIndent": "full",
        "editor.suggest.showKeywords": true,
        "editor.suggest.showSnippets": true
    },
    
    // 文件监控
    "files.watcherExclude": {
        "**/node_modules/**": true,
        "**/.git/**": true,
        "**/out/**": true
    },
    
    // 搜索设置
    "search.exclude": {
        "**/node_modules": true,
        "**/out": true,
        "**/*.vsix": true
    },
    
    // 终端设置
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.fontSize": 12,
    
    // 主题和外观
    "workbench.colorTheme": "Default Dark Modern",
    "workbench.iconTheme": "vs-seti",
    
    // 自动保存
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    
    // 错误检查
    "problems.decorations.enabled": true,
    "problems.showCurrentInStatus": true
}
