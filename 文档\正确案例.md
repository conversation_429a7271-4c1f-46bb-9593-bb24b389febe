# JASS正确案例库

## 🎯 基础触发器结构

### ✅ 完整的触发器模板（已验证）
```jass
globals
    trigger gg_trg_Test = null
endglobals

function Trig_TestActions takes nothing returns nothing
    call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
endfunction

function InitTrig_Test takes nothing returns nothing
    set gg_trg_Test = CreateTrigger()
    call TriggerRegisterTimerEventSingle(gg_trg_Test, 5.0)
    call TriggerAddAction(gg_trg_Test, function Trig_TestActions)
endfunction

function main takes nothing returns nothing
    call InitTrig_Test()  // 关键：必须调用
endfunction
```

**验证状态**：✅ 用户确认工作正常  
**适用版本**：Warcraft 3 1.27  
**关键要点**：InitTrig_*函数必须在main中手动调用

## 🏆 高级示例

### ✅ 山丘之王创建（YDWE风格）
```jass
function Trig_CreateMountainKingActions takes nothing returns nothing
    local integer ydul_i
    YDLocalInitialize()

    // 创建山丘之王
    call YDLocal1Set(unit, "dw", CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 270.0))
    set udg_MountainKing = YDLocal1Get(unit, "dw")

    // 设置英雄等级
    call SetHeroLevel(YDLocal1Get(unit, "dw"), 10, true)

    // 学习技能
    call SelectHeroSkill(YDLocal1Get(unit, "dw"), 'AHav')

    // 施放技能
    call IssueImmediateOrder(YDLocal1Get(unit, "dw"), "avatar")

    call YDLocal1Release()
endfunction
```

**特点**：使用YDWE本地变量系统  
**最佳实践**：YDLocalInitialize/Release配对使用

## 📚 API使用规范

### ✅ CreateUnit正确格式
```jass
// 正确：5个参数，坐标使用real类型
call CreateUnit(Player(0), 'Hmkg', 0.0, 0.0, 0.0)
```

### ✅ 1.27兼容函数列表
```jass
// 单位操作
CreateUnit, KillUnit, RemoveUnit
SetUnitX, SetUnitY, SetUnitPosition
SetUnitFacing, SetUnitScale

// 触发器操作
CreateTrigger, TriggerRegisterTimerEventSingle
TriggerAddAction, EnableTrigger, DisableTrigger

// 英雄操作
SetHeroLevel, SelectHeroSkill
IssueImmediateOrder, IssueTargetOrder
```

## 🎨 代码风格规范

### ✅ 推荐命名规范
```jass
// 全局变量
trigger gg_trg_TriggerName = null

// 动作函数
function Trig_TriggerNameActions takes nothing returns nothing

// 初始化函数
function InitTrig_TriggerName takes nothing returns nothing
```

### ✅ YDWE本地变量使用
```jass
function ExampleFunction takes nothing returns nothing
    local integer ydul_i
    YDLocalInitialize()
    
    call YDLocal1Set(unit, "dw", GetTriggerUnit())
    // 使用本地变量...
    
    call YDLocal1Release()
endfunction
```

### 🔥 火球术 - 弧形投射物（2025-07-21新增）
**文件**：`fireball_skill.j`
**特点**：贝塞尔曲线弧形轨迹 + 范围爆炸

```jass
function Trig_FireballTimer takes nothing returns nothing
    local timer ydl_timer = GetExpiredTimer()
    local real t = YDLocalGet(ydl_timer, real, "t")
    local real currentX
    local real currentY

    // 增加时间参数
    set t = t + 0.03
    call YDLocalSet(ydl_timer, real, "t", t)

    // 贝塞尔曲线计算当前位置
    set currentX = (1-t)*(1-t)*startX + 2*(1-t)*t*controlX + t*t*endX
    set currentY = (1-t)*(1-t)*startY + 2*(1-t)*t*controlY + t*t*endY

    // 移动火球特效
    call EXSetEffectXY(projectile, currentX, currentY)
    call EXSetEffectZ(projectile, 50.0 + t * 100.0)

    // 检查是否到达目标附近50码
    if SquareRoot((currentX-endX)*(currentX-endX) + (currentY-endY)*(currentY-endY)) <= 50.0 then
        // 创建爆炸特效和范围伤害
        call YDWETimerDestroyEffect(2.0, AddSpecialEffect("Abilities\\Spells\\Other\\Incinerate\\FireLordDeathExplode.mdl", currentX, currentY))
        // 销毁投射物和定时器
        call DestroyEffect(projectile)
        call YDLocal3Release()
        call DestroyTimer(ydl_timer)
        return
    endif
endfunction
```

**技术亮点**：
- ✅ 贝塞尔曲线实现弧形轨迹
- ✅ YDLocal系统管理定时器数据
- ✅ 高度变化模拟抛物线
- ✅ 精确的碰撞检测（50码范围）
- ✅ 完整的资源清理

---
**更新日期**：2025-07-21
**验证环境**：Warcraft 3 1.27 + YDWE + JAPI
