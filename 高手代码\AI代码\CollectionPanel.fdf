Frame "BACKDROP" "CollectionPanel" {
    Width 0.50,
    Height 0.40,
    DecorateFileNames,
    BackdropTileBackground,
    BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize 0.0125,
    BackdropBackgroundSize 0.02,
    BackdropBackgroundInsets 0.005 0.005 0.005 0.005,
    BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
}

Frame "GLUEBUTTON" "HeroButton" {
    Width 0.10,
    Height 0.08,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
    SetPoint TOPLEFT, "CollectionPanel", TOPLEFT, 0.02, -0.02,
    ControlBackdrop "HeroButtonBackdrop",
    Frame "BACKDROP" "HeroButtonBackdrop" {
        Width 0.10,
        Height 0.08,
        DecorateFileNames,
        BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.008,
        BackdropBackgroundInsets 0.005 0.005 0.005 0.005,
        BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
    }
    Frame "BACKDROP" "HeroButtonIcon" {
        Width 0.06,
        Height 0.06,
        SetPoint CENTER, "HeroButton", CENTER, 0.0, 0.0,
        BackdropBackground "ReplaceableTextures\CommandButtons\BTNArthas.blp",
    }
    Frame "TEXT" "HeroButtonText" {
        Width 0.08,
        Height 0.02,
        SetPoint CENTER, "HeroButton", CENTER, 0.0, -0.03,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "英雄",
        FontJustificationH JUSTIFYCENTER,
    }
    Tooltip "HeroButtonTooltip",
    Frame "TEXT" "HeroButtonTooltip" {
        Width 0.20,
        Height 0.04,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "查看你的英雄收藏",
    }
    ControlMouseOverHighlight "HeroButtonHighlight",
    Frame "HIGHLIGHT" "HeroButtonHighlight" {
        HighlightType "FILETEXTURE",
        HighlightAlphaFile "UI\Glues\ScoreScreen\scorescreen-tab-hilight.blp",
        HighlightAlphaMode "ADD",
    }
}

Frame "GLUEBUTTON" "AchievementButton" {
    Width 0.10,
    Height 0.08,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
    SetPoint TOPLEFT, "HeroButton", BOTTOMLEFT, 0.0, -0.02,
    ControlBackdrop "AchievementButtonBackdrop",
    Frame "BACKDROP" "AchievementButtonBackdrop" {
        Width 0.10,
        Height 0.08,
        DecorateFileNames,
        BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.008,
        BackdropBackgroundInsets 0.005 0.005 0.005 0.005,
        BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
    }
    Frame "BACKDROP" "AchievementButtonIcon" {
        Width 0.06,
        Height 0.06,
        SetPoint CENTER, "AchievementButton", CENTER, 0.0, 0.0,
        BackdropBackground "ReplaceableTextures\CommandButtons\BTNPeasant.blp",
    }
    Frame "TEXT" "AchievementButtonText" {
        Width 0.08,
        Height 0.02,
        SetPoint CENTER, "AchievementButton", CENTER, 0.0, -0.03,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "成就",
        FontJustificationH JUSTIFYCENTER,
    }
    Tooltip "AchievementButtonTooltip",
    Frame "TEXT" "AchievementButtonTooltip" {
        Width 0.20,
        Height 0.04,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "查看你的成就进度",
    }
    ControlMouseOverHighlight "AchievementButtonHighlight",
    Frame "HIGHLIGHT" "AchievementButtonHighlight" {
        HighlightType "FILETEXTURE",
        HighlightAlphaFile "UI\Glues\ScoreScreen\scorescreen-tab-hilight.blp",
        HighlightAlphaMode "ADD",
    }
}

Frame "GLUEBUTTON" "SkinButton" {
    Width 0.10,
    Height 0.08,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
    SetPoint TOPLEFT, "AchievementButton", BOTTOMLEFT, 0.0, -0.02,
    ControlBackdrop "SkinButtonBackdrop",
    Frame "BACKDROP" "SkinButtonBackdrop" {
        Width 0.10,
        Height 0.08,
        DecorateFileNames,
        BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.008,
        BackdropBackgroundInsets 0.005 0.005 0.005 0.005,
        BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
    }
    Frame "BACKDROP" "SkinButtonIcon" {
        Width 0.06,
        Height 0.06,
        SetPoint CENTER, "SkinButton", CENTER, 0.0, 0.0,
        BackdropBackground "ReplaceableTextures\CommandButtons\BTNPeasant.blp",
    }
    Frame "TEXT" "SkinButtonText" {
        Width 0.08,
        Height 0.02,
        SetPoint CENTER, "SkinButton", CENTER, 0.0, -0.03,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "皮肤",
        FontJustificationH JUSTIFYCENTER,
    }
    Tooltip "SkinButtonTooltip",
    Frame "TEXT" "SkinButtonTooltip" {
        Width 0.20,
        Height 0.04,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "浏览你的皮肤收藏",
    }
    ControlMouseOverHighlight "SkinButtonHighlight",
    Frame "HIGHLIGHT" "SkinButtonHighlight" {
        HighlightType "FILETEXTURE",
        HighlightAlphaFile "UI\Glues\ScoreScreen\scorescreen-tab-hilight.blp",
        HighlightAlphaMode "ADD",
    }
}

Frame "GLUEBUTTON" "SaveButton" {
    Width 0.10,
    Height 0.08,
    ControlStyle "AUTOTRACK|HIGHLIGHTONMOUSEOVER",
    SetPoint TOPLEFT, "SkinButton", BOTTOMLEFT, 0.0, -0.02,
    ControlBackdrop "SaveButtonBackdrop",
    Frame "BACKDROP" "SaveButtonBackdrop" {
        Width 0.10,
        Height 0.08,
        DecorateFileNames,
        BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
        BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
        BackdropCornerSize 0.008,
        BackdropBackgroundInsets 0.005 0.005 0.005 0.005,
        BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
    }
    Frame "BACKDROP" "SaveButtonIcon" {
        Width 0.06,
        Height 0.06,
        SetPoint CENTER, "SaveButton", CENTER, 0.0, 0.0,
        BackdropBackground "ReplaceableTextures\CommandButtons\BTNPeasant.blp",
    }
    Frame "TEXT" "SaveButtonText" {
        Width 0.08,
        Height 0.02,
        SetPoint CENTER, "SaveButton", CENTER, 0.0, -0.03,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "存档",
        FontJustificationH JUSTIFYCENTER,
    }
    Tooltip "SaveButtonTooltip",
    Frame "TEXT" "SaveButtonTooltip" {
        Width 0.20,
        Height 0.04,
        Font "Fonts\FRIZQT__.ttf", 0.012, "",
        FrameText "管理你的游戏存档",
    }
    ControlMouseOverHighlight "SaveButtonHighlight",
    Frame "HIGHLIGHT" "SaveButtonHighlight" {
        HighlightType "FILETEXTURE",
        HighlightAlphaFile "UI\Glues\ScoreScreen\scorescreen-tab-hilight.blp",
        HighlightAlphaMode "ADD",
    }
}

Frame "BACKDROP" "ContentPanel" {
    Width 0.38,
    Height 0.36,
    SetPoint TOPLEFT, "CollectionPanel", TOPLEFT, 0.14, -0.02,
    DecorateFileNames,
    BackdropBackground "UI\Widgets\EscMenu\Undead\undead-options-menu-background.blp",
    BackdropCornerFlags "UL|UR|BL|BR|T|L|B|R",
    BackdropCornerSize 0.008,
    BackdropBackgroundInsets 0.005 0.005 0.005 0.005,
    BackdropEdgeFile "UI\Widgets\EscMenu\Undead\undead-tooltip-border.blp",
}

Frame "TEXT" "ContentText" {
    Width 0.36,
    Height 0.34,
    SetPoint CENTER, "ContentPanel", CENTER, 0.0, 0.0,
    Font "Fonts\FRIZQT__.ttf", 0.014, "",
    FrameText "选择左侧按钮查看内容",
    FontJustificationH JUSTIFYCENTER,
}