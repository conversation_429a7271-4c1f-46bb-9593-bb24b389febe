[FontHeights]
_parent = "FontHeights"
-- 字体大小 - 聊天输入栏文字大小
ChatEditBar = 0.011
-- 字体大小 - 物品栏标题
Inventory = 0.01
-- 字体大小 - 生命魔法值
PortraitStats = 0.01
-- 字体大小 - 计分屏 - 大字体
ScoreScreenLarge = 0.01
-- 字体大小 - 计分屏 - 普通字体
ScoreScreenNormal = 0.01
-- 字体大小 - 工具栏 - 消费数值
ToolTipCost = 0.01
-- 字体大小 - 工具栏 - 说明
ToolTipDesc = 0.01
-- 字体大小 - 工具栏 - 标题
ToolTipName = 0.01
-- 字体大小 - 物体悬浮窗 - 说明
UnitTipDesc = 0.01
-- 字体大小 - 物体悬浮窗 - 名字
UnitTipPlayerName = 0.01
-- 字体大小 - 聊天文本显示
WorldFrameChatMessage = 0.011
-- 字体大小 - 游戏信息提示
WorldFrameMessage = 0.012
-- 字体大小 - 维修费提示
WorldFrameTopMessage = 0.0001
-- 字体大小 - 触发文本显示
WorldFrameUnitMessage = 0.012

[Misc]
_parent = "Misc"
-- 英雄属性 - 每点敏捷攻击速度奖励
AgiAttackSpeedBonus = 0.0
-- 英雄属性 -基础防御补正
AgiDefenseBase = 0.0
-- 英雄属性 - 每点敏捷防御奖励
AgiDefenseBonus = 0.0
-- 战斗 -攻击通知 - 最小间隔(秒)
AttackNotifyDelay = 100000.0
-- 战斗 -攻击通知 - 最小范围
AttackNotifyRange = 0.0
-- 衰退时间(秒)- 骨态尸体
BoneDecayTime = 0.5
-- 战斗 -救援范围
CallForHelp = 100000.0
-- 战斗 - 失误几率
ChanceToMiss = 0.0
-- 战斗 -救援范围(中立)
CreepCallForHelp = 10.0
-- 漂浮文字 - 致命一击 - 文字颜色
CriticalStrikeTextColor = "0,0,0,0"
-- 漂浮文字 - 致命一击 -消逝时间点
CriticalStrikeTextFadeStart = 0.0
-- 漂浮文字 - 致命一击 - 字体大小
CriticalStrikeTextHeight = 0.0
-- 漂浮文字 - 致命一击 -持续时间
CriticalStrikeTextLifetime = 0.0
-- 漂浮文字 - 致命一击 - 移动速度
CriticalStrikeTextVelocity = "0,0.00"
-- 战斗 - 伤害奖励列表 - 混乱
DamageBonusChaos = "1.00,1.00,1.00,0.75,1.00,1.00,0.70,1.00"
-- 战斗 - 伤害奖励列表 - 英雄
DamageBonusHero = "1.00,1.00,1.00,0.75,1.00,1.00,0.70,1.00"
-- 战斗 - 伤害奖励列表 - 魔法
DamageBonusMagic = "1.00,1.00,1.00,0.75,1.00,1.00,0.70,1.00"
-- 战斗 - 伤害奖励列表 - 普通
DamageBonusNormal = "1.00,1.00,1.00,0.75,1.00,1.00,0.70,1.00"
-- 战斗 - 伤害奖励列表 - 穿刺
DamageBonusPierce = "1.00,1.00,1.00,0.75,1.00,1.00,0.70,1.00"
-- 战斗 - 伤害奖励列表 - 攻城
DamageBonusSiege = "1.00,1.00,1.00,1.00,1.00,1.00,0.70,1.00"
-- 战斗 - 伤害奖励列表 - 法术
DamageBonusSpells = "1.00,1.00,1.00,0.75,1.00,1.00,0.70,1.00"
-- 衰退时间(秒)- 肉态尸体
DecayTime = 0.5
-- 战斗 - 护甲减少伤害因子
DefenseArmor = 0.03
-- 物品 - 丢弃物品范围
DropItemRange = 99999.0
-- 时间 -黄昏
Dusk = 24.0
-- 魔法 - 虚无状态医疗加成
EtherealHealBonus = 2.0
-- 魔法 - 冰冻攻击速度降低
FrostAttackSpeedDecrease = 1.0
-- 魔法 - 冰冻移动速度降低
FrostMoveSpeedDecrease = 0.65
-- 英雄EXP获取 - 英雄 - 列表
GrantHeroXP = "0"
-- 英雄EXP获取 - 英雄 - 上一个值因数
GrantHeroXPFormulaA = 0.0
-- 英雄EXP获取 - 英雄 -固定因数
GrantHeroXPFormulaC = 0.0
-- 英雄EXP获取 - 普通 - 上一个值因数
GrantNormalXPFormulaA = 0.0
-- 英雄EXP获取 - 普通 - 等级因数
GrantNormalXPFormulaB = 0.0
-- 英雄EXP获取 - 普通 -固定因数
GrantNormalXPFormulaC = 0.0
-- 中立生物 - 警戒范围
GuardDistance = 100000.0
-- 中立生物 - 警戒返回时间(秒)
GuardReturnTime = 10000.0
-- 英雄 - 默认技能跳级
HeroAbilityLevelSkip = 5
-- 英雄EXP - 经验取得范围
HeroExpRange = 100000.0
-- 英雄EXP获取 - 中立生物经验参数表
HeroFactorXP = "0"
-- 游戏性 - 镜像有攻击加成
IllusionsGetAttackBonus = 1
-- 游戏性 - 镜像有攻击速度加成
IllusionsGetAttackSpeedBonus = 1
-- 游戏性 - 镜像有移动速度加成
IllusionsGetMoveSpeedBonus = 1
-- 英雄属性 - 每点智力魔法值奖励
IntManaBonus = 0.0
-- 英雄属性 - 每点智力魔法回复奖励
IntRegenBonus = 0.0
-- 中立生物 - 警戒返回距离
MaxGuardDistance = 100000.0
-- 英雄 - 最大等级
MaxHeroLevel = 100
-- 英雄EXP - 最高等级英雄消耗经验值
MaxLevelHeroesDrainExp = 0
-- 单位最大等级
MaxUnitLevel = 100
-- 移动 - 单位最大速度
MaxUnitSpeed = 522.0
-- 移动 - 单位最小速度
MinUnitSpeed = 1.0
-- 英雄EXP需求 - 列表
NeedHeroXP = "240"
-- 英雄EXP需求 - 等级因数
NeedHeroXPFormulaB = 10.0
-- 英雄EXP需求 -固定因数
NeedHeroXPFormulaC = 240.0
-- 物品 - 贩卖物品范围
PawnItemRange = 99999.0
-- 物品 - 拾取物品范围
PickupItemRange = 600.0
-- 攻击反应延迟
ReactionDelay = 0.1
-- 英雄属性 - 每点主属性攻击奖励
StrAttackBonus = 0.0
-- 英雄属性 - 每点力量生命值奖励
StrHitPointBonus = 0.0
-- 英雄属性 - 每点力量生命回复奖励
StrRegenBonus = 0.0
-- 英雄EXP获取 - 召唤单位经验参数
SummonedKillFactor = 0.0
-- 资源交易量 -Ctrl点击
TradingIncLarge = 1000
-- 维修费 - 人口设置
UpkeepUsage = "100000,100000"
