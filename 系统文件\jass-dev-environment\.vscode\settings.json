{"workbench.colorTheme": "Default Dark+", "editor.tokenColorCustomizations": {"textMateRules": [{"name": "JASS基础代码", "scope": ["source.jass", "text.jass"], "settings": {"foreground": "#4A90E2"}}, {"name": "JASS关键字", "scope": ["keyword.control.jass", "keyword.other.jass"], "settings": {"foreground": "#5BA7F7", "fontStyle": "bold"}}, {"name": "JASS函数名", "scope": ["entity.name.function.jass", "entity.name.function.definition.jass"], "settings": {"foreground": "#6BB6FF"}}, {"name": "JASS类型", "scope": "storage.type.jass", "settings": {"foreground": "#4A90E2", "fontStyle": "italic"}}, {"name": "JASS字符串", "scope": "string.quoted.double.jass", "settings": {"foreground": "#87CEEB"}}, {"name": "JASS数字", "scope": ["constant.numeric.jass", "constant.numeric.hex.jass"], "settings": {"foreground": "#87CEEB"}}, {"name": "JASS常量", "scope": "constant.language.jass", "settings": {"foreground": "#87CEEB", "fontStyle": "bold"}}, {"name": "JASS注释", "scope": "comment.line.double-slash.jass", "settings": {"foreground": "#6A9955", "fontStyle": "italic"}}, {"name": "JASS操作符", "scope": "keyword.operator.jass", "settings": {"foreground": "#4A90E2"}}, {"name": "YDWE API", "scope": "support.function.ydwe.jass", "settings": {"foreground": "#9CDCFE", "fontStyle": "bold"}}, {"name": "DZ API", "scope": "support.function.dzapi.jass", "settings": {"foreground": "#9CDCFE", "fontStyle": "bold"}}]}, "workbench.colorCustomizations": {"editorError.foreground": "#FF0000", "editorError.border": "#FF0000", "editorWarning.foreground": "#FFA500", "editorInfo.foreground": "#00BFFF", "problemsErrorIcon.foreground": "#FF0000", "problemsWarningIcon.foreground": "#FFA500", "problemsInfoIcon.foreground": "#00BFFF"}, "editor.semanticHighlighting.enabled": true, "editor.bracketPairColorization.enabled": true, "files.associations": {"*.j": "jass"}, "editor.quickSuggestions": {"other": true, "comments": false, "strings": false}, "editor.suggest.showKeywords": true, "editor.suggest.showSnippets": true, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false}